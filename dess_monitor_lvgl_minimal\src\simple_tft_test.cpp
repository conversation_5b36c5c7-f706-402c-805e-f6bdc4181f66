#include <TFT_eSPI.h>

// Create TFT instance
TFT_eSPI tft = TFT_eSPI();

void setup() {
  Serial.begin(115200);
  Serial.println("=== ESP32-2432S028R Simple TFT Test ===");
  
  // Initialize backlight
  pinMode(21, OUTPUT);
  digitalWrite(21, HIGH);
  Serial.println("✅ Backlight ON");
  
  // Initialize TFT
  tft.init();
  Serial.println("✅ TFT initialized");
  
  // Test different rotations
  for (int rotation = 0; rotation < 4; rotation++) {
    Serial.printf("🔄 Testing rotation %d\n", rotation);
    
    tft.setRotation(rotation);
    tft.fillScreen(TFT_BLACK);
    
    // Get screen dimensions
    int w = tft.width();
    int h = tft.height();
    Serial.printf("📐 Screen size: %dx%d\n", w, h);
    
    // Draw test pattern
    tft.fillRect(0, 0, w/4, h/4, TFT_RED);           // Top-left: Red
    tft.fillRect(w*3/4, 0, w/4, h/4, TFT_GREEN);     // Top-right: Green
    tft.fillRect(0, h*3/4, w/4, h/4, TFT_BLUE);      // Bottom-left: Blue
    tft.fillRect(w*3/4, h*3/4, w/4, h/4, TFT_WHITE); // Bottom-right: White
    
    // Draw border
    tft.drawRect(0, 0, w, h, TFT_YELLOW);
    
    // Draw center cross
    tft.drawLine(w/2, 0, w/2, h, TFT_CYAN);
    tft.drawLine(0, h/2, w, h/2, TFT_CYAN);
    
    // Draw text
    tft.setTextColor(TFT_WHITE, TFT_BLACK);
    tft.setTextSize(2);
    tft.setCursor(w/2 - 60, h/2 - 10);
    tft.printf("ROT:%d", rotation);
    tft.setCursor(w/2 - 60, h/2 + 20);
    tft.printf("%dx%d", w, h);
    
    delay(3000); // Show for 3 seconds
  }
  
  Serial.println("🎯 Test complete! Check which rotation looks correct.");
}

void loop() {
  // Blink to show system is running
  static unsigned long lastBlink = 0;
  static bool ledState = false;
  
  if (millis() - lastBlink > 1000) {
    ledState = !ledState;
    digitalWrite(21, ledState ? HIGH : LOW);
    lastBlink = millis();
    Serial.println(ledState ? "💡 Blink ON" : "💡 Blink OFF");
  }
}
