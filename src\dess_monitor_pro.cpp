#include <Arduino.h>
#include <WiFi.h>
#include <WiFiManager.h>
#include <HTTPClient.h>
#include <ArduinoJson.h>
#include <TFT_eSPI.h>
#include <XPT2046_Touchscreen.h>
#include <SPI.h>

// Pin definitions
#define TFT_BL 21
#define BOOT_BUTTON 0  // GPIO0 is the BOOT button on ESP32
#define LDR_PIN 34     // Light sensor for auto brightness

// Touch pins for ESP32-2432S028R (correct pinout)
#define XPT2046_IRQ 36   // T_IRQ
#define XPT2046_MOSI 32  // T_DIN
#define XPT2046_MISO 39  // T_OUT
#define XPT2046_CLK 25   // T_CLK
#define XPT2046_CS 33    // T_CS

// TFT Display
TFT_eSPI tft = TFT_eSPI();

// Touch Screen with proper SPI setup
SPIClass touchscreenSPI = SPIClass(VSPI);
XPT2046_Touchscreen ts(XPT2046_CS, XPT2046_IRQ);

// DESS Monitor API URL - ใส่ URL ของคุณที่นี่
const char* apiURL = "https://web.dessmonitor.com/public/?sign=8743221c28ad40664baa48193bbf4b03caa726f1&salt=1748162984217&token=c8075906465cca180724b25d151680e31e2267ff1319ccf2b43c88ba979a7576&action=querySPDeviceLastData&source=1&devcode=2376&pn=Q0046526082082&devaddr=1&sn=Q0046526082082094801&i18n=en_US";

// Data refresh interval
const unsigned long REFRESH_INTERVAL = 30000; // 10 seconds for real-time
// DISPLAY_UPDATE_INTERVAL removed - now updates every frame for maximum FPS

// Advanced Anti-flicker configuration
bool enableAntiFlicker = true;
bool partialUpdate = true;
bool useDoubleBuffer = false; // Disabled for ESP32 memory constraints
unsigned long lastFullRedraw = 0;

// Touch configuration
#define TOUCH_THRESHOLD 600
#define TOUCH_DEBOUNCE 50
#define SWIPE_THRESHOLD 50
#define SWIPE_TIMEOUT 500

// Display regions for partial updates
struct DisplayRegion {
  int16_t x, y, w, h;
  bool dirty;
  unsigned long lastUpdate;
};

DisplayRegion cardRegions[4] = {
  {4, 22, 154, 105, true, 0},    // Solar card
  {162, 22, 154, 105, true, 0},  // Grid card
  {4, 131, 154, 105, true, 0},   // Battery card
  {162, 131, 154, 105, true, 0}  // Load card
};

// Smart redraw tracking
struct SmartRedraw {
  float lastPvPower;
  float lastPvVoltage;
  float lastBatteryVoltage;
  float lastGridVoltage;
  float lastGridFrequency;
  float lastOutputPower;
  float lastLoadPercent;
  String lastOperatingMode;
  String lastTimestamp;
  bool lastDataValid;
  unsigned long lastHeaderUpdate;
  bool forceNextRedraw;
  bool initialized;
} smartRedraw = {-999, -999, -999, -999, -999, -999, -999, "", "", false, 0, true, false};

// Timing variables
unsigned long lastDataUpdate = 0;
unsigned long lastDisplayUpdate = 0;
unsigned long lastAnimationUpdate = 0;

// Theme System - 2 Modern Themes
enum ThemeType {
  THEME_DARK = 0,     // Dark Modern Theme (Default)
  THEME_LIGHT = 1     // Light Modern Theme
};

struct ColorTheme {
  uint16_t bg;
  uint16_t header;
  uint16_t cardBg;
  uint16_t cardBorder;
  uint16_t textPrimary;
  uint16_t textSecondary;
  uint16_t textDim;
  uint16_t accent;
  uint16_t solar;
  uint16_t solarGlow;
  uint16_t batteryGood;
  uint16_t batteryMid;
  uint16_t batteryLow;
  uint16_t grid;
  uint16_t gridGlow;
  uint16_t output;
  uint16_t outputGlow;
  uint16_t success;
  uint16_t warning;
  uint16_t error;
  uint16_t glow;
  uint16_t shadow;
  uint16_t highlight;
  const char* name;
  const char* icon;
};

// Theme definitions - Modern Dark & Light
ColorTheme themes[2] = {
  // THEME_DARK - Modern Dark Theme (Default) - Black Background
  {
    0x0000, 0x0000, 0x0000, 0x2104, 0xFFFF, 0xC618, 0x8410, 0x07E0,
    0xFD20, 0xFEA0, 0x07E0, 0xFFE0, 0xF800, 0x07FF, 0x05DF, 0xF81F, 0xE79F,
    0x07E0, 0xFFE0, 0xF800, 0x2104, 0x1082, 0x8C71,
    "Dark Mode", "🌙"
  },
  // THEME_LIGHT - Modern Light Theme
  {
    0xFFFF, 0xE71C, 0xD69A, 0xBDF7, 0x0000, 0x4208, 0x8410, 0x001F,
    0xFD20, 0xFEA0, 0x07E0, 0xFFE0, 0xF800, 0x07FF, 0x05DF, 0xF81F, 0xE79F,
    0x07E0, 0xFFE0, 0xF800, 0xBDF7, 0xE71C, 0x8C71,
    "Light Mode", "☀"
  }
};

// Current theme
int currentTheme = 0;
ColorTheme* theme = &themes[currentTheme];

// Dynamic color macros
#define COLOR_BG           theme->bg
#define COLOR_HEADER       theme->header
#define COLOR_CARD_BG      theme->cardBg
#define COLOR_CARD_BORDER  theme->cardBorder
#define COLOR_TEXT_PRIMARY theme->textPrimary
#define COLOR_TEXT_SECONDARY theme->textSecondary
#define COLOR_TEXT_DIM     theme->textDim
#define COLOR_ACCENT       theme->accent
#define COLOR_SOLAR        theme->solar
#define COLOR_SOLAR_GLOW   theme->solarGlow
#define COLOR_BATTERY_GOOD theme->batteryGood
#define COLOR_BATTERY_MID  theme->batteryMid
#define COLOR_BATTERY_LOW  theme->batteryLow
#define COLOR_GRID         theme->grid
#define COLOR_GRID_GLOW    theme->gridGlow
#define COLOR_OUTPUT       theme->output
#define COLOR_OUTPUT_GLOW  theme->outputGlow
#define COLOR_SUCCESS      theme->success
#define COLOR_WARNING      theme->warning
#define COLOR_ERROR        theme->error
#define COLOR_GLOW_EFFECT  theme->glow
#define COLOR_SHADOW       theme->shadow
#define COLOR_HIGHLIGHT    theme->highlight

// Additional theme colors
#define COLOR_BATTERY_FULL COLOR_BATTERY_GOOD
#define COLOR_BATTERY_CRITICAL COLOR_ERROR
#define COLOR_SOLAR_DARK   COLOR_SOLAR
#define COLOR_INFO         COLOR_GRID  // Use grid color for info items

// Data structure
struct InverterData {
  float gridVoltage = 0;
  float gridFrequency = 0;
  float gridPower = 0;
  float pvVoltage = 0;
  float pvCurrent = 0;
  float pvPower = 0;
  float batteryVoltage = 0;
  float batteryCurrent = 0;
  float outputVoltage = 0;
  float outputCurrent = 0;
  float outputPower = 0;
  float outputApparentPower = 0;
  float outputFrequency = 0;
  float loadPercent = 0;
  int dcTemp = 0;
  int invTemp = 0;
  String operatingMode = "";
  String timestamp = "";
  bool dataValid = false;
  unsigned long lastUpdate = 0;
  int signalStrength = 0;
};

InverterData data;
bool wifiConnected = false;
int animationFrame = 0;

// WiFi timeout reset system
unsigned long wifiDisconnectedTime = 0;
bool wifiDisconnectedTimerStarted = false;
const unsigned long WIFI_TIMEOUT_RESET = 900000; // 15 minutes in milliseconds

// FPS monitoring
struct FPSMonitor {
  unsigned long frameCount;
  unsigned long lastFPSUpdate;
  float currentFPS;
  unsigned long lastFrameTime;
} fpsMonitor = {0, 0, 0.0, 0};

// Data fetching control
bool dataFetchingPaused = false;
unsigned long dataFetchingPausedTime = 0;

// WiFi Reset Button variables
unsigned long bootButtonPressStart = 0;
bool bootButtonPressed = false;
bool wifiResetTriggered = false;
const unsigned long WIFI_RESET_HOLD_TIME = 5000; // 5 seconds

// Touch and Theme variables
struct TouchPoint {
  int16_t x, y;
  uint16_t pressure;
  bool pressed;
  unsigned long timestamp;
};

struct SwipeGesture {
  TouchPoint start, end;
  int16_t deltaX, deltaY;
  unsigned long duration;
  bool isValid;
  enum Direction { NONE, LEFT, RIGHT, UP, DOWN } direction;
};

// Brightness Control System
struct BrightnessControl {
  bool autoMode;           // Auto brightness enabled
  int manualLevel;         // Manual brightness level (0-100)
  int currentLevel;        // Current brightness level (0-100)
  int ldrValue;           // Raw LDR reading (0-4095)
  int ldrMin;             // Minimum LDR value (dark)
  int ldrMax;             // Maximum LDR value (bright)
  unsigned long lastLdrRead;
  bool brightnessPageVisible;
  bool needsUpdate;
  bool calibrationMode;    // LDR calibration mode
  unsigned long lastPWMUpdate; // For smooth PWM transitions
} brightness = {true, 80, 80, 0, 4095, 0, 0, false, false, false, 0};

// Professional Settings Page System - 2x4 Grid (No Scrolling)
struct ProfessionalSettingsPage {
  bool isVisible;
  bool isAnimating;
  int animationProgress; // 0-100
  unsigned long animationStart;
  bool pullDownDetected;
  TouchPoint pullStartPoint;
  unsigned long lastInteraction;
  unsigned long autoCloseTimer;  // 10-second auto-close timer
  int selectedMode;
  int hoveredItem;  // Currently hovered menu item (-1 = none)
  bool touchSensitivityReduced;  // Reduced sensitivity to prevent accidental taps
} settingsPage = {false, false, 0, 0, false, {0, 0, 0, false, 0}, 0, 0, 0, -1, true};

// Operating modes
const char* operatingModes[] = {"SBU", "SUB", "UTI", "SOL"};
const char* modeDescriptions[] = {
  "Solar-Battery-Utility",
  "Solar-Utility-Battery",
  "Utility First",
  "Solar Only"
};

// Modern Menu Items Structure
struct MenuItem {
  const char* title;
  const char* subtitle;
  const char* icon;
  uint16_t color;
  bool hasSubMenu;
  int itemType; // 0=action, 1=toggle, 2=selection, 3=navigation
};

// Professional 2x4 Grid Menu Items
MenuItem menuItems[] = {
  {"Mode", "SBU/SUB/UTI", "⚡", COLOR_WARNING, true, 2},      // Row 1, Col 1
  {"Theme", "Dark/Light", "🎨", COLOR_ACCENT, true, 2},       // Row 1, Col 2
  {"Brightness", "Auto/Manual", "💡", COLOR_SUCCESS, true, 3}, // Row 2, Col 1
  {"WiFi", "Network", "📶", COLOR_INFO, true, 3},             // Row 2, Col 2
  {"Reset", "Factory", "🔄", COLOR_ERROR, true, 3},           // Row 3, Col 1
  {"Info", "Device", "ℹ️", COLOR_TEXT_SECONDARY, false, 0},   // Row 3, Col 2
  {"Advanced", "Expert", "⚙️", COLOR_SOLAR, true, 3},         // Row 4, Col 1
  {"About", "Help", "❓", COLOR_TEXT_PRIMARY, false, 0}       // Row 4, Col 2
};

// Professional Grid Layout Constants
const int MENU_ITEM_COUNT = 8;
const int MENU_ROWS = 4;
const int MENU_COLS = 2;
const int MENU_ITEM_WIDTH = 140;
const int MENU_ITEM_HEIGHT = 35;
const int MENU_HEADER_HEIGHT = 60;
const int MENU_PADDING = 8;
const int MENU_START_X = 20;
const int MENU_START_Y = 80;
const int NUM_MODES = 4;

TouchPoint currentTouch = {0, 0, 0, false, 0};
TouchPoint lastTouch = {0, 0, 0, false, 0};
SwipeGesture currentSwipe = {{0, 0, 0, false, 0}, {0, 0, 0, false, 0}, 0, 0, 0, false, SwipeGesture::NONE};

int lastTouchX = 0;
int lastTouchY = 0;
bool touchPressed = false;
bool touchActive = false;
bool swipeInProgress = false;
unsigned long lastTouchTime = 0;
unsigned long lastDebounceTime = 0;
unsigned long lastThemeChange = 0;
bool themeChangeAnimation = false;
int themeAnimationFrame = 0;

// Display optimization variables
bool needsFullRedraw = true;
bool displayChanged = false;
float lastPvPower = -1;
float lastBatteryVoltage = -1;
float lastOutputPower = -1;

// Function declarations
void showSplashScreen();
void setupWiFi();
void fetchInverterData();
void updateProfessionalDisplay();
void drawHeader();

String formatTime(String timestamp);
uint16_t getEnhancedBatteryColor(float level);
void checkBootButton();
void performWiFiReset();
void performDisplayReset();
void showWiFiResetProgress(int progress);
bool readTouch(TouchPoint& point);
void processSwipe();
bool isValidSwipe(const SwipeGesture& swipe);
SwipeGesture::Direction getSwipeDirection(int16_t deltaX, int16_t deltaY);
void handleTouchInput();
void handleSwipeGesture(const SwipeGesture& swipe);
void changeTheme(int newTheme);
void showThemeChangeAnimation();
void updateDisplaySmooth();

// Settings Page Functions
void checkPullGestures();
void showSettingsPage();
void hideSettingsPage();
void updateSettingsPageAnimation();
void drawSettingsPage();
void drawModernMenuItem(int index, int x, int y, int w, int h);
void drawProfessionalMenuItem(int index, int x, int y, int w, int h);
void handleSettingsPageTouch(int x, int y);
void handleProfessionalTouch(int x, int y);
void handleProfessionalTouchEnd();
void resetProfessionalTouchState();
void highlightProfessionalMenuItem(int itemIndex);
void handleProfessionalMenuAction(int itemIndex);
void checkSettingsPageTimeout();
void pauseDataFetching();
void resumeDataFetching();

// Brightness Control Functions
void initializeBrightness();
void updateBrightness();
void readLDRSensor();
void setBrightness(int level);
void showBrightnessPage();
void hideBrightnessPage();
void drawBrightnessPage();
void handleBrightnessPageTouch(int x, int y);
void handleBrightnessPageTouchEnd();

// Advanced Anti-Flicker Functions
void drawOptimizedFullScreen();
void drawHeaderSmooth();
void drawCardSmooth(int cardIndex);
void clearCardArea(int cardIndex);
void updateCardNumbers(int x, int y, int w, int h, int cardType);
void updateSolarNumbers(int x, int y, int w, int h);
void updateGridNumbers(int x, int y, int w, int h);
void updateBatteryNumbers(int x, int y, int w, int h);
void updateLoadNumbers(int x, int y, int w, int h);
void drawOptimizedCard(int x, int y, int w, int h, int cardType);
void drawOptimizedSolarCard(int x, int y, int w, int h);
void drawOptimizedGridCard(int x, int y, int w, int h);
void drawOptimizedBatteryCard(int x, int y, int w, int h);
void drawOptimizedLoadCard(int x, int y, int w, int h);
void showModernWiFiSetup();
void showWiFiSuccessScreen();

void setup() {
  // Initialize Serial for debugging
  Serial.begin(115200);
  delay(1000);

  Serial.println("\n=== DESS Monitor Pro v1.0 ===");
  Serial.println("Initializing system...");

  // Initialize backlight
  pinMode(TFT_BL, OUTPUT);
  digitalWrite(TFT_BL, HIGH);
  Serial.println("✓ Backlight initialized");

  // Initialize BOOT button
  pinMode(BOOT_BUTTON, INPUT_PULLUP);
  Serial.println("✓ BOOT button initialized");

  // Initialize LDR sensor
  pinMode(LDR_PIN, INPUT);
  initializeBrightness();

  // Test LDR sensor immediately
  Serial.println("🔍 Testing LDR sensor...");
  for (int i = 0; i < 5; i++) {
    int ldrValue = analogRead(LDR_PIN);
    Serial.printf("🔍 LDR Test %d: Raw value = %d\n", i+1, ldrValue);
    delay(200);
  }

  Serial.println("✓ LDR sensor and brightness control initialized");

  // Initialize TFT with maximum performance settings
  tft.init();
  tft.setRotation(1);

  // Enable hardware acceleration features for maximum FPS
  tft.setSwapBytes(true); // Hardware byte swapping for faster color processing

  // Initialize DMA for ultra-fast screen updates (if available)
  #ifdef TFT_DMA_ENABLE
    tft.initDMA(); // Enable DMA for background transfers
    Serial.println("✓ TFT DMA enabled for ultra-fast updates");
  #endif

  // Set CPU frequency to maximum for best performance
  setCpuFrequencyMhz(240); // 240MHz - maximum ESP32 frequency

  tft.fillScreen(COLOR_BG);
  Serial.printf("✓ TFT display initialized (%dx%d) with max performance\n", tft.width(), tft.height());
  Serial.printf("✓ CPU frequency set to %dMHz for maximum FPS\n", getCpuFrequencyMhz());

  // Initialize touch screen with proper SPI setup
  touchscreenSPI.begin(XPT2046_CLK, XPT2046_MISO, XPT2046_MOSI, XPT2046_CS);
  ts.begin(touchscreenSPI);
  ts.setRotation(1); // Match TFT rotation
  Serial.println("✓ XPT2046 Touch screen initialized with proper SPI");

  // Test touch screen immediately
  Serial.println("🔍 Testing XPT2046 touch screen...");
  delay(100);
  if (ts.tirqTouched() && ts.touched()) {
    TS_Point p = ts.getPoint();
    Serial.printf("🔍 Touch test: ACTIVE at x=%d, y=%d, pressure=%d\n", p.x, p.y, p.z);
  } else {
    Serial.println("🔍 Touch test: No touch detected (normal)");
  }

  // Touch screen calibration info
  Serial.println("🔍 XPT2046 Touch screen calibration data:");
  Serial.printf("🔍 Touch CS pin: %d, IRQ pin: %d\n", XPT2046_CS, XPT2046_IRQ);
  Serial.printf("🔍 SPI pins: CLK=%d, MISO=%d, MOSI=%d\n", XPT2046_CLK, XPT2046_MISO, XPT2046_MOSI);
  Serial.println("🔍 XPT2046 Touch ready for input");

  // Show splash screen
  Serial.println("Showing splash screen...");
  showSplashScreen();

  // Setup WiFi
  Serial.println("Setting up WiFi...");
  setupWiFi();

  // Initial data fetch
  Serial.println("Fetching initial data...");
  fetchInverterData();

  // Initialize display
  Serial.println("Initializing professional display...");
  updateProfessionalDisplay();

  Serial.println("=== System Ready ===\n");
}

void loop() {
  static unsigned long lastStatusLog = 0;
  static bool lastWifiStatus = false;

  // FPS monitoring for performance optimization
  fpsMonitor.frameCount++;
  unsigned long currentTime = millis();

  if (currentTime - fpsMonitor.lastFPSUpdate >= 1000) { // Update FPS every second
    fpsMonitor.currentFPS = fpsMonitor.frameCount * 1000.0 / (currentTime - fpsMonitor.lastFPSUpdate);
    fpsMonitor.frameCount = 0;
    fpsMonitor.lastFPSUpdate = currentTime;

    // Log FPS every 10 seconds for monitoring
    static unsigned long lastFPSLog = 0;
    if (currentTime - lastFPSLog >= 10000) {
      Serial.printf("🎯 Performance: FPS=%.1f, Frame time=%.1fms\n",
                   fpsMonitor.currentFPS, 1000.0 / fpsMonitor.currentFPS);
      lastFPSLog = currentTime;
    }
  }

  // Check WiFi connection with 15-minute timeout reset
  if (WiFi.status() != WL_CONNECTED) {
    if (wifiConnected) {
      Serial.println("⚠️ WiFi disconnected!");
      wifiConnected = false;
      wifiDisconnectedTime = millis();
      wifiDisconnectedTimerStarted = true;
      Serial.printf("🕒 WiFi disconnect timer started - will reset in 15 minutes if not reconnected\n");
    }

    // Check if WiFi has been disconnected for more than 15 minutes
    if (wifiDisconnectedTimerStarted && (millis() - wifiDisconnectedTime > WIFI_TIMEOUT_RESET)) {
      Serial.println("⚠️ WiFi disconnected for more than 15 minutes!");
      Serial.println("🔄 Performing system reset to attempt reconnection...");

      // Show reset warning on screen
      tft.fillScreen(COLOR_BG);
      tft.setTextColor(COLOR_ERROR, COLOR_BG);
      tft.setTextSize(2);
      tft.setCursor(50, 100);
      tft.println("WiFi Timeout");

      tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_BG);
      tft.setTextSize(1);
      tft.setCursor(60, 130);
      tft.println("Disconnected > 15 min");
      tft.setCursor(80, 145);
      tft.println("Restarting system...");

      delay(3000);
      ESP.restart();
    }

    // Try reconnect every 30s (existing logic)
    if (millis() - lastDataUpdate > 30000) {
      Serial.printf("Attempting WiFi reconnection... (disconnected for %.1f minutes)\n",
                    (millis() - wifiDisconnectedTime) / 60000.0);
      setupWiFi();
    }
  } else {
    if (!wifiConnected) {
      Serial.printf("✓ WiFi reconnected! IP: %s\n", WiFi.localIP().toString().c_str());
      if (wifiDisconnectedTimerStarted) {
        float disconnectedMinutes = (millis() - wifiDisconnectedTime) / 60000.0;
        Serial.printf("📊 WiFi was disconnected for %.1f minutes\n", disconnectedMinutes);
        wifiDisconnectedTimerStarted = false;
      }
    }
    wifiConnected = true;
    data.signalStrength = WiFi.RSSI();
  }

  // Update data periodically (only if not paused)
  if (wifiConnected && !dataFetchingPaused && millis() - lastDataUpdate >= REFRESH_INTERVAL) {
    Serial.println("📡 Fetching inverter data...");
    fetchInverterData();
    lastDataUpdate = millis();

    if (data.dataValid) {
      Serial.printf("✓ Data updated: PV=%.0fW, Batt=%.1fV, Grid=%.0fV, Out=%.0fW\n",
                    data.pvPower, data.batteryVoltage, data.gridVoltage, data.outputPower);
    } else {
      Serial.println("❌ Failed to fetch valid data");
    }
  }

  // Update display with ultra-high FPS (every frame for maximum smoothness)
  updateDisplaySmooth();
  lastDisplayUpdate = millis();

  // Status log every 30 seconds
  if (millis() - lastStatusLog >= 30000) {
    if (wifiConnected) {
      Serial.printf("📊 Status: WiFi=OK, Signal=%ddBm, Mode=%s, Uptime=%lus\n",
                    data.signalStrength,
                    data.operatingMode.c_str(),
                    millis() / 1000);
    } else {
      float disconnectedMinutes = wifiDisconnectedTimerStarted ?
                                  (millis() - wifiDisconnectedTime) / 60000.0 : 0;
      float remainingMinutes = 15.0 - disconnectedMinutes;
      Serial.printf("📊 Status: WiFi=FAIL, Disconnected=%.1fmin, Reset in=%.1fmin, Uptime=%lus\n",
                    disconnectedMinutes,
                    remainingMinutes > 0 ? remainingMinutes : 0,
                    millis() / 1000);
    }
    lastStatusLog = millis();
  }

  // Ultra-smooth animation frame counter (60fps animations)
  if (millis() - lastAnimationUpdate >= 16) { // 16ms = 60fps
    animationFrame = (animationFrame + 1) % 60; // 60 frames for smooth animations
    lastAnimationUpdate = millis();
  }

  // Check BOOT button for WiFi reset
  checkBootButton();

  // Update brightness control
  updateBrightness();

  // Handle touch input and gestures
  handleTouchInput();

  // Check for pull gestures for settings page
  checkPullGestures();

  // Update settings page animation
  updateSettingsPageAnimation();

  // Check settings page timeout
  checkSettingsPageTimeout();

  // Debug XPT2046 touch screen every 2 seconds
  static unsigned long lastTouchDebug = 0;
  if (millis() - lastTouchDebug >= 2000) {
    if (ts.tirqTouched() && ts.touched()) {
      TS_Point p = ts.getPoint();
      int screenX = map(p.x, 200, 3700, 1, 320);
      int screenY = map(p.y, 240, 3800, 1, 240);
      Serial.printf("🔍 XPT2046 DEBUG: raw(%d,%d,%d) -> screen(%d,%d)\n",
                    p.x, p.y, p.z, screenX, screenY);
    } else {
      Serial.println("🔍 XPT2046 DEBUG: No touch detected");
    }
    lastTouchDebug = millis();
  }

  // Handle theme change animation
  if (themeChangeAnimation) {
    showThemeChangeAnimation();
  }

  // Ultra-high FPS for maximum smoothness - minimal delay
  delay(1); // 1ms delay = theoretical 1000fps, actual ~200-500fps depending on processing
}

void showSplashScreen() {
  // Animated gradient background
  for (int y = 0; y < 240; y++) {
    uint16_t color = tft.color565(
      8 + (y / 8),    // Deep blue gradient
      33 + (y / 6),   // Navy to blue
      65 + (y / 4)    // Dark to bright
    );
    tft.drawFastHLine(0, y, 320, color);
  }

  // Animated solar rays background
  for (int i = 0; i < 12; i++) {
    float angle = i * 30 * PI / 180;
    int x1 = 160 + cos(angle) * 80;
    int y1 = 120 + sin(angle) * 80;
    int x2 = 160 + cos(angle) * 120;
    int y2 = 120 + sin(angle) * 120;
    tft.drawLine(x1, y1, x2, y2, COLOR_GLOW_EFFECT);
  }

  // Central sun logo with glow
  tft.fillCircle(160, 120, 35, COLOR_GLOW_EFFECT);
  tft.fillCircle(160, 120, 30, COLOR_SOLAR);
  tft.fillCircle(160, 120, 25, COLOR_SOLAR_GLOW);

  // Animated sun rays
  for (int frame = 0; frame < 20; frame++) {
    for (int i = 0; i < 8; i++) {
      float angle = (i * 45 + frame * 3) * PI / 180;
      int rayX = 160 + cos(angle) * 45;
      int rayY = 120 + sin(angle) * 45;
      tft.drawLine(160 + cos(angle) * 32, 120 + sin(angle) * 32, rayX, rayY, COLOR_SOLAR_GLOW);
    }
    delay(50);
  }

  // Modern logo text with glow effect
  tft.setTextColor(COLOR_SHADOW, COLOR_BG);
  tft.setTextSize(3);
  tft.setCursor(71, 61);
  tft.println("SOLAR");

  tft.setTextColor(COLOR_SOLAR, COLOR_BG);
  tft.setCursor(70, 60);
  tft.println("SOLAR");

  tft.setTextColor(COLOR_SHADOW, COLOR_BG);
  tft.setTextSize(2);
  tft.setCursor(81, 91);
  tft.println("MONITOR");

  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_BG);
  tft.setCursor(80, 90);
  tft.println("MONITOR");

  // Subtitle with animation
  tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_BG);
  tft.setTextSize(1);
  tft.setCursor(95, 180);
  tft.println("Professional Edition");

  tft.setTextColor(COLOR_SOLAR, COLOR_BG);
  tft.setCursor(125, 195);
  tft.println("v2.0 Solar");

  // Loading animation
  tft.setTextColor(COLOR_TEXT_DIM, COLOR_BG);
  tft.setCursor(130, 210);
  tft.println("Loading");

  for (int i = 0; i < 3; i++) {
    for (int dots = 0; dots < 4; dots++) {
      tft.fillRect(180 + dots * 8, 210, 6, 8, COLOR_BG);
      for (int d = 0; d <= dots; d++) {
        tft.fillRect(180 + d * 8, 210, 6, 8, COLOR_SOLAR);
      }
      delay(200);
    }
  }

  delay(500);
}

void setupWiFi() {
  Serial.println("Starting WiFi setup...");

  // Show modern WiFi setup screen
  showModernWiFiSetup();

  Serial.println("Creating WiFi AP: DESS-Monitor");
  Serial.println("Portal IP: ***********");
  Serial.println("Portal timeout: 180 seconds");

  WiFiManager wm;
  wm.setAPStaticIPConfig(IPAddress(192,168,4,1), IPAddress(192,168,4,1), IPAddress(255,255,255,0));
  wm.setConfigPortalTimeout(180);

  if (!wm.autoConnect("DESS-Monitor")) {
    Serial.println("❌ WiFi connection failed!");

    // Modern error screen
    tft.fillScreen(COLOR_BG);

    // Error icon
    tft.fillCircle(160, 80, 25, COLOR_ERROR);
    tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_ERROR);
    tft.setTextSize(3);
    tft.setCursor(150, 70);
    tft.println("!");

    tft.setTextColor(COLOR_ERROR, COLOR_BG);
    tft.setTextSize(2);
    tft.setCursor(80, 130);
    tft.println("WiFi Failed");

    tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_BG);
    tft.setTextSize(1);
    tft.setCursor(90, 160);
    tft.println("Restarting system...");

    delay(3000);
    Serial.println("Restarting ESP32...");
    ESP.restart();
  }

  wifiConnected = true;

  Serial.printf("✓ WiFi connected successfully!\n");
  Serial.printf("SSID: %s\n", WiFi.SSID().c_str());
  Serial.printf("IP Address: %s\n", WiFi.localIP().toString().c_str());
  Serial.printf("Signal Strength: %d dBm\n", WiFi.RSSI());
  Serial.printf("MAC Address: %s\n", WiFi.macAddress().c_str());

  // Beautiful WiFi success screen with professional design
  showWiFiSuccessScreen();
}

void fetchInverterData() {
  if (!wifiConnected) {
    // Don't invalidate existing data when WiFi is disconnected
    Serial.println("⚠️ Cannot fetch data: WiFi not connected - keeping last valid data");
    return;
  }

  Serial.printf("🌐 HTTP GET: %s\n", apiURL);

  HTTPClient http;
  http.begin(apiURL);
  http.setTimeout(10000);

  int httpCode = http.GET();
  Serial.printf("📡 HTTP Response: %d\n", httpCode);

  if (httpCode == 200) {
    String payload = http.getString();
    Serial.printf("📦 Payload size: %d bytes\n", payload.length());

    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, payload);

    if (!error && doc["err"] == 0) {
      // CRITICAL: Only update data when we have valid new data
      data.timestamp = doc["dat"]["gts"].as<String>();
      data.lastUpdate = millis();
      Serial.printf("⏰ Data timestamp: %s\n", data.timestamp.c_str());

      // Parse PV data
      if (doc["dat"]["pars"]["pv_"].is<JsonArray>()) {
        Serial.println("📊 Parsing PV data:");
        for (JsonObject param : doc["dat"]["pars"]["pv_"].as<JsonArray>()) {
          String parName = param["par"].as<String>();
          float value = param["val"].as<float>();
          Serial.printf("  - %s: %.2f\n", parName.c_str(), value);

          if (parName == "PV Voltage") {
            data.pvVoltage = value;
          } else if (parName == "PV Current") {
            data.pvCurrent = value;
          } else if (parName == "PV Power") {
            data.pvPower = value;
          }
        }
      }

      // Parse Battery data
      if (doc["dat"]["pars"]["bt_"].is<JsonArray>()) {
        Serial.println("🔋 Parsing Battery data:");
        for (JsonObject param : doc["dat"]["pars"]["bt_"].as<JsonArray>()) {
          String parName = param["par"].as<String>();
          float value = param["val"].as<float>();
          Serial.printf("  - %s: %.2f\n", parName.c_str(), value);

          if (parName == "Battery Voltage") {
            data.batteryVoltage = value;
          } else if (parName == "Battery Current") {
            data.batteryCurrent = value;
          }
        }
      }

      // Parse Grid data
      if (doc["dat"]["pars"]["gd_"].is<JsonArray>()) {
        Serial.println("⚡ Parsing Grid data:");
        for (JsonObject param : doc["dat"]["pars"]["gd_"].as<JsonArray>()) {
          String parName = param["par"].as<String>();
          float value = param["val"].as<float>();
          Serial.printf("  - %s: %.2f\n", parName.c_str(), value);

          if (parName == "Grid Voltage") {
            data.gridVoltage = value;
          } else if (parName == "Grid Frequency") {
            data.gridFrequency = value;
          } else if (parName == "Grid Power") {
            data.gridPower = value;
          }
        }
      }

      // Parse Output data (bc_ section)
      if (doc["dat"]["pars"]["bc_"].is<JsonArray>()) {
        Serial.println("📤 Parsing Output data (bc_):");
        for (JsonObject param : doc["dat"]["pars"]["bc_"].as<JsonArray>()) {
          String parName = param["par"].as<String>();
          float value = param["val"].as<float>();
          Serial.printf("  - %s: %.2f\n", parName.c_str(), value);

          if (parName == "Output Voltage") {
            data.outputVoltage = value;
          } else if (parName == "Output Current") {
            data.outputCurrent = value;
          } else if (parName == "Output Active Power") {
            data.outputPower = value;
          } else if (parName == "Output Apparent Power") {
            data.outputApparentPower = value;
          } else if (parName == "Output frequency") {  // Note: lowercase 'f'
            data.outputFrequency = value;
          } else if (parName == "Load Percent") {
            data.loadPercent = value;
          }
        }
      }

      // Parse System data
      if (doc["dat"]["pars"]["sy_"].is<JsonArray>()) {
        Serial.println("🌡️ Parsing System data:");
        for (JsonObject param : doc["dat"]["pars"]["sy_"].as<JsonArray>()) {
          String parName = param["par"].as<String>();
          String value = param["val"].as<String>();
          Serial.printf("  - %s: %s\n", parName.c_str(), value.c_str());

          if (parName == "DC Module Termperature") {
            data.dcTemp = param["val"].as<int>();
          } else if (parName == "INV Module Termperature") {
            data.invTemp = param["val"].as<int>();
          } else if (parName == "Operating Mode") {
            data.operatingMode = value;
          }
        }
      }

      data.dataValid = true;
      Serial.println("✅ Data parsing successful");
    } else {
      // Don't invalidate existing data on JSON parsing error - keep last valid data
      Serial.println("❌ JSON parsing failed or API error - keeping last valid data");
    }
  } else {
    // Don't invalidate existing data on HTTP error - keep last valid data
    Serial.printf("❌ HTTP request failed: %d - keeping last valid data\n", httpCode);
  }

  http.end();
}

void updateProfessionalDisplay() {
  // Advanced Smart Anti-Flicker System with Real-time Data Detection
  bool needFullRedraw = smartRedraw.forceNextRedraw;
  bool needHeaderUpdate = false;
  bool cardUpdates[4] = {false, false, false, false}; // Solar, Grid, Battery, Load

  // First time initialization
  if (!smartRedraw.initialized) {
    needFullRedraw = true;
    smartRedraw.initialized = true;
    Serial.println("🖥️ First time initialization - full redraw");
  } else if (smartRedraw.forceNextRedraw) {
    needFullRedraw = true;
    Serial.println("🖥️ Forced redraw for theme change - keeping existing data");
  }

  // CRITICAL: Check for new data timestamp - ensures real-time updates
  if (data.timestamp != smartRedraw.lastTimestamp && data.dataValid) {
    Serial.printf("🕒 New data timestamp: %s -> %s\n",
                  smartRedraw.lastTimestamp.c_str(), data.timestamp.c_str());
    needHeaderUpdate = true; // Update header with new timestamp

    // Force check all cards for any changes when new data arrives
    Serial.println("🔄 New data detected - checking all cards for updates");
  }

  // Removed scheduled full redraw - not needed with smart redraw system

  // CRITICAL: Only redraw when data becomes valid, not when it becomes invalid
  // This prevents screen clearing when HTTP errors occur
  if (data.dataValid && !smartRedraw.lastDataValid) {
    needFullRedraw = true;
    Serial.println("🖥️ Data became valid - full redraw");
  } else if (!data.dataValid && smartRedraw.lastDataValid) {
    // Data became invalid - don't redraw, just log
    Serial.println("⚠️ Data became invalid - keeping current display");
  }

  if (data.operatingMode != smartRedraw.lastOperatingMode) {
    needHeaderUpdate = true;
    Serial.println("📊 Operating mode changed - header update");
  }

  // CRITICAL: Only check for data changes when data is valid
  // This prevents updates with invalid/stale data during HTTP errors
  if (data.dataValid) {
    // Ultra-sensitive thresholds for real-time responsiveness - detect ANY change
    const float EPSILON = 0.01f;

    // Solar card - detect ANY change in power or voltage
    if (abs(data.pvPower - smartRedraw.lastPvPower) > EPSILON ||
        abs(data.pvVoltage - smartRedraw.lastPvVoltage) > EPSILON) {
      cardUpdates[0] = true;
      cardRegions[0].dirty = true;
      Serial.printf("🔄 Solar update: Power %.1f->%.1fW, Voltage %.1f->%.1fV\n",
                    smartRedraw.lastPvPower, data.pvPower, smartRedraw.lastPvVoltage, data.pvVoltage);
    }

    // Grid card - detect ANY change in voltage or frequency
    if (abs(data.gridVoltage - smartRedraw.lastGridVoltage) > EPSILON ||
        abs(data.gridFrequency - smartRedraw.lastGridFrequency) > EPSILON) {
      cardUpdates[1] = true;
      cardRegions[1].dirty = true;
      Serial.printf("🔄 Grid update: Voltage %.1f->%.1fV, Freq %.2f->%.2fHz\n",
                    smartRedraw.lastGridVoltage, data.gridVoltage, smartRedraw.lastGridFrequency, data.gridFrequency);
    }

    // Battery card - detect ANY change in voltage
    if (abs(data.batteryVoltage - smartRedraw.lastBatteryVoltage) > EPSILON) {
      cardUpdates[2] = true;
      cardRegions[2].dirty = true;
      Serial.printf("🔄 Battery update: Voltage %.2f->%.2fV, Current %.2fA\n",
                    smartRedraw.lastBatteryVoltage, data.batteryVoltage, data.batteryCurrent);
    }

    // Load card - detect ANY change in power or load percentage
    if (abs(data.outputPower - smartRedraw.lastOutputPower) > EPSILON ||
        abs(data.loadPercent - smartRedraw.lastLoadPercent) > EPSILON) {
      cardUpdates[3] = true;
      cardRegions[3].dirty = true;
      Serial.printf("🔄 Load update: Power %.1f->%.1fW, Load %.1f->%.1f%%\n",
                    smartRedraw.lastOutputPower, data.outputPower,
                    smartRedraw.lastLoadPercent, data.loadPercent);
    }
  } else {
    // Data is invalid - don't update cards, keep showing last valid data
    Serial.println("⚠️ Data invalid - skipping card updates, keeping current display");
  }

  // Check if any updates are needed
  bool anyCardUpdate = cardUpdates[0] || cardUpdates[1] || cardUpdates[2] || cardUpdates[3];
  if (!anyCardUpdate && !needFullRedraw && !needHeaderUpdate) {
    // No updates needed - skip drawing
    return;
  }

  // Execute updates with minimal flicker
  if (needFullRedraw) {
    // Full redraw with optimized clearing
    drawOptimizedFullScreen();
    lastFullRedraw = millis();
    smartRedraw.forceNextRedraw = false;
  } else {
    // Partial updates only
    if (needHeaderUpdate) {
      drawHeaderSmooth();
    }

    // Update only dirty cards
    for (int i = 0; i < 4; i++) {
      if (cardUpdates[i] || cardRegions[i].dirty) {
        drawCardSmooth(i);
        cardRegions[i].dirty = false;
        cardRegions[i].lastUpdate = millis();
      }
    }
  }

  // Update tracking values - CRITICAL: Only update when data is valid
  // This prevents tracking invalid data during HTTP errors
  if (data.dataValid) {
    smartRedraw.lastPvPower = data.pvPower;
    smartRedraw.lastPvVoltage = data.pvVoltage;
    smartRedraw.lastBatteryVoltage = data.batteryVoltage;
    smartRedraw.lastGridVoltage = data.gridVoltage;
    smartRedraw.lastGridFrequency = data.gridFrequency;
    smartRedraw.lastOutputPower = data.outputPower;
    smartRedraw.lastLoadPercent = data.loadPercent;
    smartRedraw.lastOperatingMode = data.operatingMode;
    smartRedraw.lastTimestamp = data.timestamp; // Track timestamp changes
  }

  // Always update data valid status to track state changes
  smartRedraw.lastDataValid = data.dataValid;
}

void drawHeader() {
  // Top status bar with essential info
  tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_BG);
  tft.setTextSize(1);

  // Last update time (left side)
  tft.setCursor(5, 3);
  if (data.dataValid) {
    tft.printf("Last: %s", formatTime(data.timestamp).c_str());
  } else {
    tft.print("Last: --:--");
  }

  // Device Name (center)
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_BG);
  tft.setCursor(90, 3);
  tft.print("POWMR 6.2KW");  // Device model name

  // WiFi signal strength (right side)
  tft.setCursor(240, 3);
  if (wifiConnected) {
    int rssi = WiFi.RSSI();
    tft.printf("WiFi: %ddBm", rssi);

    // Signal quality indicator
    uint16_t signalColor;
    if (rssi > -50) signalColor = COLOR_SUCCESS;
    else if (rssi > -70) signalColor = COLOR_WARNING;
    else signalColor = COLOR_ERROR;

    tft.fillCircle(315, 6, 2, signalColor);
  } else {
    // Show WiFi timeout warning
    if (wifiDisconnectedTimerStarted) {
      float disconnectedMinutes = (millis() - wifiDisconnectedTime) / 60000.0;
      float remainingMinutes = 15.0 - disconnectedMinutes;

      if (remainingMinutes > 0) {
        tft.setTextColor(COLOR_WARNING, COLOR_BG);
        tft.printf("Reset: %.0fm", remainingMinutes);
      } else {
        tft.setTextColor(COLOR_ERROR, COLOR_BG);
        tft.print("Resetting...");
      }
    } else {
      tft.print("WiFi: OFF");
    }
    tft.fillCircle(315, 6, 2, COLOR_ERROR);
  }

  // Separator line
  tft.drawFastHLine(0, 12, 320, COLOR_TEXT_DIM);
}

void drawStatusBar() {
  // Status bar removed - info moved to header
  // This function is kept for compatibility but does nothing
}



// Removed unused card functions - using optimized cards instead

// Helper functions
void drawProgressBar(int x, int y, int w, int h, float value, float max, uint16_t color) {
  // Background
  tft.fillRect(x, y, w, h, COLOR_TEXT_SECONDARY);

  // Progress
  int progress = (value / max) * w;
  progress = constrain(progress, 0, w);
  tft.fillRect(x, y, progress, h, color);

  // Border
  tft.drawRect(x, y, w, h, COLOR_TEXT_PRIMARY);
}

void drawRoundedRect(int x, int y, int w, int h, int r, uint16_t color) {
  tft.fillRect(x + r, y, w - 2*r, h, color);
  tft.fillRect(x, y + r, w, h - 2*r, color);
  tft.fillCircle(x + r, y + r, r, color);
  tft.fillCircle(x + w - r - 1, y + r, r, color);
  tft.fillCircle(x + r, y + h - r - 1, r, color);
  tft.fillCircle(x + w - r - 1, y + h - r - 1, r, color);
}

String formatPower(float power) {
  if (power >= 1000) {
    return String(power/1000, 1) + "kW";
  } else {
    return String((int)power) + "W";
  }
}

String formatTime(String timestamp) {
  if (timestamp.length() >= 19) {
    return timestamp.substring(11, 16); // HH:MM
  }
  return "00:00";
}

// Removed unused helper functions

uint16_t getEnhancedBatteryColor(float level) {
  if (level > 80) return COLOR_BATTERY_FULL;
  else if (level > 60) return COLOR_BATTERY_GOOD;
  else if (level > 40) return COLOR_BATTERY_MID;
  else if (level > 20) return COLOR_BATTERY_LOW;
  else return COLOR_BATTERY_CRITICAL;
}

void drawModernCard(int x, int y, int w, int h, uint16_t accentColor) {
  // Shadow effect
  tft.fillRoundRect(x + 2, y + 2, w, h, 8, COLOR_SHADOW);

  // Main card background with gradient
  for (int i = 0; i < h; i++) {
    uint16_t color = tft.color565(
      41 + (i / 4),   // Subtle gradient
      101 + (i / 4),
      149 + (i / 4)
    );
    tft.drawFastHLine(x, y + i, w, color);
  }

  // Card border with accent color
  tft.drawRoundRect(x, y, w, h, 8, COLOR_CARD_BORDER);
  tft.drawRoundRect(x + 1, y + 1, w - 2, h - 2, 7, accentColor);

  // Top accent line
  tft.fillRect(x + 8, y + 2, w - 16, 2, accentColor);
}

void drawEnhancedProgressBar(int x, int y, int w, int h, float value, float max, uint16_t color, bool animated) {
  // Background with gradient
  for (int i = 0; i < h; i++) {
    uint16_t bgColor = tft.color565(20 + i * 2, 20 + i * 2, 20 + i * 2);
    tft.drawFastHLine(x, y + i, w, bgColor);
  }

  // Progress fill
  int progress = (value / max) * w;
  progress = constrain(progress, 0, w);

  if (animated && progress > 0) {
    // Animated fill with flowing effect
    static int flowAnim = 0;
    flowAnim = (flowAnim + 1) % 20;

    for (int i = 0; i < progress; i++) {
      int brightness = 255;
      if (i > progress - 5) {
        // Flowing edge effect
        brightness = 150 + ((flowAnim + i) % 10) * 10;
      }
      uint16_t fillColor = tft.color565(
        (color >> 11) * brightness / 255,
        ((color >> 5) & 0x3F) * brightness / 255,
        (color & 0x1F) * brightness / 255
      );
      tft.drawFastVLine(x + i, y, h, fillColor);
    }
  } else {
    // Static fill with gradient
    for (int i = 0; i < progress; i++) {
      uint16_t fillColor = tft.color565(
        ((color >> 11) * (255 - i * 2)) / 255,
        (((color >> 5) & 0x3F) * (255 - i * 2)) / 255,
        ((color & 0x1F) * (255 - i * 2)) / 255
      );
      tft.drawFastVLine(x + i, y, h, fillColor);
    }
  }

  // Border with glow
  tft.drawRect(x - 1, y - 1, w + 2, h + 2, COLOR_GLOW_EFFECT);
  tft.drawRect(x, y, w, h, color);
}











void showModernWiFiSetup() {
  // Beautiful gradient background
  for (int y = 0; y < 240; y++) {
    uint16_t color = tft.color565(
      ((COLOR_BG >> 11) * (240 - y)) / 240,
      (((COLOR_BG >> 5) & 0x3F) * (240 - y)) / 240,
      ((COLOR_BG & 0x1F) * (240 - y)) / 240
    );
    tft.drawFastHLine(0, y, 320, color);
  }

  // Floating particles animation
  for (int frame = 0; frame < 15; frame++) {
    for (int i = 0; i < 8; i++) {
      int x = 40 + (i * 35) + (frame * 2) % 20;
      int y = 30 + (i % 3) * 15 + sin(frame * 0.5 + i) * 5;
      tft.fillCircle(x, y, 2, COLOR_GLOW_EFFECT);
    }
    delay(100);
  }

  // Central WiFi icon with glow effect
  int centerX = 160, centerY = 80;

  // Glow rings
  for (int r = 50; r > 0; r -= 5) {
    uint16_t glowColor = tft.color565(
      (COLOR_ACCENT >> 11) * r / 50,
      ((COLOR_ACCENT >> 5) & 0x3F) * r / 50,
      (COLOR_ACCENT & 0x1F) * r / 50
    );
    tft.drawCircle(centerX, centerY, r, glowColor);
  }

  // Animated WiFi signal with ripple effect
  for (int frame = 0; frame < 25; frame++) {
    // Clear center area
    tft.fillCircle(centerX, centerY, 35, COLOR_BG);

    // WiFi signal bars with ripple animation
    for (int i = 0; i < 4; i++) {
      int barHeight = (i + 1) * 6;
      int barWidth = 6;
      int x = centerX - 15 + i * 10;
      int y = centerY + 10;

      // Ripple effect
      if ((frame - i * 3) % 12 < 6) {
        tft.fillRect(x, y - barHeight, barWidth, barHeight, COLOR_ACCENT);
        // Glow effect
        tft.drawRect(x - 1, y - barHeight - 1, barWidth + 2, barHeight + 2, COLOR_GLOW_EFFECT);
      } else {
        tft.drawRect(x, y - barHeight, barWidth, barHeight, COLOR_TEXT_DIM);
      }
    }

    // Central WiFi symbol
    tft.fillCircle(centerX, centerY - 5, 3, COLOR_ACCENT);
    delay(80);
  }

  // Elegant title with shadow effect
  tft.setTextColor(COLOR_SHADOW, COLOR_BG);
  tft.setTextSize(3);
  tft.setCursor(71, 131);
  tft.println("WiFi Setup");

  tft.setTextColor(COLOR_ACCENT, COLOR_BG);
  tft.setCursor(70, 130);
  tft.println("WiFi Setup");

  // Theme indicator with simple styling
  tft.fillRect(60, 160, 200, 25, COLOR_CARD_BG);
  tft.drawRect(60, 160, 200, 25, COLOR_ACCENT);
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_CARD_BG);
  tft.setTextSize(1);
  tft.setCursor(70, 170);
  tft.printf("%s %s Theme Active", theme->icon, theme->name);

  // Instruction panel
  tft.fillRect(20, 190, 280, 45, COLOR_CARD_BG);
  tft.drawRect(20, 190, 280, 45, COLOR_ACCENT);

  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_CARD_BG);
  tft.setTextSize(1);
  tft.setCursor(30, 200);
  tft.println("📱 Connect to 'DESS-Monitor' WiFi");
  tft.setCursor(30, 212);
  tft.println("🌐 Open browser → ***********");
  tft.setCursor(30, 224);
  tft.println("⚙️  Configure your network settings");

  // Breathing dots animation
  for (int breath = 0; breath < 20; breath++) {
    tft.fillRect(130, 240, 60, 10, COLOR_BG);

    for (int dot = 0; dot < 4; dot++) {
      int size = 2 + sin((breath + dot * 2) * 0.3) * 1;
      uint16_t dotColor = tft.color565(
        (COLOR_ACCENT >> 11) * (size + 1) / 4,
        ((COLOR_ACCENT >> 5) & 0x3F) * (size + 1) / 4,
        (COLOR_ACCENT & 0x1F) * (size + 1) / 4
      );
      tft.fillCircle(140 + dot * 15, 245, size, dotColor);
    }
    delay(150);
  }
}

void showWiFiSuccessScreen() {
  // Beautiful WiFi connection success screen with professional design
  tft.fillScreen(COLOR_BG);

  // Animated gradient background with subtle movement
  for (int y = 0; y < 240; y++) {
    uint16_t color = tft.color565(
      ((COLOR_BG >> 11) * (240 - y)) / 240 + 5,
      (((COLOR_BG >> 5) & 0x3F) * (240 - y)) / 240 + 10,
      ((COLOR_BG & 0x1F) * (240 - y)) / 240 + 15
    );
    tft.drawFastHLine(0, y, 320, color);
  }

  // Success celebration animation with floating particles
  for (int frame = 0; frame < 20; frame++) {
    // Clear previous particles
    if (frame > 0) {
      for (int i = 0; i < 12; i++) {
        int x = 40 + (i * 25) + (frame - 1) * 3;
        int y = 40 + (i % 4) * 20 + sin((frame - 1) * 0.4 + i) * 8;
        if (x >= 0 && x < 320 && y >= 0 && y < 240) {
          tft.fillCircle(x, y, 3, COLOR_BG);
        }
      }
    }

    // Draw new particles
    for (int i = 0; i < 12; i++) {
      int x = 40 + (i * 25) + frame * 3;
      int y = 40 + (i % 4) * 20 + sin(frame * 0.4 + i) * 8;
      if (x >= 0 && x < 320 && y >= 0 && y < 240) {
        uint16_t particleColor = (i % 3 == 0) ? COLOR_SUCCESS :
                                (i % 3 == 1) ? COLOR_ACCENT : COLOR_SOLAR;
        tft.fillCircle(x, y, 2 + sin(frame * 0.3 + i) * 1, particleColor);
      }
    }
    delay(80);
  }

  // Central success icon with glow effect
  int centerX = 160, centerY = 90;

  // Multi-layer glow effect
  for (int r = 45; r > 0; r -= 3) {
    uint16_t glowColor = tft.color565(
      (COLOR_SUCCESS >> 11) * r / 45,
      ((COLOR_SUCCESS >> 5) & 0x3F) * r / 45,
      (COLOR_SUCCESS & 0x1F) * r / 45
    );
    tft.drawCircle(centerX, centerY, r, glowColor);
  }

  // Success checkmark with animation
  tft.fillCircle(centerX, centerY, 30, COLOR_SUCCESS);
  tft.fillCircle(centerX, centerY, 25, COLOR_BG);

  // Animated checkmark drawing
  for (int step = 0; step <= 10; step++) {
    // Clear previous checkmark
    if (step > 0) {
      tft.fillCircle(centerX, centerY, 25, COLOR_BG);
    }

    // Draw checkmark progressively
    tft.setTextColor(COLOR_SUCCESS, COLOR_BG);
    tft.setTextSize(4);
    tft.setCursor(centerX - 12, centerY - 15);

    if (step >= 5) tft.print("✓");
    else if (step >= 3) tft.print("✓"[0]);

    delay(100);
  }

  // Elegant title with shadow and glow
  tft.setTextColor(COLOR_SHADOW, COLOR_BG);
  tft.setTextSize(3);
  tft.setCursor(71, 141);
  tft.println("Connected!");

  tft.setTextColor(COLOR_SUCCESS, COLOR_BG);
  tft.setCursor(70, 140);
  tft.println("Connected!");

  // Professional information panel with rounded corners
  int panelX = 20, panelY = 170, panelW = 280, panelH = 60;

  // Panel background with gradient
  for (int i = 0; i < panelH; i++) {
    uint16_t panelColor = tft.color565(
      ((COLOR_CARD_BG >> 11) * (panelH - i)) / panelH + 10,
      (((COLOR_CARD_BG >> 5) & 0x3F) * (panelH - i)) / panelH + 20,
      ((COLOR_CARD_BG & 0x1F) * (panelH - i)) / panelH + 30
    );
    tft.drawFastHLine(panelX, panelY + i, panelW, panelColor);
  }

  // Panel border with accent color
  tft.drawRect(panelX, panelY, panelW, panelH, COLOR_SUCCESS);
  tft.drawRect(panelX + 1, panelY + 1, panelW - 2, panelH - 2, COLOR_ACCENT);

  // Connection information with icons
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_CARD_BG);
  tft.setTextSize(1);

  // Network name with WiFi icon
  tft.setCursor(panelX + 8, panelY + 8);
  tft.printf("📶 %s", WiFi.SSID().c_str());

  // IP address with network icon
  tft.setCursor(panelX + 8, panelY + 22);
  tft.printf("🌐 %s", WiFi.localIP().toString().c_str());

  // Signal strength with quality indicator
  int rssi = WiFi.RSSI();
  const char* signalIcon = (rssi > -50) ? "📶" : (rssi > -70) ? "📶" : "📶";
  uint16_t signalColor = (rssi > -50) ? COLOR_SUCCESS : (rssi > -70) ? COLOR_WARNING : COLOR_ERROR;

  tft.setTextColor(signalColor, COLOR_CARD_BG);
  tft.setCursor(panelX + 8, panelY + 36);
  tft.printf("%s Signal: %d dBm", signalIcon, rssi);

  // Theme indicator in corner
  tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_CARD_BG);
  tft.setCursor(panelX + panelW - 60, panelY + 50);
  tft.printf("%s %s", theme->icon, theme->name);

  // Success pulse animation
  for (int pulse = 0; pulse < 8; pulse++) {
    // Pulse the success circle
    int pulseRadius = 30 + sin(pulse * 0.8) * 5;
    tft.drawCircle(centerX, centerY, pulseRadius, COLOR_SUCCESS);
    delay(150);
    tft.drawCircle(centerX, centerY, pulseRadius, COLOR_BG);
  }

  // Final display for 2 seconds
  delay(2000);
}

void changeTheme(int newTheme) {
  if (newTheme == currentTheme) return;
  if (millis() - lastThemeChange < 2000) return; // Prevent rapid changes

  Serial.printf("🎨 Changing theme from %s to %s\n",
                themes[currentTheme].name, themes[newTheme].name);

  currentTheme = newTheme;
  theme = &themes[currentTheme];
  lastThemeChange = millis();

  // Force immediate data refresh and full redraw
  Serial.println("🔄 Forcing data refresh for theme change");
  smartRedraw.forceNextRedraw = true;
  // Don't reset initialized - keep existing data
  // Force immediate display update with current data
  for (int i = 0; i < 4; i++) {
    cardRegions[i].dirty = true;
  }

  // Start theme change animation
  themeChangeAnimation = true;
  themeAnimationFrame = 0;
}

void showThemeChangeAnimation() {
  static unsigned long lastAnimFrame = 0;
  if (millis() - lastAnimFrame < 50) return;
  lastAnimFrame = millis();

  themeAnimationFrame++;

  if (themeAnimationFrame <= 20) {
    // Fade out effect
    int alpha = 255 - (themeAnimationFrame * 12);

    // Draw theme change overlay
    tft.fillRect(0, 100, 320, 40, COLOR_SHADOW);

    // Theme info
    tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_SHADOW);
    tft.setTextSize(2);
    tft.setCursor(60, 110);
    tft.printf("%s %s", theme->icon, theme->name);

    // Progress bar
    int progress = (themeAnimationFrame * 280) / 20;
    tft.fillRect(20, 130, progress, 4, COLOR_ACCENT);
    tft.drawRect(20, 130, 280, 4, COLOR_TEXT_DIM);

  } else {
    // Animation complete
    themeChangeAnimation = false;
    themeAnimationFrame = 0;

    // Update display with existing data and new theme (no screen clear)
    Serial.println("🔄 Theme change complete - updating display with existing data");
    smartRedraw.forceNextRedraw = true; // Force full redraw with new theme
    updateProfessionalDisplay(); // Update display with new theme and existing data
  }
}

void checkBootButton() {
  bool currentButtonState = !digitalRead(BOOT_BUTTON); // Active LOW
  static bool progressShown = false; // Track if progress was shown

  if (currentButtonState && !bootButtonPressed) {
    // Button just pressed
    bootButtonPressed = true;
    bootButtonPressStart = millis();
    wifiResetTriggered = false;
    progressShown = false; // Reset progress flag
    Serial.println("🔘 BOOT button pressed - hold 2-5s for display reset, 5s+ for WiFi reset");
  } else if (!currentButtonState && bootButtonPressed) {
    // Button just released
    bootButtonPressed = false;
    unsigned long holdTime = millis() - bootButtonPressStart;
    bootButtonPressStart = 0;

    // Clear progress display if it was shown but WiFi reset wasn't triggered
    if (progressShown && !wifiResetTriggered) {
      Serial.println("🔄 Clearing WiFi reset progress display");
      // Force full screen redraw to clear progress
      smartRedraw.forceNextRedraw = true;
      updateProfessionalDisplay();
      progressShown = false;
    }

    if (holdTime >= WIFI_RESET_HOLD_TIME && wifiResetTriggered) {
      // Long press - WiFi reset (already triggered)
      Serial.println("🔄 WiFi reset completed");
    } else if (holdTime >= 2000 && holdTime < WIFI_RESET_HOLD_TIME) {
      // Medium press (2-5 seconds) - Display reset
      Serial.printf("🖥️ Display reset triggered (%lums press)\n", holdTime);
      performDisplayReset();
    } else if (holdTime >= 100 && holdTime < 2000) {
      // Short press (0.1-2 seconds) - Theme toggle
      Serial.printf("🎨 Theme toggle triggered (%lums press)\n", holdTime);
      int newTheme = (currentTheme + 1) % 2; // Toggle between 0 and 1
      changeTheme(newTheme);
    } else if (holdTime < 100) {
      // Very short press - ignore (debounce)
      Serial.printf("🔘 Button press too short (%lums) - ignored\n", holdTime);
    }
    Serial.println("🔘 BOOT button released");
  } else if (currentButtonState && bootButtonPressed) {
    // Button is being held
    unsigned long holdTime = millis() - bootButtonPressStart;

    if (holdTime >= WIFI_RESET_HOLD_TIME && !wifiResetTriggered) {
      // Trigger WiFi reset
      wifiResetTriggered = true;
      Serial.println("🔄 WiFi reset triggered!");
      performWiFiReset();
    } else if (holdTime >= 3000 && holdTime < WIFI_RESET_HOLD_TIME) {
      // Show progress during hold (only after 3 seconds to avoid false triggers)
      int progress = map(holdTime, 3000, WIFI_RESET_HOLD_TIME, 0, 100);
      showWiFiResetProgress(progress);
      progressShown = true; // Mark that progress was shown

      // Log progress every second
      static unsigned long lastProgressLog = 0;
      if (millis() - lastProgressLog >= 1000) {
        Serial.printf("🔘 Hold progress: %d%% (%lums)\n", progress, holdTime);
        lastProgressLog = millis();
      }
    }
  }
}

void performDisplayReset() {
  Serial.println("🖥️ Starting display reset process...");

  // Clear screen and show reset message
  tft.fillScreen(COLOR_BG);

  // Header
  tft.fillRect(0, 0, 320, 30, COLOR_WARNING);
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_WARNING);
  tft.setTextSize(2);
  tft.setCursor(70, 8);
  tft.println("Display Reset");

  // Progress steps
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_BG);
  tft.setTextSize(1);

  int y = 50;

  // Step 1: Clear display cache
  tft.setCursor(10, y);
  tft.println("1. Clearing display cache...");
  y += 20;

  // Reset display variables
  needsFullRedraw = true;
  displayChanged = false;
  smartRedraw.forceNextRedraw = true;
  smartRedraw.initialized = false;

  // Reset card regions
  for (int i = 0; i < 4; i++) {
    cardRegions[i].dirty = true;
    cardRegions[i].lastUpdate = 0;
  }

  Serial.println("✓ Display cache cleared");
  delay(500);

  tft.setTextColor(COLOR_SUCCESS, COLOR_BG);
  tft.setCursor(20, y);
  tft.println("✓ Display cache cleared");
  y += 25;

  // Step 2: Reset display state
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_BG);
  tft.setCursor(10, y);
  tft.println("2. Resetting display state...");
  y += 20;

  // Reset animation and theme variables
  animationFrame = 0;
  themeChangeAnimation = false;
  themeAnimationFrame = 0;

  // Reset last values to force redraw
  lastPvPower = -1;
  lastBatteryVoltage = -1;
  lastOutputPower = -1;

  Serial.println("✓ Display state reset");
  delay(500);

  tft.setTextColor(COLOR_SUCCESS, COLOR_BG);
  tft.setCursor(20, y);
  tft.println("✓ Display state reset");
  y += 25;

  // Step 3: Reinitialize display
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_BG);
  tft.setCursor(10, y);
  tft.println("3. Reinitializing display...");
  y += 20;

  // Force complete redraw
  tft.fillScreen(COLOR_BG);
  smartRedraw.forceNextRedraw = true;
  updateProfessionalDisplay();

  Serial.println("✓ Display reinitialized");
  delay(500);

  tft.setTextColor(COLOR_SUCCESS, COLOR_BG);
  tft.setCursor(20, y);
  tft.println("✓ Display reinitialized");
  y += 25;

  // Completion message
  tft.setTextColor(COLOR_SUCCESS, COLOR_BG);
  tft.setTextSize(2);
  tft.setCursor(10, y);
  tft.println("Reset Complete!");
  y += 30;

  tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_BG);
  tft.setTextSize(1);
  tft.setCursor(10, y);
  tft.println("Display has been reset and");
  y += 15;
  tft.setCursor(10, y);
  tft.println("reinitialized successfully.");

  // Show completion for 2 seconds
  Serial.println("🖥️ Display reset completed successfully");
  delay(2000);

  // Return to normal display
  tft.fillScreen(COLOR_BG);
  smartRedraw.forceNextRedraw = true;
  updateProfessionalDisplay();
}

void performWiFiReset() {
  Serial.println("🔄 Starting WiFi reset process...");

  // Clear screen and show reset message
  tft.fillScreen(COLOR_BG);

  // Header
  tft.fillRect(0, 0, 320, 30, COLOR_ERROR);
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_ERROR);
  tft.setTextSize(2);
  tft.setCursor(80, 8);
  tft.println("WiFi Reset");

  // Progress steps
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_BG);
  tft.setTextSize(1);

  int y = 50;

  // Step 1: Clear WiFi credentials
  tft.setCursor(10, y);
  tft.println("1. Clearing WiFi credentials...");
  y += 20;

  WiFi.disconnect(true, true); // Disconnect and erase credentials
  Serial.println("✓ WiFi credentials cleared");
  delay(1000);

  tft.setTextColor(COLOR_SUCCESS, COLOR_BG);
  tft.setCursor(20, y);
  tft.println("✓ WiFi credentials cleared");
  y += 25;

  // Step 2: Reset WiFi settings
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_BG);
  tft.setCursor(10, y);
  tft.println("2. Resetting WiFi settings...");
  y += 20;

  // Clear WiFiManager settings
  WiFiManager wm;
  wm.resetSettings();
  Serial.println("✓ WiFiManager settings reset");
  delay(1000);

  tft.setTextColor(COLOR_SUCCESS, COLOR_BG);
  tft.setCursor(20, y);
  tft.println("✓ WiFi settings reset");
  y += 25;

  // Step 3: Restart message
  tft.setTextColor(COLOR_WARNING, COLOR_BG);
  tft.setTextSize(2);
  tft.setCursor(10, y);
  tft.println("Restarting...");
  y += 30;

  tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_BG);
  tft.setTextSize(1);
  tft.setCursor(10, y);
  tft.println("Device will restart and enter");
  y += 15;
  tft.setCursor(10, y);
  tft.println("WiFi setup mode.");
  y += 20;

  tft.setCursor(10, y);
  tft.println("Connect to 'DESS-Monitor' AP");
  y += 15;
  tft.setCursor(10, y);
  tft.println("and configure WiFi again.");

  // Countdown
  Serial.println("🔄 Restarting in 5 seconds...");
  for (int i = 5; i > 0; i--) {
    tft.fillRect(10, y + 30, 100, 20, COLOR_BG);
    tft.setTextColor(COLOR_ERROR, COLOR_BG);
    tft.setTextSize(2);
    tft.setCursor(10, y + 30);
    tft.printf("Restart in %d", i);
    Serial.printf("Restart in %d...\n", i);
    delay(1000);
  }

  // Restart
  Serial.println("🔄 Restarting ESP32...");
  ESP.restart();
}

void showWiFiResetProgress(int progress) {
  // Show progress bar at bottom of screen
  static unsigned long lastProgressUpdate = 0;

  // Limit update frequency
  if (millis() - lastProgressUpdate < 100) {
    return;
  }
  lastProgressUpdate = millis();

  // Clear progress area
  tft.fillRect(0, 220, 320, 20, COLOR_BG);

  // Progress bar background
  tft.fillRect(10, 225, 300, 10, COLOR_TEXT_SECONDARY);

  // Progress bar fill
  int progressWidth = map(progress, 0, 100, 0, 300);
  tft.fillRect(10, 225, progressWidth, 10, COLOR_WARNING);

  // Progress text
  tft.setTextColor(COLOR_WARNING, COLOR_BG);
  tft.setTextSize(1);
  tft.setCursor(10, 210);
  tft.printf("Hold BOOT button to reset WiFi... %d%%", progress);

  // Border
  tft.drawRect(10, 225, 300, 10, COLOR_TEXT_PRIMARY);
}

// ===== TOUCH FUNCTIONS =====
bool readTouch(TouchPoint& point) {
  if (ts.tirqTouched() && ts.touched()) {
    TS_Point p = ts.getPoint();

    // Map touch coordinates to screen coordinates (proper calibration)
    point.x = map(p.x, 200, 3700, 1, 320);  // Map X coordinate
    point.y = map(p.y, 240, 3800, 1, 240);  // Map Y coordinate
    point.pressure = p.z;
    point.pressed = true;
    point.timestamp = millis();

    // Debug touch detection
    Serial.printf("👆 XPT2046 Touch detected: raw(%d,%d,%d) -> screen(%d,%d)\n",
                  p.x, p.y, p.z, point.x, point.y);

    return true;
  }

  point.pressed = false;
  return false;
}

void updateTouch() {
  TouchPoint newTouch;

  if (readTouch(newTouch)) {
    // Touch detected
    if (!touchActive || millis() - lastDebounceTime > TOUCH_DEBOUNCE) {
      if (!touchActive) {
        // New touch started
        currentSwipe.start = newTouch;
        swipeInProgress = true;
        Serial.printf("👆 Touch started at (%d, %d)\n", newTouch.x, newTouch.y);
      }

      lastTouch = currentTouch;
      currentTouch = newTouch;
      touchActive = true;
      lastTouchTime = millis();
      lastDebounceTime = millis();

      // Update swipe end point
      if (swipeInProgress) {
        currentSwipe.end = newTouch;
        processSwipe();
      }
    }
  } else {
    // No touch detected
    if (touchActive) {
      // Touch ended
      if (swipeInProgress) {
        currentSwipe.end = currentTouch;
        currentSwipe.duration = millis() - currentSwipe.start.timestamp;
        processSwipe();

        if (isValidSwipe(currentSwipe)) {
          handleSwipeGesture(currentSwipe);
        }

        swipeInProgress = false;
      }

      Serial.printf("👆 Touch ended at (%d, %d)\n", currentTouch.x, currentTouch.y);
      touchActive = false;
    }
  }
}

void processSwipe() {
  if (!swipeInProgress) return;

  currentSwipe.deltaX = currentSwipe.end.x - currentSwipe.start.x;
  currentSwipe.deltaY = currentSwipe.end.y - currentSwipe.start.y;
  currentSwipe.duration = millis() - currentSwipe.start.timestamp;
  currentSwipe.direction = getSwipeDirection(currentSwipe.deltaX, currentSwipe.deltaY);
  currentSwipe.isValid = isValidSwipe(currentSwipe);
}

bool isValidSwipe(const SwipeGesture& swipe) {
  // Check minimum distance
  int16_t distance = abs(swipe.deltaX) > abs(swipe.deltaY) ? abs(swipe.deltaX) : abs(swipe.deltaY);
  if (distance < SWIPE_THRESHOLD) return false;

  // Check maximum duration
  if (swipe.duration > SWIPE_TIMEOUT) return false;

  // Check direction is valid
  if (swipe.direction == SwipeGesture::NONE) return false;

  return true;
}

SwipeGesture::Direction getSwipeDirection(int16_t deltaX, int16_t deltaY) {
  if (abs(deltaX) > abs(deltaY)) {
    // Horizontal swipe
    if (deltaX > SWIPE_THRESHOLD) return SwipeGesture::RIGHT;
    if (deltaX < -SWIPE_THRESHOLD) return SwipeGesture::LEFT;
  } else {
    // Vertical swipe
    if (deltaY > SWIPE_THRESHOLD) return SwipeGesture::DOWN;
    if (deltaY < -SWIPE_THRESHOLD) return SwipeGesture::UP;
  }
  return SwipeGesture::NONE;
}

void handleTouchInput() {
  // Debug: Check XPT2046 touch every call
  static unsigned long lastRawCheck = 0;
  if (millis() - lastRawCheck >= 500) { // Check every 500ms
    if (ts.tirqTouched() && ts.touched()) {
      TS_Point p = ts.getPoint();
      int screenX = map(p.x, 200, 3700, 1, 320);
      int screenY = map(p.y, 240, 3800, 1, 240);
      Serial.printf("🔍 XPT2046 RAW: raw(%d,%d,%d) -> screen(%d,%d)\n",
                    p.x, p.y, p.z, screenX, screenY);
    }
    lastRawCheck = millis();
  }

  updateTouch();

  // Handle brightness page touch first
  if (brightness.brightnessPageVisible && touchActive) {
    static unsigned long lastBrightnessTouch = 0;
    if (millis() - lastBrightnessTouch > 100) { // Reduced debounce for better responsiveness
      handleBrightnessPageTouch(currentTouch.x, currentTouch.y);
      lastBrightnessTouch = millis();
    }
  }
  // Handle brightness page touch end
  else if (brightness.brightnessPageVisible && !touchActive && lastTouch.pressed) {
    static unsigned long brightnesssTouchEndTime = 0;
    static bool lastBrightnessTouchState = false;

    // Only call touch end once per touch session
    if (lastBrightnessTouchState && !touchActive && millis() - brightnesssTouchEndTime > 100) {
      Serial.printf("💡 Brightness touch session ended at (%d,%d)\n", lastTouch.x, lastTouch.y);

      // Call brightness touch end handler
      handleBrightnessPageTouchEnd();

      brightnesssTouchEndTime = millis();
      lastBrightnessTouchState = false;
    } else if (touchActive) {
      lastBrightnessTouchState = true;
    }
  }
  // Handle settings page touch - professional touch handling
  else if (settingsPage.isVisible && touchActive) {
    // Handle settings page touch with ultra-responsive performance
    static unsigned long lastSettingsTouch = 0;
    if (millis() - lastSettingsTouch > 16) { // 60fps touch response
      handleSettingsPageTouch(currentTouch.x, currentTouch.y);
      lastSettingsTouch = millis();
    }
  }
  // Handle touch end for settings page - professional single tap detection
  else if (settingsPage.isVisible && !touchActive && lastTouch.pressed) {
    // Touch ended - handle single tap or finalize scrolling
    static unsigned long touchEndTime = 0;
    static bool lastTouchState = false;

    // Only call touch end once per touch session
    if (lastTouchState && !touchActive && millis() - touchEndTime > 100) {
      Serial.printf("📱 Professional touch session ended at (%d,%d)\n", lastTouch.x, lastTouch.y);

      // Call professional touch end handler
      handleProfessionalTouchEnd();

      touchEndTime = millis();
      lastTouchState = false;
    } else if (touchActive) {
      lastTouchState = true;
    }
  }

  // Handle single touch (for future use)
  if (touchActive && currentTouch.timestamp > lastTouchTime + 100) {
    // Single touch handling can be added here
  }
}

void handleSwipeGesture(const SwipeGesture& swipe) {
  // Check if settings page is visible - different behavior
  if (settingsPage.isVisible) {
    switch (swipe.direction) {
      case SwipeGesture::UP:
        Serial.println("👆 Swipe UP - Close settings page");
        hideSettingsPage();
        break;
      default:
        // Ignore other swipes when settings is open
        break;
    }
    return;
  }

  // Normal swipe behavior when settings is not visible
  // Prevent rapid actions
  if (millis() - lastThemeChange < 1000) return;

  switch (swipe.direction) {
    case SwipeGesture::LEFT:
      Serial.println("👈 Swipe LEFT - Next theme");
      changeTheme((currentTheme + 1) % 2);
      break;

    case SwipeGesture::RIGHT:
      Serial.println("👉 Swipe RIGHT - Previous theme");
      changeTheme((currentTheme - 1 + 2) % 2);
      break;

    case SwipeGesture::UP:
      Serial.println("👆 Swipe UP - Force refresh");
      // Force immediate data refresh and full redraw
      Serial.println("🔄 Forcing immediate data refresh");
      fetchInverterData(); // Fetch fresh data immediately
      smartRedraw.forceNextRedraw = true;
      updateProfessionalDisplay(); // Update display immediately
      lastThemeChange = millis();
      break;

    case SwipeGesture::DOWN:
      Serial.println("👇 Swipe DOWN - Show settings page");
      showSettingsPage();
      lastThemeChange = millis();
      break;

    default:
      break;
  }
}

// ===== ANTI-FLICKER DISPLAY FUNCTIONS =====
void updateDisplaySmooth() {
  // Use the new advanced anti-flicker system
  updateProfessionalDisplay();
}

// ===== ADVANCED ANTI-FLICKER FUNCTIONS =====

void drawOptimizedFullScreen() {
  Serial.println("🎨 Optimized full screen redraw");

  // Clear background efficiently - no fillScreen()
  tft.fillRect(0, 0, 320, 240, COLOR_BG);

  // Draw all components in optimal order
  drawHeaderSmooth();

  // Draw all cards
  for (int i = 0; i < 4; i++) {
    drawCardSmooth(i);
    cardRegions[i].dirty = false;
    cardRegions[i].lastUpdate = millis();
  }

  // No need to redraw settings icon - new settings page system
}

void drawHeaderSmooth() {
  // NO CLEAR - Use text overlay to prevent flicker
  // Only clear background during full redraw

  // Top status bar with essential info
  tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_BG);
  tft.setTextSize(1);

  // Last update time (left side) - clear only text area
  tft.fillRect(5, 3, 80, 8, COLOR_BG);
  tft.setCursor(5, 3);
  if (data.dataValid) {
    tft.printf("Last: %s", formatTime(data.timestamp).c_str());
  } else {
    tft.print("Last: --:--");
  }

  // Device Name (center) - always redraw to prevent disappearing
  tft.fillRect(90, 3, 120, 8, COLOR_BG); // Clear device name area
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_BG);
  tft.setCursor(90, 3);
  tft.print("POWMR 6.2KW");

  // WiFi signal strength (right side) - clear only text area
  tft.fillRect(240, 3, 75, 8, COLOR_BG);
  tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_BG);
  tft.setCursor(240, 3);
  if (wifiConnected) {
    int rssi = WiFi.RSSI();
    tft.printf("WiFi: %ddBm", rssi);

    // Signal quality indicator - clear only dot area
    tft.fillCircle(315, 6, 3, COLOR_BG);
    uint16_t signalColor;
    if (rssi > -50) signalColor = COLOR_SUCCESS;
    else if (rssi > -70) signalColor = COLOR_WARNING;
    else signalColor = COLOR_ERROR;

    tft.fillCircle(315, 6, 2, signalColor);
  } else {
    tft.print("WiFi: OFF");
    tft.fillCircle(315, 6, 3, COLOR_BG);
    tft.fillCircle(315, 6, 2, COLOR_ERROR);
  }

  // Separator line - draw only once
  static bool separatorDrawn = false;
  if (!separatorDrawn) {
    tft.drawFastHLine(0, 12, 320, COLOR_TEXT_DIM);
    separatorDrawn = true;
  }
}

void drawCardSmooth(int cardIndex) {
  if (cardIndex < 0 || cardIndex > 3) return;

  DisplayRegion& region = cardRegions[cardIndex];

  // Check if this is a full redraw (first time or theme change)
  if (smartRedraw.forceNextRedraw || !smartRedraw.initialized) {
    // Full card redraw with frame and decorations
    clearCardArea(cardIndex);
    drawOptimizedCard(region.x, region.y, region.w, region.h, cardIndex);
    Serial.printf("🎯 Card %d full redraw with frame\n", cardIndex);
  } else {
    // Only update numbers, keep frame intact
    updateCardNumbers(region.x, region.y, region.w, region.h, cardIndex);
    Serial.printf("🎯 Card %d numbers updated smoothly\n", cardIndex);
  }
}

void updateCardNumbers(int x, int y, int w, int h, int cardType) {
  switch (cardType) {
    case 0: // Solar card
      updateSolarNumbers(x, y, w, h);
      break;
    case 1: // Grid card
      updateGridNumbers(x, y, w, h);
      break;
    case 2: // Battery card
      updateBatteryNumbers(x, y, w, h);
      break;
    case 3: // Load card
      updateLoadNumbers(x, y, w, h);
      break;
  }
}

void clearCardArea(int cardIndex) {
  DisplayRegion& region = cardRegions[cardIndex];

  // Clear only the card area, not the entire screen
  tft.fillRect(region.x, region.y, region.w, region.h, COLOR_BG);
}

void drawOptimizedCard(int x, int y, int w, int h, int cardType) {
  switch (cardType) {
    case 0: // Solar card
      drawOptimizedSolarCard(x, y, w, h);
      break;
    case 1: // Grid card
      drawOptimizedGridCard(x, y, w, h);
      break;
    case 2: // Battery card
      drawOptimizedBatteryCard(x, y, w, h);
      break;
    case 3: // Load card
      drawOptimizedLoadCard(x, y, w, h);
      break;
  }
}

// ===== NUMBER UPDATE FUNCTIONS (NO FLICKER) =====

void updateSolarNumbers(int x, int y, int w, int h) {
  // Clear complete areas to prevent text overlap - preserve card background
  // IMPORTANT: Only clear text areas, NOT the entire card to preserve frames

  // Main power value area - clear only text area (smaller region)
  tft.fillRect(x + 10, y + 35, w - 20, 30, COLOR_CARD_BG);
  String powerStr;
  if (data.pvPower >= 1000) {
    powerStr = String(data.pvPower / 1000.0, 1) + "kW";
  } else {
    powerStr = String((int)data.pvPower) + "W";
  }
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_CARD_BG);
  tft.setTextSize(3);
  tft.setCursor(x + 10, y + 35);  // Match fillRect position (x+10)
  tft.print(powerStr);

  // Voltage area - clear only text area (smaller region)
  tft.fillRect(x + 10, y + h - 30, w - 20, 12, COLOR_CARD_BG);
  String voltageStr = "Voltage: " + String(data.pvVoltage, 1) + "V";
  tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_CARD_BG);
  tft.setTextSize(1);
  tft.setCursor(x + 10, y + h - 30);  // Same position as fillRect
  tft.print(voltageStr);

  // Current area - clear complete area (same x position)
  tft.fillRect(x + 5, y + h - 20, w - 10, 15, COLOR_CARD_BG);
  String currentStr = "Current: " + String(data.pvCurrent, 2) + "A";
  tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_CARD_BG);
  tft.setTextSize(1);
  tft.setCursor(x + 10, y + h - 15);  // Same x position as voltage (x+10)
  tft.print(currentStr);
}

void updateGridNumbers(int x, int y, int w, int h) {
  // Clear complete areas to prevent text overlap - preserve card background
  // IMPORTANT: Only clear text areas, NOT the entire card to preserve frames

  // Main power value area - clear only text area (smaller region)
  tft.fillRect(x + 10, y + 35, w - 20, 30, COLOR_CARD_BG);
  String powerStr;
  if (data.gridPower >= 1000) {
    powerStr = String(data.gridPower / 1000.0, 1) + "kW";
  } else {
    powerStr = String((int)data.gridPower) + "W";
  }
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_CARD_BG);
  tft.setTextSize(3);
  tft.setCursor(x + 10, y + 35);  // Match fillRect position (x+10)
  tft.print(powerStr);

  // Frequency area - clear only text area (smaller region)
  tft.fillRect(x + 10, y + h - 30, w - 20, 12, COLOR_CARD_BG);
  tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_CARD_BG);
  tft.setTextSize(1);
  tft.setCursor(x + 10, y + h - 30);  // Same position as fillRect
  tft.printf("Frequency: %.2fHz", data.gridFrequency);

  // Voltage area - clear complete area (same x position)
  tft.fillRect(x + 5, y + h - 20, w - 10, 15, COLOR_CARD_BG);
  tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_CARD_BG);
  tft.setTextSize(1);
  tft.setCursor(x + 10, y + h - 15);  // Same x position as frequency (x+10)
  tft.printf("Voltage: %.0fV", data.gridVoltage);
}

void updateBatteryNumbers(int x, int y, int w, int h) {
  // Clear complete areas to prevent text overlap - preserve card background
  // IMPORTANT: Only clear text areas, NOT the entire card to preserve frames

  // Main voltage value area - clear only text area (smaller region)
  tft.fillRect(x + 10, y + 35, w - 20, 30, COLOR_CARD_BG);
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_CARD_BG);
  tft.setTextSize(3);
  tft.setCursor(x + 10, y + 35);  // Match fillRect position (x+10)
  tft.printf("%.1fV", data.batteryVoltage);

  // Current area - clear only text area (smaller region)
  tft.fillRect(x + 10, y + h - 30, w - 20, 12, COLOR_CARD_BG);
  tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_CARD_BG);
  tft.setTextSize(1);
  tft.setCursor(x + 10, y + h - 30);  // Same position as fillRect
  tft.printf("Current: %.2fA", data.batteryCurrent);

  // Battery status with color indicator - clear complete area (same x position)
  float battLevel = ((data.batteryVoltage - 48.0) / (58.4 - 48.0)) * 100;
  battLevel = constrain(battLevel, 0, 100);
  tft.fillRect(x + 5, y + h - 20, w - 10, 15, COLOR_CARD_BG);

  // Determine battery status color: Green (>60%), Yellow (30-60%), Red (<30%)
  uint16_t statusColor;
  String statusText;
  if (battLevel > 60) {
    statusColor = COLOR_SUCCESS;  // Green
    statusText = "Status: Good";
  } else if (battLevel > 30) {
    statusColor = COLOR_WARNING;  // Yellow
    statusText = "Status: Low";
  } else {
    statusColor = COLOR_ERROR;    // Red
    statusText = "Status: Critical";
  }

  tft.setTextColor(statusColor, COLOR_CARD_BG);
  tft.setTextSize(1);
  tft.setCursor(x + 10, y + h - 15);  // Same x position as current (x+10)
  tft.print(statusText);
}

void updateLoadNumbers(int x, int y, int w, int h) {
  // Clear complete areas to prevent text overlap - preserve card background
  // IMPORTANT: Only clear text areas, NOT the entire card to preserve frames

  // Main power value area - clear only text area (smaller region)
  tft.fillRect(x + 10, y + 35, w - 20, 30, COLOR_CARD_BG);
  String powerStr;
  if (data.outputPower >= 1000) {
    powerStr = String(data.outputPower / 1000.0, 1) + "kW";
  } else {
    powerStr = String((int)data.outputPower) + "W";
  }
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_CARD_BG);
  tft.setTextSize(3);
  tft.setCursor(x + 10, y + 35);  // Match fillRect position (x+10)
  tft.print(powerStr);

  // Current area - clear only text area (smaller region)
  tft.fillRect(x + 10, y + h - 30, w - 20, 12, COLOR_CARD_BG);
  tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_CARD_BG);
  tft.setTextSize(1);
  tft.setCursor(x + 10, y + h - 30);  // Same position as fillRect
  tft.printf("Current: %.1fA", data.outputCurrent);

  // Load percentage area - clear only text area (same position)
  tft.fillRect(x + 10, y + h - 18, w - 20, 12, COLOR_CARD_BG);
  tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_CARD_BG);
  tft.setTextSize(1);
  tft.setCursor(x + 10, y + h - 18);  // Same position as current (x+10)
  tft.printf("Load Level: %.0f%%", data.loadPercent);
}

// ===== OPTIMIZED CARD DRAWING FUNCTIONS =====

void drawOptimizedSolarCard(int x, int y, int w, int h) {
  // Enhanced card background with gradient shadow
  tft.fillRoundRect(x + 3, y + 3, w, h, 12, 0x1082); // Deep shadow
  tft.fillRoundRect(x + 2, y + 2, w, h, 12, 0x2104); // Mid shadow
  tft.fillRoundRect(x, y, w, h, 12, COLOR_CARD_BG);

  // Gradient border effect
  tft.drawRoundRect(x, y, w, h, 12, COLOR_SOLAR);
  tft.drawRoundRect(x + 1, y + 1, w - 2, h - 2, 11, COLOR_SOLAR_GLOW);
  tft.drawRoundRect(x + 2, y + 2, w - 4, h - 4, 10, COLOR_TEXT_SECONDARY);

  // Enhanced solar icon with glow
  uint16_t sunColor = (data.pvPower > 10) ? COLOR_SOLAR : 0x8410;
  tft.fillCircle(x + 22, y + 18, 10, COLOR_SOLAR_GLOW);
  tft.fillCircle(x + 22, y + 18, 8, sunColor);
  tft.drawCircle(x + 22, y + 18, 9, COLOR_SOLAR);

  // Enhanced sun rays with varying lengths
  for (int i = 0; i < 8; i++) {
    float angle = i * 45 * PI / 180;
    int rayLength = (i % 2 == 0) ? 18 : 15; // Alternating ray lengths
    int x1 = x + 22 + cos(angle) * 12;
    int y1 = y + 18 + sin(angle) * 12;
    int x2 = x + 22 + cos(angle) * rayLength;
    int y2 = y + 18 + sin(angle) * rayLength;
    tft.drawLine(x1, y1, x2, y2, sunColor);
  }

  // Enhanced title with better positioning
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_CARD_BG);
  tft.setTextSize(1);
  tft.setCursor(x + 45, y + 12);
  tft.print("SOLAR POWER");

  // Decorative accent line
  tft.fillRect(x + 45, y + 22, w - 55, 2, COLOR_SOLAR);
  tft.fillRect(x + 45, y + 23, w - 60, 1, COLOR_SOLAR_GLOW);

  // Main power value with better spacing
  String powerStr;
  if (data.pvPower >= 1000) {
    powerStr = String(data.pvPower / 1000.0, 1) + "kW";
  } else {
    powerStr = String((int)data.pvPower) + "W";
  }

  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_CARD_BG);
  tft.setTextSize(3);
  tft.setCursor(x + 10, y + 35);
  tft.print(powerStr);

  // Enhanced info display with better spacing and larger text
  String voltageStr = "Voltage: " + String(data.pvVoltage, 1) + "V";
  String currentStr = "Current: " + String(data.pvCurrent, 2) + "A";

  tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_CARD_BG);
  tft.setTextSize(1);
  tft.setCursor(x + 10, y + h - 25);
  tft.print(voltageStr);
  tft.setCursor(x + 10, y + h - 15);
  tft.print(currentStr);

  // Enhanced status indicator with animated effect
  uint16_t statusColor = (data.pvPower > 10) ? COLOR_SOLAR : COLOR_TEXT_DIM;
  tft.fillCircle(x + w - 18, y + 18, 6, COLOR_CARD_BG);
  tft.fillCircle(x + w - 18, y + 18, 5, statusColor);
  if (data.pvPower > 10) {
    tft.drawCircle(x + w - 18, y + 18, 7, statusColor);
    tft.drawCircle(x + w - 18, y + 18, 8, COLOR_SOLAR_GLOW);
  }

  // Vertical solar power bars at bottom-right corner - show one bar at a time
  int activeBars = 0;
  if (data.pvPower > 0) activeBars = 1;
  if (data.pvPower > 50) activeBars = 2;
  if (data.pvPower > 100) activeBars = 3;
  if (data.pvPower > 150) activeBars = 4;

  for (int i = 0; i < 4; i++) {
    int barHeight = 3 + i * 3; // Start small, get bigger: 3, 6, 9, 12 pixels
    uint16_t barColor = (i < activeBars) ? statusColor : COLOR_TEXT_DIM;
    int barX = x + w - 25 + i * 4; // Position at bottom-right
    int barY = y + h - 8; // Bottom of card

    tft.fillRect(barX, barY - barHeight, 3, barHeight, barColor);
    tft.drawRect(barX, barY - barHeight, 3, barHeight, COLOR_SOLAR);
  }
}

void drawOptimizedGridCard(int x, int y, int w, int h) {
  // Enhanced card background with gradient shadow
  tft.fillRoundRect(x + 3, y + 3, w, h, 12, 0x1082); // Deep shadow
  tft.fillRoundRect(x + 2, y + 2, w, h, 12, 0x2104); // Mid shadow
  tft.fillRoundRect(x, y, w, h, 12, COLOR_CARD_BG);

  // Gradient border effect
  tft.drawRoundRect(x, y, w, h, 12, COLOR_GRID);
  tft.drawRoundRect(x + 1, y + 1, w - 2, h - 2, 11, COLOR_GRID_GLOW);
  tft.drawRoundRect(x + 2, y + 2, w - 4, h - 4, 10, COLOR_TEXT_SECONDARY);

  // Enhanced electrical tower icon
  uint16_t towerColor = (data.gridVoltage > 220 && data.gridVoltage < 240) ? COLOR_GRID : COLOR_WARNING;

  // Tower base and structure
  tft.drawLine(x + 22, y + 10, x + 22, y + 25, towerColor);
  tft.drawLine(x + 16, y + 15, x + 28, y + 15, towerColor);
  tft.drawLine(x + 18, y + 12, x + 26, y + 12, towerColor);
  tft.drawLine(x + 18, y + 18, x + 26, y + 18, towerColor);

  // Power lines
  tft.drawLine(x + 14, y + 12, x + 16, y + 12, towerColor);
  tft.drawLine(x + 28, y + 12, x + 30, y + 12, towerColor);

  // Tower glow effect
  tft.drawCircle(x + 22, y + 17, 12, COLOR_GRID_GLOW);

  // Enhanced title with better positioning
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_CARD_BG);
  tft.setTextSize(1);
  tft.setCursor(x + 45, y + 12);
  tft.print("GRID SUPPLY");

  // Decorative accent line
  tft.fillRect(x + 45, y + 22, w - 55, 2, COLOR_GRID);
  tft.fillRect(x + 45, y + 23, w - 60, 1, COLOR_GRID_GLOW);

  // Main power value with better spacing
  String powerStr;
  if (data.gridPower >= 1000) {
    powerStr = String(data.gridPower / 1000.0, 1) + "kW";
  } else {
    powerStr = String((int)data.gridPower) + "W";
  }
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_CARD_BG);
  tft.setTextSize(3);
  tft.setCursor(x + 10, y + 35);
  tft.print(powerStr);

  // Enhanced info display with better spacing and larger text
  String voltageStr = "Voltage: " + String(data.gridVoltage, 0) + "V";
  String freqStr = "Frequency: " + String(data.gridFrequency, 2) + "Hz";

  tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_CARD_BG);
  tft.setTextSize(1);
  tft.setCursor(x + 10, y + h - 25);
  tft.print(voltageStr);
  tft.setCursor(x + 10, y + h - 15);
  tft.print(freqStr);

  // Enhanced status indicator with signal strength
  uint16_t statusColor = (data.gridVoltage > 220 && data.gridVoltage < 240) ? COLOR_SUCCESS : COLOR_WARNING;

  // Vertical signal bars at bottom-right corner - show one bar at a time
  int activeBars = 0;
  if (data.gridPower > 0) activeBars = 1;
  if (data.gridPower > 300) activeBars = 2;
  if (data.gridPower > 600) activeBars = 3;
  if (data.gridPower > 900) activeBars = 4;

  for (int i = 0; i < 4; i++) {
    int barHeight = 3 + i * 3; // Start small, get bigger: 3, 6, 9, 12 pixels
    uint16_t barColor = (i < activeBars) ? statusColor : COLOR_TEXT_DIM;
    int barX = x + w - 25 + i * 4; // Position at bottom-right
    int barY = y + h - 8; // Bottom of card

    tft.fillRect(barX, barY - barHeight, 3, barHeight, barColor);
    tft.drawRect(barX, barY - barHeight, 3, barHeight, COLOR_GRID);
  }

  // Status circle moved in front of signal bars
  tft.fillCircle(x + w - 18, y + 18, 6, COLOR_CARD_BG);
  tft.fillCircle(x + w - 18, y + 18, 5, statusColor);
}

void drawOptimizedBatteryCard(int x, int y, int w, int h) {
  float battLevel = ((data.batteryVoltage - 48.0) / (58.4 - 48.0)) * 100;
  battLevel = constrain(battLevel, 0, 100);
  uint16_t battColor = getEnhancedBatteryColor(battLevel);

  // Enhanced card background with gradient shadow
  tft.fillRoundRect(x + 3, y + 3, w, h, 12, 0x1082); // Deep shadow
  tft.fillRoundRect(x + 2, y + 2, w, h, 12, 0x2104); // Mid shadow
  tft.fillRoundRect(x, y, w, h, 12, COLOR_CARD_BG);

  // Gradient border effect
  tft.drawRoundRect(x, y, w, h, 12, battColor);
  tft.drawRoundRect(x + 1, y + 1, w - 2, h - 2, 11, COLOR_BATTERY_GOOD);
  tft.drawRoundRect(x + 2, y + 2, w - 4, h - 4, 10, COLOR_TEXT_SECONDARY);

  // Enhanced battery icon with 3D effect
  tft.fillRect(x + 20, y + 12, 18, 12, COLOR_CARD_BG);
  tft.drawRect(x + 20, y + 12, 18, 12, battColor);
  tft.drawRect(x + 19, y + 11, 20, 14, battColor);
  tft.fillRect(x + 38, y + 15, 3, 6, battColor);

  // Enhanced battery level fill with gradient effect
  int fillWidth = (battLevel / 100.0) * 16;
  if (fillWidth > 0) {
    tft.fillRect(x + 21, y + 13, fillWidth, 10, battColor);
    // Add highlight effect
    tft.fillRect(x + 21, y + 13, fillWidth, 2, COLOR_BATTERY_GOOD);
  }

  // Battery percentage text inside icon
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_CARD_BG);
  tft.setTextSize(1);
  String percentStr = String((int)battLevel) + "%";
  tft.setCursor(x + 23, y + 16);
  tft.print(percentStr);

  // Enhanced title with better positioning
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_CARD_BG);
  tft.setTextSize(1);
  tft.setCursor(x + 45, y + 12);
  tft.print("BATTERY BANK");

  // Decorative accent line
  tft.fillRect(x + 45, y + 22, w - 55, 2, battColor);
  tft.fillRect(x + 45, y + 23, w - 60, 1, COLOR_BATTERY_GOOD);

  // Main voltage value with better spacing
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_CARD_BG);
  tft.setTextSize(3);
  tft.setCursor(x + 10, y + 35);
  tft.printf("%.1fV", data.batteryVoltage);

  // Enhanced info display with better spacing and larger text
  String currentStr = "Current: " + String(data.batteryCurrent, 2) + "A";

  // Determine battery status color and text: Green (>60%), Yellow (30-60%), Red (<30%)
  uint16_t statusColor;
  String statusText;
  if (battLevel > 60) {
    statusColor = COLOR_SUCCESS;  // Green
    statusText = "Status: Good";
  } else if (battLevel > 30) {
    statusColor = COLOR_WARNING;  // Yellow
    statusText = "Status: Low";
  } else {
    statusColor = COLOR_ERROR;    // Red
    statusText = "Status: Critical";
  }

  tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_CARD_BG);
  tft.setTextSize(1);
  tft.setCursor(x + 10, y + h - 25);
  tft.print(currentStr);

  tft.setTextColor(statusColor, COLOR_CARD_BG);
  tft.setTextSize(1);
  tft.setCursor(x + 10, y + h - 15);
  tft.print(statusText);

  // Enhanced status indicator with charging animation
  tft.fillCircle(x + w - 18, y + 18, 6, COLOR_CARD_BG);
  tft.fillCircle(x + w - 18, y + 18, 5, statusColor);

  // Vertical battery level bars at bottom-right corner - show one bar at a time
  int activeBars = 0;
  if (battLevel > 20) activeBars = 1;
  if (battLevel > 40) activeBars = 2;
  if (battLevel > 60) activeBars = 3;
  if (battLevel > 80) activeBars = 4;

  for (int i = 0; i < 4; i++) {
    int barHeight = 3 + i * 2; // Start small, get bigger: 3, 5, 7, 9 pixels
    uint16_t barColor = (i < activeBars) ? statusColor : COLOR_TEXT_DIM;
    int barX = x + w - 25 + i * 4; // Position at bottom-right
    int barY = y + h - 8; // Bottom of card

    tft.fillRect(barX, barY - barHeight, 3, barHeight, barColor);
    tft.drawRect(barX, barY - barHeight, 3, barHeight, statusColor);
  }
}

void drawOptimizedLoadCard(int x, int y, int w, int h) {
  // Enhanced card background with gradient shadow
  tft.fillRoundRect(x + 3, y + 3, w, h, 12, 0x1082); // Deep shadow
  tft.fillRoundRect(x + 2, y + 2, w, h, 12, 0x2104); // Mid shadow
  tft.fillRoundRect(x, y, w, h, 12, COLOR_CARD_BG);

  // Gradient border effect
  tft.drawRoundRect(x, y, w, h, 12, COLOR_OUTPUT);
  tft.drawRoundRect(x + 1, y + 1, w - 2, h - 2, 11, COLOR_OUTPUT_GLOW);
  tft.drawRoundRect(x + 2, y + 2, w - 4, h - 4, 10, COLOR_TEXT_SECONDARY);

  // Enhanced electrical outlet icon with 3D effect
  uint16_t plugColor = (data.outputPower > 100) ? COLOR_OUTPUT : COLOR_TEXT_DIM;

  // Outlet face
  tft.fillRect(x + 18, y + 12, 12, 10, COLOR_CARD_BG);
  tft.drawRect(x + 18, y + 12, 12, 10, plugColor);
  tft.drawRect(x + 17, y + 11, 14, 12, plugColor);

  // Outlet holes
  tft.fillRect(x + 21, y + 15, 2, 2, plugColor);
  tft.fillRect(x + 25, y + 15, 2, 2, plugColor);
  tft.fillRect(x + 23, y + 18, 2, 2, plugColor);

  // Power cord
  tft.drawLine(x + 24, y + 22, x + 24, y + 26, plugColor);
  tft.drawLine(x + 24, y + 26, x + 28, y + 26, plugColor);

  // Glow effect around outlet
  tft.drawCircle(x + 24, y + 17, 10, COLOR_OUTPUT_GLOW);

  // Enhanced title with better positioning
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_CARD_BG);
  tft.setTextSize(1);
  tft.setCursor(x + 45, y + 12);
  tft.print("LOAD OUTPUT");

  // Decorative accent line
  tft.fillRect(x + 45, y + 22, w - 55, 2, COLOR_OUTPUT);
  tft.fillRect(x + 45, y + 23, w - 60, 1, COLOR_OUTPUT_GLOW);

  // Main power value with better spacing
  String powerStr;
  if (data.outputPower >= 1000) {
    powerStr = String(data.outputPower / 1000.0, 1) + "kW";
  } else {
    powerStr = String((int)data.outputPower) + "W";
  }

  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_CARD_BG);
  tft.setTextSize(3);
  tft.setCursor(x + 10, y + 35);
  tft.print(powerStr);

  // Enhanced info display with better spacing and larger text
  String currentStr = "Current: " + String(data.outputCurrent, 1) + "A";
  String loadStr = "Load Level: " + String(data.loadPercent, 0) + "%";

  tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_CARD_BG);
  tft.setTextSize(1);
  tft.setCursor(x + 10, y + h - 25);
  tft.print(currentStr);
  tft.setCursor(x + 10, y + h - 15);
  tft.print(loadStr);

  // Enhanced status indicator with load animation
  uint16_t statusColor = (data.loadPercent > 80) ? COLOR_ERROR :
                        (data.loadPercent > 50) ? COLOR_WARNING : COLOR_SUCCESS;
  tft.fillCircle(x + w - 18, y + 18, 6, COLOR_CARD_BG);
  tft.fillCircle(x + w - 18, y + 18, 5, statusColor);

  // Vertical load level bars at bottom-right corner - show one bar at a time
  int activeBars = 0;
  if (data.loadPercent > 0) activeBars = 1;
  if (data.loadPercent > 25) activeBars = 2;
  if (data.loadPercent > 50) activeBars = 3;
  if (data.loadPercent > 75) activeBars = 4;

  for (int i = 0; i < 4; i++) {
    int barHeight = 3 + i * 2; // Start small, get bigger: 3, 5, 7, 9 pixels
    uint16_t barColor = (i < activeBars) ? statusColor : COLOR_TEXT_DIM;
    int barX = x + w - 25 + i * 4; // Position at bottom-right
    int barY = y + h - 8; // Bottom of card

    tft.fillRect(barX, barY - barHeight, 3, barHeight, barColor);
    tft.drawRect(barX, barY - barHeight, 3, barHeight, COLOR_OUTPUT);
  }
}

// ===== SMOOTH DRAWING HELPER FUNCTIONS =====

void drawTextSmooth(int x, int y, const String& text, uint16_t color, uint8_t size, uint16_t bgColor) {
  tft.setTextColor(color, bgColor);
  tft.setTextSize(size);
  tft.setCursor(x, y);
  tft.print(text);
}

void fillRectSmooth(int x, int y, int w, int h, uint16_t color) {
  tft.fillRect(x, y, w, h, color);
}

// ===== SETTINGS MENU FUNCTIONS =====

// OLD SETTINGS MENU FUNCTIONS REMOVED

// OLD SETTINGS MENU FUNCTIONS REMOVED

// OLD SETTINGS MENU FUNCTIONS REMOVED

void drawSettingsIcon(int x, int y, uint16_t color) {
  // Draw gear icon (settings)
  int centerX = x;
  int centerY = y;
  int outerRadius = 12;
  int innerRadius = 6;
  int teethCount = 8;

  // Draw gear teeth
  for (int i = 0; i < teethCount; i++) {
    float angle1 = (i * 360.0 / teethCount) * PI / 180.0;
    float angle2 = ((i + 0.3) * 360.0 / teethCount) * PI / 180.0;
    float angle3 = ((i + 0.7) * 360.0 / teethCount) * PI / 180.0;
    float angle4 = ((i + 1) * 360.0 / teethCount) * PI / 180.0;

    // Outer tooth
    int x1 = centerX + cos(angle1) * outerRadius;
    int y1 = centerY + sin(angle1) * outerRadius;
    int x2 = centerX + cos(angle2) * outerRadius;
    int y2 = centerY + sin(angle2) * outerRadius;
    int x3 = centerX + cos(angle3) * outerRadius;
    int y3 = centerY + sin(angle3) * outerRadius;
    int x4 = centerX + cos(angle4) * outerRadius;
    int y4 = centerY + sin(angle4) * outerRadius;

    // Draw tooth outline
    tft.drawLine(x1, y1, x2, y2, color);
    tft.drawLine(x2, y2, x3, y3, color);
    tft.drawLine(x3, y3, x4, y4, color);
  }

  // Draw inner circle
  tft.drawCircle(centerX, centerY, innerRadius, color);
  tft.drawCircle(centerX, centerY, innerRadius - 1, color);

  // Draw center hole
  tft.fillCircle(centerX, centerY, 3, COLOR_CARD_BG);
  tft.drawCircle(centerX, centerY, 3, color);
}

// ===== NEW SETTINGS SYSTEM FUNCTIONS =====

// OLD SETTINGS MENU FUNCTIONS REMOVED

// OLD SETTINGS MENU FUNCTIONS REMOVED

void pauseDataFetching() {
  if (dataFetchingPaused) return;

  dataFetchingPaused = true;
  dataFetchingPausedTime = millis();

  Serial.println("⏸️ Data fetching paused");
}

void resumeDataFetching() {
  if (!dataFetchingPaused) return;

  dataFetchingPaused = false;

  unsigned long pausedDuration = millis() - dataFetchingPausedTime;
  Serial.printf("▶️ Data fetching resumed (paused for %lu ms)\n", pausedDuration);

  // Force immediate data refresh when resuming
  lastDataUpdate = 0; // This will trigger immediate fetch on next loop
}

// ===== MODERN SETTINGS ICON FUNCTIONS =====

void drawModernSettingsIcon() {
  // Modern settings icon position
  int iconX = 290;
  int iconY = 25;
  int iconW = 28;
  int iconH = 20;

  // Modern rounded rectangle background with gradient
  // Shadow layers for depth
  tft.fillRoundRect(iconX + 2, iconY + 2, iconW, iconH, 8, 0x2104); // Deep shadow
  tft.fillRoundRect(iconX + 1, iconY + 1, iconW, iconH, 8, 0x4208); // Mid shadow

  // Main background with gradient effect
  tft.fillRoundRect(iconX, iconY, iconW, iconH, 8, COLOR_CARD_BG);

  // Gradient border effect
  tft.drawRoundRect(iconX, iconY, iconW, iconH, 8, COLOR_ACCENT);
  tft.drawRoundRect(iconX + 1, iconY + 1, iconW - 2, iconH - 2, 7, COLOR_SUCCESS);

  // Modern gear icon inside
  int gearX = iconX + iconW/2;
  int gearY = iconY + iconH/2;

  // Draw modern gear with clean lines
  // Outer gear teeth (8 teeth)
  for (int i = 0; i < 8; i++) {
    float angle = i * 45 * PI / 180;
    int x1 = gearX + cos(angle) * 6;
    int y1 = gearY + sin(angle) * 6;
    int x2 = gearX + cos(angle) * 8;
    int y2 = gearY + sin(angle) * 8;

    // Draw tooth as thick line
    tft.drawLine(x1, y1, x2, y2, COLOR_ACCENT);
    tft.drawLine(x1-1, y1, x2-1, y2, COLOR_ACCENT);
  }

  // Inner circle
  tft.fillCircle(gearX, gearY, 4, COLOR_ACCENT);
  tft.fillCircle(gearX, gearY, 3, COLOR_CARD_BG);

  // Center hole
  tft.fillCircle(gearX, gearY, 1, COLOR_ACCENT);

  // Add subtle glow effect
  tft.drawRoundRect(iconX - 1, iconY - 1, iconW + 2, iconH + 2, 9, COLOR_SUCCESS);

  // Modern notification dot (optional)
  tft.fillCircle(iconX + iconW - 3, iconY + 3, 2, COLOR_ERROR);
  tft.drawCircle(iconX + iconW - 3, iconY + 3, 2, COLOR_CARD_BG);

  Serial.printf("🎨 Modern settings icon drawn at (%d,%d) size %dx%d\n", iconX, iconY, iconW, iconH);
}

// ===== SETTINGS PAGE FUNCTIONS =====

void checkPullGestures() {
  // Check for pull-down gesture to show settings page
  if (!settingsPage.isVisible && !swipeInProgress && touchActive) {
    // Check if touch started from top area (y < 30)
    if (currentSwipe.start.y < 30 && !settingsPage.pullDownDetected) {
      settingsPage.pullDownDetected = true;
      settingsPage.pullStartPoint = currentSwipe.start;
      Serial.println("📱 Pull-down gesture detected - preparing settings page");
    }
  }
}

void showSettingsPage() {
  if (settingsPage.isVisible) return;

  Serial.println("👇 Swipe DOWN - Show professional settings page");
  settingsPage.isVisible = true;
  settingsPage.isAnimating = true;
  settingsPage.animationStart = millis();
  settingsPage.animationProgress = 0;
  settingsPage.lastInteraction = millis();
  settingsPage.autoCloseTimer = millis(); // Start 10-second auto-close timer
  settingsPage.hoveredItem = -1; // Reset hover state

  // CRITICAL: Reset professional touch state for fresh start
  resetProfessionalTouchState();

  // Pause data fetching while settings is open
  pauseDataFetching();

  // Don't draw immediately - let animation handle it
}

void hideSettingsPage() {
  if (!settingsPage.isVisible) return;

  Serial.println("⚙️ Hiding settings page");
  settingsPage.isVisible = false;
  settingsPage.isAnimating = false;
  settingsPage.pullDownDetected = false;
  settingsPage.hoveredItem = -1; // Reset hover state
  settingsPage.autoCloseTimer = 0; // Reset auto-close timer

  // CRITICAL: Reset professional touch state to prevent stuck state
  resetProfessionalTouchState();

  // Resume data fetching
  resumeDataFetching();

  // Force full screen redraw to clear settings page
  smartRedraw.forceNextRedraw = true;
  updateProfessionalDisplay();
}

void updateSettingsPageAnimation() {
  if (!settingsPage.isAnimating) return;

  unsigned long elapsed = millis() - settingsPage.animationStart;
  const unsigned long animationDuration = 300; // 300ms animation

  if (elapsed >= animationDuration) {
    // Animation complete
    settingsPage.isAnimating = false;
    settingsPage.animationProgress = settingsPage.isVisible ? 100 : 0;

    // Draw settings page only once when animation completes
    if (settingsPage.isVisible) {
      drawSettingsPage();
    }
  } else {
    // Update animation progress - no redraw during animation
    float progress = (float)elapsed / animationDuration;
    settingsPage.animationProgress = settingsPage.isVisible ? (progress * 100) : (100 - progress * 100);
  }
}

void drawSettingsPage() {
  if (!settingsPage.isVisible) return;

  // Professional dark background with subtle gradient
  tft.fillScreen(COLOR_BG);

  // Modern gradient header with shadow
  for (int i = 0; i < MENU_HEADER_HEIGHT; i++) {
    uint16_t gradientColor = tft.color565(
      map(i, 0, MENU_HEADER_HEIGHT, 0x20, 0x40),
      map(i, 0, MENU_HEADER_HEIGHT, 0x20, 0x60),
      map(i, 0, MENU_HEADER_HEIGHT, 0x40, 0x80)
    );
    tft.drawFastHLine(0, i, 320, gradientColor);
  }

  // Header content - clean professional text
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_ACCENT);
  tft.setTextSize(2);
  tft.setCursor(20, 18);
  tft.print("SETTINGS");

  // Modern close button with shadow
  tft.fillRoundRect(275, 12, 36, 36, 12, COLOR_SHADOW);
  tft.fillRoundRect(273, 10, 36, 36, 12, COLOR_ERROR);
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_ERROR);
  tft.setTextSize(2);
  tft.setCursor(285, 20);
  tft.print("X");

  // Subtitle
  tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_BG);
  tft.setTextSize(1);
  tft.setCursor(20, 40);
  tft.println("Professional Configuration Panel");

  // Draw 2x4 grid menu items (no scrolling needed)
  for (int row = 0; row < MENU_ROWS; row++) {
    for (int col = 0; col < MENU_COLS; col++) {
      int itemIndex = row * MENU_COLS + col;
      if (itemIndex < MENU_ITEM_COUNT) {
        int x = MENU_START_X + col * (MENU_ITEM_WIDTH + MENU_PADDING);
        int y = MENU_START_Y + row * (MENU_ITEM_HEIGHT + MENU_PADDING);

        drawProfessionalMenuItem(itemIndex, x, y, MENU_ITEM_WIDTH, MENU_ITEM_HEIGHT);
      }
    }
  }

  // Auto-close timer indicator
  if (settingsPage.autoCloseTimer > 0) {
    unsigned long timeLeft = 10000 - (millis() - settingsPage.autoCloseTimer);
    if (timeLeft > 0) {
      int seconds = timeLeft / 1000 + 1;
      tft.setTextColor(COLOR_TEXT_DIM, COLOR_BG);
      tft.setTextSize(1);
      tft.setCursor(250, 45);
      tft.printf("Auto-close: %ds", seconds);
    }
  }

  Serial.println("🎨 Professional 2x4 grid settings page drawn");
}

// Professional settings use 2x4 grid - no scrolling needed

void drawProfessionalMenuItem(int index, int x, int y, int w, int h) {
  if (index >= MENU_ITEM_COUNT) return;

  MenuItem& item = menuItems[index];

  // Professional card design with subtle shadow
  tft.fillRoundRect(x + 2, y + 2, w, h, 8, COLOR_SHADOW);
  tft.fillRoundRect(x, y, w, h, 8, COLOR_CARD_BG);

  // Highlight effect if hovered
  if (settingsPage.hoveredItem == index) {
    tft.drawRoundRect(x, y, w, h, 8, COLOR_ACCENT);
    tft.drawRoundRect(x + 1, y + 1, w - 2, h - 2, 7, COLOR_ACCENT);
  } else {
    tft.drawRoundRect(x, y, w, h, 8, item.color);
  }

  // Icon with colored background (smaller for grid layout)
  int iconSize = 20;
  int iconX = x + 8;
  int iconY = y + 8;

  tft.fillRoundRect(iconX, iconY, iconSize, iconSize, 4, item.color);
  tft.setTextColor(COLOR_TEXT_PRIMARY, item.color);
  tft.setTextSize(1);
  tft.setCursor(iconX + 4, iconY + 6);
  tft.print(item.icon);

  // Title text (compact for grid)
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_CARD_BG);
  tft.setTextSize(1);
  tft.setCursor(iconX + iconSize + 6, y + 8);
  tft.print(item.title);

  // Subtitle text (smaller)
  tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_CARD_BG);
  tft.setTextSize(1);
  tft.setCursor(iconX + iconSize + 6, y + 20);
  tft.print(item.subtitle);

  // Status indicator for current selections (compact)
  if (index == 0) { // Operating Mode
    tft.setTextColor(COLOR_SUCCESS, COLOR_CARD_BG);
    tft.setTextSize(1);
    tft.setCursor(x + 4, y + h - 8);
    tft.printf("%s", operatingModes[settingsPage.selectedMode]);
  } else if (index == 1) { // Theme
    tft.setTextColor(COLOR_ACCENT, COLOR_CARD_BG);
    tft.setTextSize(1);
    tft.setCursor(x + 4, y + h - 8);
    tft.printf("%s", themes[currentTheme].name);
  } else if (index == 2) { // Brightness
    tft.setTextColor(COLOR_WARNING, COLOR_CARD_BG);
    tft.setTextSize(1);
    tft.setCursor(x + 4, y + h - 8);
    tft.printf("%s %d%%", brightness.autoMode ? "Auto" : "Manual", brightness.currentLevel);
  }
}

void drawModernMenuItem(int index, int x, int y, int w, int h) {
  if (index >= MENU_ITEM_COUNT) return;

  MenuItem& item = menuItems[index];

  // Modern card with shadow and gradient
  tft.fillRoundRect(x + 3, y + 3, w, h, 12, COLOR_SHADOW);
  tft.fillRoundRect(x, y, w, h, 12, COLOR_CARD_BG);

  // Gradient border effect
  tft.drawRoundRect(x, y, w, h, 12, item.color);
  tft.drawRoundRect(x + 1, y + 1, w - 2, h - 2, 11, COLOR_HIGHLIGHT);

  // Icon area with colored background
  int iconSize = 40;
  int iconX = x + 15;
  int iconY = y + (h - iconSize) / 2;

  tft.fillRoundRect(iconX, iconY, iconSize, iconSize, 8, item.color);
  tft.setTextColor(COLOR_TEXT_PRIMARY, item.color);
  tft.setTextSize(2);
  tft.setCursor(iconX + 8, iconY + 12);
  tft.print(item.icon);

  // Title text
  tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_CARD_BG);
  tft.setTextSize(1);
  tft.setCursor(iconX + iconSize + 15, y + 15);
  tft.print(item.title);

  // Subtitle text
  tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_CARD_BG);
  tft.setTextSize(1);
  tft.setCursor(iconX + iconSize + 15, y + 30);
  tft.print(item.subtitle);

  // Arrow indicator for sub-menus
  if (item.hasSubMenu) {
    tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_CARD_BG);
    tft.setTextSize(1);
    tft.setCursor(x + w - 20, y + h/2 - 4);
    tft.print(">");
  }

  // Status indicator for current selections
  if (index == 0) { // Operating Mode
    tft.setTextColor(COLOR_SUCCESS, COLOR_CARD_BG);
    tft.setTextSize(1);
    tft.setCursor(iconX + iconSize + 15, y + 45);
    tft.printf("Current: %s", operatingModes[settingsPage.selectedMode]);
  } else if (index == 1) { // Theme
    tft.setTextColor(COLOR_ACCENT, COLOR_CARD_BG);
    tft.setTextSize(1);
    tft.setCursor(iconX + iconSize + 15, y + 45);
    tft.printf("Active: %s", themes[currentTheme].name);
  } else if (index == 2) { // Brightness
    tft.setTextColor(COLOR_WARNING, COLOR_CARD_BG);
    tft.setTextSize(1);
    tft.setCursor(iconX + iconSize + 15, y + 45);
    tft.printf("Mode: %s (%d%%)", brightness.autoMode ? "Auto" : "Manual", brightness.currentLevel);
  }
}

void handleSettingsPageTouch(int x, int y) {
  if (!settingsPage.isVisible) return;

  // Update interaction timer and reset auto-close timer
  settingsPage.lastInteraction = millis();
  settingsPage.autoCloseTimer = millis(); // Reset 10-second timer

  Serial.printf("⚙️ Professional settings touch at (%d,%d)\n", x, y);

  // Check close button (X) - updated coordinates
  if (x >= 273 && x <= 309 && y >= 10 && y <= 46) {
    Serial.printf("⚙️ Close button tapped at (%d,%d) - hiding settings page\n", x, y);
    hideSettingsPage();
    return;
  }

  // Handle professional grid touch (no scrolling)
  handleProfessionalTouch(x, y);
}

// Global professional touch state
static bool g_professionalTouchActive = false;
static bool g_professionalTouchProcessed = false;
static int g_professionalLastTouchX = -1;
static int g_professionalLastTouchY = -1;
static unsigned long g_professionalTouchStartTime = 0;

// Global brightness touch state for single tap detection
static bool g_brightnesssTouchActive = false;
static int g_brightnessStartX = -1;
static int g_brightnessStartY = -1;
static unsigned long g_brightnesssTouchStartTime = 0;

void handleProfessionalTouch(int x, int y) {
  // Professional 2x4 grid touch handling with reduced sensitivity

  // Start new touch session
  if (!g_professionalTouchActive) {
    g_professionalTouchActive = true;
    g_professionalTouchProcessed = false;
    g_professionalTouchStartTime = millis();
    g_professionalLastTouchX = x;
    g_professionalLastTouchY = y;
    Serial.printf("📱 Professional touch started at (%d,%d)\n", x, y);
    return;
  }

  // Skip if already processed this touch
  if (g_professionalTouchProcessed) {
    return;
  }

  // Check if touch has been stable for minimum duration (300ms to prevent accidental taps)
  unsigned long touchDuration = millis() - g_professionalTouchStartTime;
  int touchMovement = abs(x - g_professionalLastTouchX) + abs(y - g_professionalLastTouchY);

  if (touchDuration >= 300 && touchMovement <= 15) {
    // Stable touch detected - process grid item selection
    g_professionalTouchProcessed = true; // Mark as processed

    // Calculate which grid item was touched
    for (int row = 0; row < MENU_ROWS; row++) {
      for (int col = 0; col < MENU_COLS; col++) {
        int itemIndex = row * MENU_COLS + col;
        if (itemIndex < MENU_ITEM_COUNT) {
          int itemX = MENU_START_X + col * (MENU_ITEM_WIDTH + MENU_PADDING);
          int itemY = MENU_START_Y + row * (MENU_ITEM_HEIGHT + MENU_PADDING);

          // Check if touch is within this item's bounds
          if (g_professionalLastTouchX >= itemX && g_professionalLastTouchX <= itemX + MENU_ITEM_WIDTH &&
              g_professionalLastTouchY >= itemY && g_professionalLastTouchY <= itemY + MENU_ITEM_HEIGHT) {

            Serial.printf("⚙️ Professional menu item %d selected: %s\n", itemIndex, menuItems[itemIndex].title);

            // Visual feedback
            highlightProfessionalMenuItem(itemIndex);

            // Handle action
            handleProfessionalMenuAction(itemIndex);

            return;
          }
        }
      }
    }

    Serial.println("📱 Professional touch outside menu items");
  } else if (touchDuration >= 1000) {
    // Touch held too long - reset to prevent stuck state
    Serial.println("📱 Professional touch timeout - resetting");
    g_professionalTouchActive = false;
    g_professionalTouchProcessed = false;
  }
}

void handleProfessionalTouchEnd() {
  // Called when touch ends - reset touch state properly
  if (g_professionalTouchActive) {
    Serial.printf("📱 Professional touch ended (duration: %lums, processed: %s)\n",
                  millis() - g_professionalTouchStartTime,
                  g_professionalTouchProcessed ? "Yes" : "No");

    g_professionalTouchActive = false;
    g_professionalTouchProcessed = false;
  }
}

void resetProfessionalTouchState() {
  // CRITICAL: Reset all professional touch state variables
  // This prevents stuck touch state when settings page is closed
  g_professionalTouchActive = false;
  g_professionalTouchProcessed = false;
  g_professionalLastTouchX = -1;
  g_professionalLastTouchY = -1;
  g_professionalTouchStartTime = 0;

  Serial.println("🔄 Professional touch state reset - ready for new touch sessions");
}

void highlightProfessionalMenuItem(int itemIndex) {
  // Brief highlight effect for visual feedback
  if (itemIndex < 0 || itemIndex >= MENU_ITEM_COUNT) return;

  int row = itemIndex / MENU_COLS;
  int col = itemIndex % MENU_COLS;
  int x = MENU_START_X + col * (MENU_ITEM_WIDTH + MENU_PADDING);
  int y = MENU_START_Y + row * (MENU_ITEM_HEIGHT + MENU_PADDING);

  // Set hovered item and redraw with highlight
  settingsPage.hoveredItem = itemIndex;
  drawProfessionalMenuItem(itemIndex, x, y, MENU_ITEM_WIDTH, MENU_ITEM_HEIGHT);

  delay(100); // Brief highlight duration

  // Remove highlight
  settingsPage.hoveredItem = -1;
  drawProfessionalMenuItem(itemIndex, x, y, MENU_ITEM_WIDTH, MENU_ITEM_HEIGHT);
}

void handleProfessionalMenuAction(int itemIndex) {
  // CRITICAL: Reset touch state after every action to prevent stuck state
  resetProfessionalTouchState();

  switch (itemIndex) {
    case 0: // Operating Mode
      settingsPage.selectedMode = (settingsPage.selectedMode + 1) % 4;
      Serial.printf("⚙️ Operating mode changed to: %s\n", operatingModes[settingsPage.selectedMode]);
      drawSettingsPage(); // Redraw to show new mode
      break;

    case 1: // Display Theme
      changeTheme((currentTheme + 1) % 2);
      Serial.printf("⚙️ Theme changed to: %s\n", themes[currentTheme].name);
      drawSettingsPage(); // Redraw to show new theme
      break;

    case 2: // Brightness
      Serial.printf("⚙️ Opening brightness settings\n");
      showBrightnessPage();
      break;

    case 3: // WiFi Settings
      Serial.printf("⚙️ WiFi settings - opening WiFi config\n");
      // TODO: Implement WiFi settings page
      break;

    case 4: // System Reset
      Serial.printf("⚙️ System reset - opening reset options\n");
      // TODO: Implement system reset page
      break;

    case 5: // System Info
      Serial.printf("⚙️ System info - showing device information\n");
      // TODO: Implement system info page
      break;

    case 6: // Advanced
      Serial.printf("⚙️ Advanced settings - opening advanced options\n");
      // TODO: Implement advanced settings page
      break;

    case 7: // Help & About
      Serial.printf("⚙️ Help & About - showing help information\n");
      // TODO: Implement help & about page
      break;
  }
}

// Professional settings use 2x4 grid - no scrolling functions needed

void checkSettingsPageTimeout() {
  if (!settingsPage.isVisible) return;

  // Professional auto-close after 10 seconds of inactivity
  if (settingsPage.autoCloseTimer > 0 && millis() - settingsPage.autoCloseTimer > 10000) {
    Serial.println("⏰ Professional settings page timeout (10s) - auto-closing");
    hideSettingsPage();
  }
}

// ===== BRIGHTNESS CONTROL FUNCTIONS =====

void initializeBrightness() {
  // Initialize brightness control
  brightness.autoMode = true;
  brightness.manualLevel = 80;
  brightness.currentLevel = 80;
  brightness.ldrValue = 0;
  brightness.lastLdrRead = 0;
  brightness.brightnessPageVisible = false;
  brightness.needsUpdate = false;

  // Set initial brightness
  setBrightness(brightness.currentLevel);

  Serial.println("💡 Brightness control initialized");
}

void updateBrightness() {
  // Read LDR sensor every 500ms for responsive readings
  static int targetBrightness = 80;
  static int pendingTargetBrightness = 80;
  static unsigned long lastTargetUpdate = 0;
  static unsigned long lightChangeDetected = 0;
  static bool waitingForStableLight = false;
  static int lastStableLdrValue = 0;

  if (millis() - brightness.lastLdrRead >= 500) {
    readLDRSensor();
    brightness.lastLdrRead = millis();

    // Update target brightness only when LDR is read
    if (brightness.autoMode) {
      // Auto-calibrate LDR range
      if (brightness.ldrValue < brightness.ldrMin) {
        brightness.ldrMin = brightness.ldrValue;
      }
      if (brightness.ldrValue > brightness.ldrMax) {
        brightness.ldrMax = brightness.ldrValue;
      }

      // Use adaptive mapping based on observed range
      // FIXED: LDR logic corrected - higher LDR = more light = lower brightness (correct for this circuit)
      int ldrRange = brightness.ldrMax - brightness.ldrMin;
      int newTargetBrightness;

      if (ldrRange > 100) { // Good range detected
        // CORRECTED: Higher LDR value = more light = lower brightness (inverted mapping)
        newTargetBrightness = map(brightness.ldrValue, brightness.ldrMin, brightness.ldrMax, 100, 20);
      } else {
        // Fallback to fixed mapping if range is too small
        // CORRECTED: Higher LDR value = more light = lower brightness (inverted mapping)
        newTargetBrightness = map(brightness.ldrValue, 0, 4095, 100, 20);
      }

      newTargetBrightness = constrain(newTargetBrightness, 20, 100);

      // Check if light level changed significantly (threshold: 10% brightness change)
      if (abs(newTargetBrightness - targetBrightness) >= 10) {
        if (!waitingForStableLight) {
          // Light change detected - start 3-second wait
          lightChangeDetected = millis();
          waitingForStableLight = true;
          pendingTargetBrightness = newTargetBrightness;
          lastStableLdrValue = brightness.ldrValue;
          Serial.printf("💡 Light change detected: %d%% → %d%% (LDR: %d), waiting 3 seconds...\n",
                       targetBrightness, newTargetBrightness, brightness.ldrValue);
        } else {
          // Update pending target if light continues to change
          pendingTargetBrightness = newTargetBrightness;
          lastStableLdrValue = brightness.ldrValue;
        }
      }

      // Check if 3 seconds have passed and light is stable
      if (waitingForStableLight && millis() - lightChangeDetected >= 3000) {
        // Confirm light is still at the new level (within 5% tolerance)
        int currentMapping;
        if (ldrRange > 100) {
          currentMapping = map(brightness.ldrValue, brightness.ldrMin, brightness.ldrMax, 100, 20);
        } else {
          currentMapping = map(brightness.ldrValue, 0, 4095, 100, 20);
        }
        currentMapping = constrain(currentMapping, 20, 100);

        if (abs(currentMapping - pendingTargetBrightness) <= 5) {
          // Light is stable - apply the change
          targetBrightness = pendingTargetBrightness;
          lastTargetUpdate = millis();
          waitingForStableLight = false;
          Serial.printf("💡 Light stable for 3s - applying brightness change: %d%% (LDR: %d)\n",
                       targetBrightness, brightness.ldrValue);
        } else {
          // Light is still changing - reset wait timer
          lightChangeDetected = millis();
          pendingTargetBrightness = currentMapping;
          Serial.printf("💡 Light still changing - resetting wait timer (LDR: %d)\n", brightness.ldrValue);
        }
      }
    }
  }

  // Fast and smooth brightness transition every frame (high FPS)
  if (brightness.autoMode && abs(targetBrightness - brightness.currentLevel) > 0) {
    // Fast smooth transition - 3% per frame for quick but smooth changes
    int step = 3;
    if (abs(targetBrightness - brightness.currentLevel) < 10) {
      step = 1; // Slower for fine adjustments
    }

    if (targetBrightness > brightness.currentLevel) {
      brightness.currentLevel += step; // Fast smooth increase
    } else {
      brightness.currentLevel -= step; // Fast smooth decrease
    }
    brightness.currentLevel = constrain(brightness.currentLevel, 20, 100);
    setBrightness(brightness.currentLevel);
    brightness.needsUpdate = true;

    // Log only significant changes
    static int lastLoggedLevel = -1;
    if (abs(brightness.currentLevel - lastLoggedLevel) >= 5 ||
        (millis() - lastTargetUpdate < 100)) { // Log immediately after target update
      Serial.printf("💡 Fast smooth transition: %d%% → %d%% (Target: %d%%, LDR: %d)\n",
                   lastLoggedLevel, brightness.currentLevel, targetBrightness, brightness.ldrValue);
      lastLoggedLevel = brightness.currentLevel;
    }
  }

  // Update brightness page if visible (limit to 30fps for UI)
  static unsigned long lastUIUpdate = 0;
  if (brightness.brightnessPageVisible && brightness.needsUpdate &&
      millis() - lastUIUpdate >= 33) { // 30fps for UI
    drawBrightnessPage();
    brightness.needsUpdate = false;
    lastUIUpdate = millis();
  }
}

void readLDRSensor() {
  // Read LDR sensor with heavy averaging for very stable readings
  int ldrSum = 0;
  const int samples = 10; // More samples for stability

  for (int i = 0; i < samples; i++) {
    ldrSum += analogRead(LDR_PIN);
    delayMicroseconds(100); // Very short delay between samples
  }

  int newLdrValue = ldrSum / samples;

  // Apply additional smoothing filter to reduce noise
  static int smoothedLDR = 0;
  if (smoothedLDR == 0) {
    smoothedLDR = newLdrValue; // Initialize on first reading
  } else {
    // Exponential moving average for ultra-smooth readings
    // Alpha = 0.1 means 90% old value + 10% new value = very smooth
    smoothedLDR = (smoothedLDR * 9 + newLdrValue) / 10;
  }

  brightness.ldrValue = smoothedLDR;

  // Debug output every 2 seconds (less frequent)
  static unsigned long lastDebug = 0;
  if (millis() - lastDebug >= 2000) {
    // Show corrected logic mapping
    int correctedMapping = map(brightness.ldrValue, 0, 4095, 100, 20); // Corrected: Higher LDR = Lower brightness

    Serial.printf("💡 LDR Smooth: %d (Raw: %d), Corrected: %d%%, Current: %d%%, Mode: %s\n",
                  brightness.ldrValue,
                  newLdrValue,
                  correctedMapping,
                  brightness.currentLevel,
                  brightness.autoMode ? "Auto" : "Manual");

    // Circuit analysis
    float voltage = (brightness.ldrValue / 4095.0) * 3.3;
    Serial.printf("💡 Circuit: Voltage=%.2fV, Range=%d-%d, Logic=Corrected, Smoothing=90%%\n",
                  voltage, brightness.ldrMin, brightness.ldrMax);

    lastDebug = millis();
  }
}

void setBrightness(int level) {
  // Set PWM brightness (0-255) with ultra-smooth transitions
  level = constrain(level, 0, 100);

  // No frequency limit - allow maximum FPS for smoothest transitions
  static bool pwmInitialized = false;
  if (!pwmInitialized) {
    // Initialize PWM once with very high frequency for ultra-smooth operation
    ledcSetup(0, 25000, 10); // Channel 0, 25kHz frequency, 10-bit resolution (0-1023)
    ledcAttachPin(TFT_BL, 0);
    pwmInitialized = true;
    Serial.println("💡 Ultra-smooth PWM initialized: 25kHz, 10-bit");
  }

  // Map to 10-bit PWM (0-1023) for finer control
  int pwmValue = map(level, 0, 100, 0, 1023);

  // Write PWM immediately for maximum responsiveness
  ledcWrite(0, pwmValue);

  brightness.currentLevel = level;
  brightness.lastPWMUpdate = millis();

  // Minimal debug logging to avoid performance impact
  static int lastLoggedLevel = -1;
  static unsigned long lastLogTime = 0;
  if (level != lastLoggedLevel && millis() - lastLogTime >= 500) { // Log every 500ms max
    Serial.printf("💡 Ultra-smooth PWM: %d%% → PWM=%d/1023\n", level, pwmValue);
    lastLoggedLevel = level;
    lastLogTime = millis();
  }
}

void showBrightnessPage() {
  if (brightness.brightnessPageVisible) return;

  Serial.println("💡 Showing brightness page");
  brightness.brightnessPageVisible = true;

  // Hide settings page
  settingsPage.isVisible = false;

  // CRITICAL: Pause data fetching while brightness page is open
  pauseDataFetching();

  // Reset brightness touch state for fresh start
  g_brightnesssTouchActive = false;

  // Draw brightness page
  drawBrightnessPage();
}

void hideBrightnessPage() {
  if (!brightness.brightnessPageVisible) return;

  Serial.println("💡 Hiding brightness page");
  brightness.brightnessPageVisible = false;

  // CRITICAL: Reset professional touch state when returning to settings
  resetProfessionalTouchState();

  // CRITICAL: Resume data fetching when brightness page is closed
  resumeDataFetching();

  // Return to settings page
  settingsPage.isVisible = true;
  drawSettingsPage();
}

void drawBrightnessPage() {
  if (!brightness.brightnessPageVisible) return;

  // Clear screen with clean white background
  tft.fillScreen(TFT_WHITE);

  // Professional header with gradient-like effect
  tft.fillRect(0, 0, 320, 60, 0x4A69);  // Professional blue header
  tft.fillRect(0, 55, 320, 5, 0x3186);  // Darker blue bottom border

  // Header title - clean and professional
  tft.setTextColor(TFT_WHITE);
  tft.setTextSize(2);
  tft.setCursor(80, 20);
  tft.print("BRIGHTNESS CONTROL");

  // Back button - modern rounded design
  tft.fillRoundRect(15, 15, 40, 30, 8, 0x2945);  // Dark blue
  tft.drawRoundRect(15, 15, 40, 30, 8, TFT_WHITE);
  tft.setTextColor(TFT_WHITE);
  tft.setTextSize(2);
  tft.setCursor(28, 23);
  tft.print("<");

  // Close button - modern rounded design
  tft.fillRoundRect(265, 15, 40, 30, 8, 0xF800);  // Red
  tft.drawRoundRect(265, 15, 40, 30, 8, TFT_WHITE);
  tft.setTextColor(TFT_WHITE);
  tft.setTextSize(2);
  tft.setCursor(278, 23);
  tft.print("X");

  // Main content area with professional cards
  int y = 80;

  // Current brightness card
  tft.fillRoundRect(20, y, 280, 50, 12, 0xF7BE);  // Light blue card
  tft.drawRoundRect(20, y, 280, 50, 12, 0x4A69);  // Blue border

  tft.setTextColor(0x2945);  // Dark blue text
  tft.setTextSize(1);
  tft.setCursor(30, y + 10);
  tft.print("Current Brightness Level");

  tft.setTextSize(3);
  tft.setCursor(30, y + 25);
  tft.printf("%d%%", brightness.currentLevel);

  // Light sensor indicator
  tft.setTextSize(1);
  tft.setCursor(200, y + 15);
  tft.print("Light Sensor");
  tft.setCursor(200, y + 30);
  tft.printf("Value: %d", brightness.ldrValue);

  y += 70;

  // Auto mode card
  tft.fillRoundRect(20, y, 280, 60, 12, TFT_WHITE);
  tft.drawRoundRect(20, y, 280, 60, 12, 0x4A69);

  tft.setTextColor(0x2945);
  tft.setTextSize(1);
  tft.setCursor(30, y + 15);
  tft.print("Automatic Brightness Control");

  // Auto mode toggle button - modern switch design
  uint16_t switchBg = brightness.autoMode ? 0x07E0 : 0xC618;  // Green or Gray
  uint16_t switchText = TFT_WHITE;

  tft.fillRoundRect(200, y + 25, 80, 25, 12, switchBg);
  tft.drawRoundRect(200, y + 25, 80, 25, 12, 0x4A69);
  tft.setTextColor(switchText);
  tft.setTextSize(1);
  tft.setCursor(brightness.autoMode ? 215 : 225, y + 33);
  tft.print(brightness.autoMode ? "AUTO" : "MANUAL");

  y += 80;

  // Manual control card (only if auto mode is off)
  if (!brightness.autoMode) {
    tft.fillRoundRect(20, y, 280, 100, 12, TFT_WHITE);
    tft.drawRoundRect(20, y, 280, 100, 12, 0x4A69);

    tft.setTextColor(0x2945);
    tft.setTextSize(1);
    tft.setCursor(30, y + 15);
    tft.print("Manual Brightness Control");

    // Brightness slider - modern design
    int sliderY = y + 35;
    tft.fillRoundRect(30, sliderY, 240, 15, 8, 0xE71C);  // Light gray background
    tft.drawRoundRect(30, sliderY, 240, 15, 8, 0x4A69);

    // Slider fill
    int sliderWidth = map(brightness.manualLevel, 0, 100, 0, 240);
    if (sliderWidth > 0) {
      tft.fillRoundRect(30, sliderY, sliderWidth, 15, 8, 0x07E0);  // Green fill
    }

    // Slider handle
    int handleX = 30 + sliderWidth - 8;
    if (handleX < 30) handleX = 30;
    if (handleX > 262) handleX = 262;
    tft.fillCircle(handleX, sliderY + 7, 10, TFT_WHITE);
    tft.drawCircle(handleX, sliderY + 7, 10, 0x4A69);
    tft.fillCircle(handleX, sliderY + 7, 6, 0x4A69);

    // Brightness value display
    tft.setTextColor(0x2945);
    tft.setTextSize(2);
    tft.setCursor(130, sliderY + 25);
    tft.printf("%d%%", brightness.manualLevel);

    // Control buttons - modern design
    int buttonY = y + 70;

    // Decrease button
    tft.fillRoundRect(50, buttonY, 50, 25, 8, 0xFD20);  // Orange
    tft.drawRoundRect(50, buttonY, 50, 25, 8, 0x4A69);
    tft.setTextColor(TFT_WHITE);
    tft.setTextSize(2);
    tft.setCursor(68, buttonY + 6);
    tft.print("-");

    // Increase button
    tft.fillRoundRect(220, buttonY, 50, 25, 8, 0x07E0);  // Green
    tft.drawRoundRect(220, buttonY, 50, 25, 8, 0x4A69);
    tft.setTextColor(TFT_WHITE);
    tft.setTextSize(2);
    tft.setCursor(238, buttonY + 6);
    tft.print("+");
  }

  Serial.println("Professional brightness page drawn");
}

void handleBrightnessPageTouch(int x, int y) {
  if (!brightness.brightnessPageVisible) return;

  // Professional single tap detection for brightness page
  if (!g_brightnesssTouchActive) {
    g_brightnesssTouchActive = true;
    g_brightnessStartX = x;
    g_brightnessStartY = y;
    g_brightnesssTouchStartTime = millis();
    Serial.printf("💡 Brightness page touch started at (%d,%d)\n", x, y);
    return;
  }

  // Check for stable touch (100ms minimum, 10px tolerance)
  unsigned long touchDuration = millis() - g_brightnesssTouchStartTime;
  int touchMovement = abs(x - g_brightnessStartX) + abs(y - g_brightnessStartY);

  if (touchDuration >= 100 && touchMovement <= 10) {
    // Stable touch detected - process button
    Serial.printf("💡 Brightness page stable touch at (%d,%d)\n", g_brightnessStartX, g_brightnessStartY);

    // Check back button (<) - new coordinates
    if (g_brightnessStartX >= 15 && g_brightnessStartX <= 55 && g_brightnessStartY >= 15 && g_brightnessStartY <= 45) {
      Serial.printf("💡 Back button tapped at (%d,%d) - returning to settings\n", g_brightnessStartX, g_brightnessStartY);
      g_brightnesssTouchActive = false; // Reset touch state
      hideBrightnessPage();
      return;
    }

    // Check close button (X) - new coordinates
    if (g_brightnessStartX >= 265 && g_brightnessStartX <= 305 && g_brightnessStartY >= 15 && g_brightnessStartY <= 45) {
      Serial.printf("💡 Close button tapped at (%d,%d) - hiding brightness page\n", g_brightnessStartX, g_brightnessStartY);
      g_brightnesssTouchActive = false; // Reset touch state
      brightness.brightnessPageVisible = false;
      settingsPage.isVisible = false;

      // CRITICAL: Resume data fetching when closing brightness page
      resumeDataFetching();

      // Force full screen redraw
      smartRedraw.forceNextRedraw = true;
      updateProfessionalDisplay();
      return;
    }

    // Check auto mode toggle button - new coordinates (y = 80 + 70 + 25 = 175)
    if (g_brightnessStartY >= 175 && g_brightnessStartY <= 200 && g_brightnessStartX >= 200 && g_brightnessStartX <= 280) {
      brightness.autoMode = !brightness.autoMode;
      Serial.printf("💡 Auto mode toggled at (%d,%d) - now %s\n",
                   g_brightnessStartX, g_brightnessStartY, brightness.autoMode ? "ON" : "OFF");

      if (!brightness.autoMode) {
        // Switch to manual mode - use current level as manual level
        brightness.manualLevel = brightness.currentLevel;
      }

      g_brightnesssTouchActive = false; // Reset touch state
      drawBrightnessPage();
      return;
    }

    // Manual control (only if auto mode is off)
    if (!brightness.autoMode) {
      // Check decrease button - new coordinates (y = 80 + 70 + 80 + 70 = 300)
      if (g_brightnessStartY >= 300 && g_brightnessStartY <= 325 && g_brightnessStartX >= 50 && g_brightnessStartX <= 100) {
        brightness.manualLevel = constrain(brightness.manualLevel - 10, 20, 100);
        setBrightness(brightness.manualLevel);
        Serial.printf("💡 Brightness decreased at (%d,%d) - now %d%%\n",
                     g_brightnessStartX, g_brightnessStartY, brightness.manualLevel);
        g_brightnesssTouchActive = false; // Reset touch state
        drawBrightnessPage();
        return;
      }

      // Check increase button - new coordinates
      if (g_brightnessStartY >= 300 && g_brightnessStartY <= 325 && g_brightnessStartX >= 220 && g_brightnessStartX <= 270) {
        brightness.manualLevel = constrain(brightness.manualLevel + 10, 20, 100);
        setBrightness(brightness.manualLevel);
        Serial.printf("💡 Brightness increased at (%d,%d) - now %d%%\n",
                     g_brightnessStartX, g_brightnessStartY, brightness.manualLevel);
        g_brightnesssTouchActive = false; // Reset touch state
        drawBrightnessPage();
        return;
      }

      // Check slider area - new coordinates (y = 80 + 70 + 80 + 35 = 265)
      if (g_brightnessStartY >= 265 && g_brightnessStartY <= 280 && g_brightnessStartX >= 30 && g_brightnessStartX <= 270) {
        int newLevel = map(g_brightnessStartX, 30, 270, 0, 100);
        newLevel = constrain(newLevel, 20, 100);
        brightness.manualLevel = newLevel;
        setBrightness(brightness.manualLevel);
        Serial.printf("💡 Slider adjusted at (%d,%d) - now %d%%\n",
                     g_brightnessStartX, g_brightnessStartY, brightness.manualLevel);
        g_brightnesssTouchActive = false; // Reset touch state
        drawBrightnessPage();
        return;
      }
    }

    // No button hit
    g_brightnesssTouchActive = false; // Reset touch state
    Serial.printf("💡 Touch at (%d,%d) - no button hit\n", g_brightnessStartX, g_brightnessStartY);
  } else if (touchDuration >= 1000) {
    // Touch held too long - reset to prevent stuck state
    Serial.println("💡 Brightness touch timeout - resetting");
    g_brightnesssTouchActive = false;
  }
}

void handleBrightnessPageTouchEnd() {
  // Called when touch ends - reset brightness touch state
  if (g_brightnesssTouchActive) {
    Serial.printf("💡 Brightness touch ended (duration: %lums)\n",
                  millis() - g_brightnesssTouchStartTime);
    g_brightnesssTouchActive = false;
  }
}
