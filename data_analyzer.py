import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import glob
import os
from config import Config

class DataAnalyzer:
    def __init__(self):
        self.config = Config()
        
    def load_data(self, days_back=7):
        """Load data from CSV files for the last N days"""
        data_files = []
        
        for i in range(days_back):
            date = datetime.now() - timedelta(days=i)
            filename = f"{self.config.DATA_SAVE_PATH}/inverter_data_{date.strftime('%Y%m%d')}.csv"
            if os.path.exists(filename):
                data_files.append(filename)
        
        if not data_files:
            print("No data files found")
            return None
            
        # Load and combine all data
        dfs = []
        for file in data_files:
            df = pd.read_csv(file)
            dfs.append(df)
            
        combined_df = pd.concat(dfs, ignore_index=True)
        
        # Convert timestamp to datetime
        combined_df['timestamp'] = pd.to_datetime(combined_df['timestamp'])
        combined_df = combined_df.sort_values('timestamp')
        
        return combined_df
        
    def generate_daily_report(self, df):
        """Generate daily energy report"""
        if df is None or df.empty:
            return None
            
        # Calculate daily statistics
        daily_stats = {
            'date': datetime.now().strftime('%Y-%m-%d'),
            'avg_pv_power': df['pv_power'].astype(float).mean(),
            'max_pv_power': df['pv_power'].astype(float).max(),
            'avg_output_power': df['output_active_power'].astype(float).mean(),
            'max_output_power': df['output_active_power'].astype(float).max(),
            'avg_battery_voltage': df['battery_voltage'].astype(float).mean(),
            'avg_load_percent': df['load_percent'].astype(float).mean(),
            'max_load_percent': df['load_percent'].astype(float).max(),
        }
        
        return daily_stats
        
    def plot_power_trends(self, df, save_path=None):
        """Plot power trends"""
        if df is None or df.empty:
            return
            
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Inverter Power Trends', fontsize=16)
        
        # PV Power
        axes[0, 0].plot(df['timestamp'], df['pv_power'].astype(float))
        axes[0, 0].set_title('PV Power (W)')
        axes[0, 0].set_ylabel('Power (W)')
        axes[0, 0].tick_params(axis='x', rotation=45)
        
        # Output Power
        axes[0, 1].plot(df['timestamp'], df['output_active_power'].astype(float), color='orange')
        axes[0, 1].set_title('Output Active Power (W)')
        axes[0, 1].set_ylabel('Power (W)')
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # Battery Voltage
        axes[1, 0].plot(df['timestamp'], df['battery_voltage'].astype(float), color='green')
        axes[1, 0].set_title('Battery Voltage (V)')
        axes[1, 0].set_ylabel('Voltage (V)')
        axes[1, 0].tick_params(axis='x', rotation=45)
        
        # Load Percentage
        axes[1, 1].plot(df['timestamp'], df['load_percent'].astype(float), color='red')
        axes[1, 1].set_title('Load Percentage (%)')
        axes[1, 1].set_ylabel('Load (%)')
        axes[1, 1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Plot saved to {save_path}")
        else:
            plt.show()
            
    def export_summary_report(self, df):
        """Export summary report to Excel"""
        if df is None or df.empty:
            return
            
        filename = f"{self.config.DATA_SAVE_PATH}/summary_report_{datetime.now().strftime('%Y%m%d')}.xlsx"
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # Raw data
            df.to_excel(writer, sheet_name='Raw Data', index=False)
            
            # Daily summary
            daily_summary = df.groupby(df['timestamp'].dt.date).agg({
                'pv_power': ['mean', 'max', 'min'],
                'output_active_power': ['mean', 'max', 'min'],
                'battery_voltage': ['mean', 'max', 'min'],
                'load_percent': ['mean', 'max', 'min']
            }).round(2)
            
            daily_summary.to_excel(writer, sheet_name='Daily Summary')
            
            # Hourly averages
            hourly_avg = df.groupby(df['timestamp'].dt.hour).agg({
                'pv_power': 'mean',
                'output_active_power': 'mean',
                'battery_voltage': 'mean',
                'load_percent': 'mean'
            }).round(2)
            
            hourly_avg.to_excel(writer, sheet_name='Hourly Averages')
            
        print(f"Summary report exported to {filename}")

def main():
    analyzer = DataAnalyzer()
    
    # Load data for the last 7 days
    df = analyzer.load_data(days_back=7)
    
    if df is not None:
        print(f"Loaded {len(df)} data points")
        
        # Generate daily report
        daily_stats = analyzer.generate_daily_report(df)
        if daily_stats:
            print("\nDaily Statistics:")
            for key, value in daily_stats.items():
                print(f"{key}: {value}")
        
        # Generate plots
        plot_path = f"{analyzer.config.DATA_SAVE_PATH}/power_trends_{datetime.now().strftime('%Y%m%d')}.png"
        analyzer.plot_power_trends(df, save_path=plot_path)
        
        # Export summary report
        analyzer.export_summary_report(df)
        
    else:
        print("No data available for analysis")

if __name__ == "__main__":
    main()
