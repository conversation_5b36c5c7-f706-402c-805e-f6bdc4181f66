#ifndef CONFIG_H
#define CONFIG_H

#include <Arduino.h>

// ===== HARDWARE CONFIGURATION =====
#define TFT_BL 21
#define BOOT_BUTTON 0  // GPIO0 is the BOOT button on ESP32
#define TOUCH_CS 33    // Touch screen CS pin

// ===== TIMING CONFIGURATION =====
#define REFRESH_INTERVAL 10000        // Data refresh every 10 seconds
#define DISPLAY_UPDATE_INTERVAL 500   // Display update every 500ms (smooth)
#define WIFI_RESET_HOLD_TIME 5000     // Hold BOOT button for 5 seconds to reset WiFi
#define THEME_CHANGE_COOLDOWN 1000    // Prevent rapid theme changes

// ===== ANTI-FLICKER CONFIGURATION =====
#define ENABLE_DOUBLE_BUFFER true     // Enable double buffering
#define PARTIAL_UPDATE true           // Only update changed areas
#define SMOOTH_TRANSITIONS true       // Enable smooth transitions
#define FLICKER_REDUCTION true        // Enable flicker reduction techniques

// ===== TOUCH CONFIGURATION =====
#define TOUCH_THRESHOLD 600           // Touch pressure threshold
#define TOUCH_DEBOUNCE 50            // Touch debounce time (ms)
#define SWIPE_THRESHOLD 50           // Minimum swipe distance
#define SWIPE_TIMEOUT 500            // Maximum swipe time (ms)

// ===== API CONFIGURATION =====
#define API_URL "https://web.dessmonitor.com/public/?sign=8743221c28ad40664baa48193bbf4b03caa726f1&salt=1748162984217&token=c8075906465cca180724b25d151680e31e2267ff1319ccf2b43c88ba979a7576&action=querySPDeviceLastData&source=1&devcode=2376&pn=Q0046526082082&devaddr=1&sn=Q0046526082082094801&i18n=en_US"

// ===== THEME CONFIGURATION =====
#define MAX_THEMES 5

// Color Theme Structure
struct ColorTheme {
  uint16_t bg;
  uint16_t header;
  uint16_t cardBg;
  uint16_t cardBorder;
  uint16_t textPrimary;
  uint16_t textSecondary;
  uint16_t textDim;
  uint16_t accent;
  uint16_t solar;
  uint16_t solarGlow;
  uint16_t batteryGood;
  uint16_t batteryMid;
  uint16_t batteryLow;
  uint16_t grid;
  uint16_t gridGlow;
  uint16_t output;
  uint16_t outputGlow;
  uint16_t success;
  uint16_t warning;
  uint16_t error;
  uint16_t glow;
  uint16_t shadow;
  uint16_t highlight;
};

#endif // CONFIG_H
