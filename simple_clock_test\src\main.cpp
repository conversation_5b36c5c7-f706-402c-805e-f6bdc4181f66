/////////////////////////////////////////////////////////////////
/*
Simple ESP32-2432S028 Clock Test
Based on esp32-2432s028-clock-master but simplified
Using TFT_eSPI instead of LovyanGFX for easier setup
*/
/////////////////////////////////////////////////////////////////

#include <Arduino.h>
#include <TFT_eSPI.h>
#include <WiFi.h>
#include "time.h"

// WiFi credentials - CHANGE THESE!
const char *SSID = "YOUR_WIFI_SSID";
const char *wifi_password = "YOUR_WIFI_PASSWORD";

// NTP server
const char *ntpServer = "pool.ntp.org";

// Time format
const char *time_format = "%H:%M:%S";   // eg. 16:45:23
const char *date_format = "%A, %B %d %Y"; // eg. Monday, January 01 2024

// Timezone - Bangkok/Thailand
const char *timezone = "ICT-7";
const char *timezone_text = "Bangkok";

// Hardware pins
#define TFT_BL 21

// Display
TFT_eSPI tft = TFT_eSPI(240, 320);

// Colors
#define COLOR_BACKGROUND    0x0000  // Black
#define COLOR_TEXT_PRIMARY  0xFFFF  // White
#define COLOR_TEXT_TIME     0x07E0  // Green
#define COLOR_TEXT_DATE     0xFFE0  // Yellow
#define COLOR_TEXT_LOCATION 0x051F  // Blue

// Variables
bool wifiConnected = false;
unsigned long lastTimeUpdate = 0;

// Function declarations
void setTimezone(String timezone);
void connectWiFi();
void updateDisplay();
void drawClock();

void setup() {
    Serial.begin(115200);
    Serial.println("🚀 Starting Simple ESP32 Clock...");

    // Initialize backlight
    pinMode(TFT_BL, OUTPUT);
    digitalWrite(TFT_BL, HIGH);
    Serial.println("✅ Backlight initialized");

    // Initialize display
    tft.init();
    tft.setRotation(1); // Landscape 320x240
    tft.fillScreen(COLOR_BACKGROUND);
    Serial.println("✅ TFT Display initialized");

    // Show startup message
    tft.setTextColor(COLOR_TEXT_PRIMARY);
    tft.setTextSize(2);
    tft.drawString("ESP32 Clock", 80, 50);
    tft.setTextSize(1);
    tft.drawString("Connecting to WiFi...", 80, 100);

    // Connect to WiFi
    connectWiFi();

    if (wifiConnected) {
        // Initialize NTP
        configTime(0, 0, ntpServer);
        setTimezone(timezone);
        Serial.println("✅ NTP time configured");
        
        tft.fillScreen(COLOR_BACKGROUND);
        tft.setTextColor(COLOR_TEXT_PRIMARY);
        tft.setTextSize(1);
        tft.drawString("Time synchronized!", 80, 100);
        delay(2000);
    }

    // Clear screen for clock display
    tft.fillScreen(COLOR_BACKGROUND);
    
    Serial.println("🎉 Simple ESP32 Clock ready!");
}

void loop() {
    // Update display every second
    if (millis() - lastTimeUpdate > 1000) {
        updateDisplay();
        lastTimeUpdate = millis();
    }
    
    delay(100);
}

void setTimezone(String timezone) {
    Serial.printf("Setting Timezone to %s\n", timezone.c_str());
    setenv("TZ", timezone.c_str(), 1);
    tzset();
}

void connectWiFi() {
    // Skip WiFi if credentials not set
    if (String(SSID) == "YOUR_WIFI_SSID") {
        Serial.println("📡 WiFi credentials not set, using offline mode");
        wifiConnected = false;
        return;
    }
    
    WiFi.begin(SSID, wifi_password);
    Serial.print("📡 Connecting to WiFi");
    
    int attempts = 0;
    while (WiFi.status() != WL_CONNECTED && attempts < 20) {
        delay(500);
        Serial.print(".");
        attempts++;
    }
    
    if (WiFi.status() == WL_CONNECTED) {
        Serial.println();
        Serial.printf("✅ WiFi connected! IP: %s\n", WiFi.localIP().toString().c_str());
        wifiConnected = true;
    } else {
        Serial.println();
        Serial.println("❌ WiFi connection failed, using offline mode");
        wifiConnected = false;
    }
}

void updateDisplay() {
    if (wifiConnected) {
        drawClock();
    } else {
        // Show offline clock
        tft.fillScreen(COLOR_BACKGROUND);
        
        // Show current millis as time
        unsigned long currentTime = millis() / 1000;
        int hours = (currentTime / 3600) % 24;
        int minutes = (currentTime / 60) % 60;
        int seconds = currentTime % 60;
        
        // Draw time
        tft.setTextColor(COLOR_TEXT_TIME);
        tft.setTextSize(4);
        char timeStr[16];
        sprintf(timeStr, "%02d:%02d:%02d", hours, minutes, seconds);
        tft.drawString(timeStr, 50, 80);
        
        // Draw offline indicator
        tft.setTextColor(COLOR_TEXT_DATE);
        tft.setTextSize(2);
        tft.drawString("OFFLINE MODE", 80, 140);
        
        // Draw location
        tft.setTextColor(COLOR_TEXT_LOCATION);
        tft.setTextSize(1);
        tft.drawString(timezone_text, 130, 180);
    }
}

void drawClock() {
    struct tm timeinfo;
    if (!getLocalTime(&timeinfo)) {
        Serial.println("Failed to obtain time");
        return;
    }
    
    // Clear screen
    tft.fillScreen(COLOR_BACKGROUND);
    
    // Draw date
    char dateStr[64];
    strftime(dateStr, 64, date_format, &timeinfo);
    tft.setTextColor(COLOR_TEXT_DATE);
    tft.setTextSize(1);
    tft.drawString(dateStr, 10, 20);
    
    // Draw time
    char timeStr[64];
    strftime(timeStr, 64, time_format, &timeinfo);
    tft.setTextColor(COLOR_TEXT_TIME);
    tft.setTextSize(4);
    tft.drawString(timeStr, 50, 80);
    
    // Draw location
    tft.setTextColor(COLOR_TEXT_LOCATION);
    tft.setTextSize(2);
    tft.drawString(timezone_text, 120, 180);
    
    // Draw WiFi status
    tft.setTextColor(COLOR_TEXT_PRIMARY);
    tft.setTextSize(1);
    tft.drawString("WiFi: Connected", 10, 220);
}
