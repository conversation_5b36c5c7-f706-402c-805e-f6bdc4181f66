; PlatformIO Project Configuration File for ESP32-2432S028 Clock
;
; Based on esp32-2432s028-clock-master Arduino sketch
; Converted to PlatformIO with LVGL and LovyanGFX

[env:esp32-2432S028R]
platform = espressif32
board = esp32dev
framework = arduino

build_flags =
	-DCORE_DEBUG_LEVEL=0
	-DBOARD_HAS_PSRAM
	-mfix-esp32-psram-cache-issue
	-DARDUINO_USB_CDC_ON_BOOT=0

	; TFT Display Configuration for ESP32-2432S028
	-DUSER_SETUP_LOADED=1
	-DILI9341_2_DRIVER=1
	-DTFT_WIDTH=240
	-DTFT_HEIGHT=320
	-DTFT_MISO=12
	-DTFT_MOSI=13
	-DTFT_SCLK=14
	-DTFT_CS=15
	-DTFT_DC=2
	-DTFT_RST=4
	-DTFT_BL=21
	-DTOUCH_CS=33
	-DSPI_FREQUENCY=27000000
	-DTFT_INVERSION_ON=1

	; LVGL Configuration
	-DLV_CONF_INCLUDE_SIMPLE
	-DLV_USE_LOG=0
	-DLV_COLOR_DEPTH=16
	-DLV_COLOR_16_SWAP=0
	-DLV_MEM_SIZE=49152
	-DLV_USE_ANIMATION=1
	-DLV_FONT_MONTSERRAT_14=1
	-DLV_FONT_MONTSERRAT_22=1
	-DLV_TICK_CUSTOM=1

	-Os

lib_deps =
	lovyan03/LovyanGFX@^1.1.12
	lvgl/lvgl@^8.3.0

monitor_speed = 115200
monitor_filters = esp32_exception_decoder
upload_speed = 921600
