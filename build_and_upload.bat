@echo off
echo ESP32-2432S028R DESS Monitor Build Script
echo ==========================================

REM Check if PlatformIO is installed
pio --version >nul 2>&1
if errorlevel 1 (
    echo Error: PlatformIO is not installed or not in PATH
    echo Please install PlatformIO Core or PlatformIO IDE
    echo Visit: https://platformio.org/install
    pause
    exit /b 1
)

echo PlatformIO found, building project...
echo.

REM Clean previous build
echo Cleaning previous build...
pio run --target clean

if errorlevel 1 (
    echo Error: Clean failed
    pause
    exit /b 1
)

REM Build project
echo Building project...
pio run

if errorlevel 1 (
    echo Error: Build failed
    echo Please check the code for errors
    pause
    exit /b 1
)

echo.
echo Build successful!
echo.

REM Ask user if they want to upload
set /p upload="Do you want to upload to ESP32? (y/n): "
if /i "%upload%"=="y" (
    echo.
    echo Uploading to ESP32...
    echo Make sure ESP32 is connected via USB
    echo.
    
    pio run --target upload
    
    if errorlevel 1 (
        echo Error: Upload failed
        echo Check USB connection and COM port
        pause
        exit /b 1
    )
    
    echo.
    echo Upload successful!
    echo.
    
    set /p monitor="Do you want to start serial monitor? (y/n): "
    if /i "%monitor%"=="y" (
        echo.
        echo Starting serial monitor...
        echo Press Ctrl+C to exit monitor
        echo.
        pio device monitor
    )
)

echo.
echo Done!
pause
