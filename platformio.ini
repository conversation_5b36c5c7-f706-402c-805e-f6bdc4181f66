; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32-2432S028R]
platform = espressif32
board = esp32dev
framework = arduino
monitor_speed = 115200
monitor_filters = esp32_exception_decoder
build_flags = 
	-DCORE_DEBUG_LEVEL=0
	-DARDUINO_USB_CDC_ON_BOOT=0
	-DDISABLE_ALL_LIBRARY_WARNINGS
	-O3
	-DCONFIG_FREERTOS_HZ=1000
	-DUSER_SETUP_LOADED=1
	-DILI9341_2_DRIVER=1
	-DTFT_MISO=12
	-DTFT_MOSI=13
	-DTFT_SCLK=14
	-DTFT_CS=15
	-DTFT_DC=2
	-DTFT_RST=-1
	-DTFT_BL=21
	-DTOUCH_CS=33
	-DTFT_ROTATION=1
	-DTFT_OFFSET_X=0
	-DTFT_OFFSET_Y=0
	-DLOAD_GLCD=1
	-DLOAD_FONT2=1
	-DLOAD_FONT4=1
	-DLOAD_FONT6=1
	-DLOAD_FONT7=1
	-DLOAD_FONT8=1
	-DLOAD_GFXFF=1
	-DSMOOTH_FONT=1
	-DSPI_FREQUENCY=80000000
	-DSPI_READ_FREQUENCY=40000000
	-DSPI_TOUCH_FREQUENCY=2500000
	-DTFT_INVERSION_ON=1
	-DTFT_RGB_ORDER=TFT_BGR
lib_deps = 
	https://github.com/Bodmer/TFT_eSPI.git
	bblanchon/ArduinoJson@^7.0.4
	https://github.com/tzapu/WiFiManager.git
	https://github.com/PaulStoffregen/XPT2046_Touchscreen.git
	lvgl/lvgl@^9.2.2
upload_speed = 921600
build_src_filter = +<*> -<dess_monitor_main.cpp> -<theme_manager.cpp> -<display_manager.cpp> -<card_renderer.cpp> -<network_manager.cpp> -<main.cpp> -<main_simple.cpp> -<display.cpp> -<api_client.cpp> -<display_test.cpp> -<simple_display_test.cpp> -<tft_calibration_test.cpp> -<esp32_2432s028r_test.cpp> -<data_verification_test.cpp>
