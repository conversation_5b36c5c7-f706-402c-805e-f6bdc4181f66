/////////////////////////////////////////////////////////////////
/*
@gilbellosta, 2023-04-09
Adapted from https://docs.lvgl.io/master/widgets/chart.html
Converted to PlatformIO by AI Assistant
*/
/////////////////////////////////////////////////////////////////

#include <Arduino.h>
#include <stdlib.h>

#define LGFX_USE_V1
#include <LovyanGFX.hpp>
#include <lvgl.h>
#include <WiFi.h>
#include "esp_sntp.h"
#include "time.h"

// WiFi credentials - CHANGE THESE!
const char *SSID = "YOUR_WIFI_SSID";
const char *wifi_password = "YOUR_WIFI_PASSWORD";

// NTP server
const char *ntpServer = "pool.ntp.org";

// Time format
const char *time_format = "%H:%M:%S";   // eg. 16:45:23

// Timezone - Bangkok/Thailand
const char *timezone = "ICT-7";
const char *timezone_text = "Bangkok";

//----------------------------------------------------------------------
// LovyanGFX configuration for ESP32-2432S028
class LGFX : public lgfx::LGFX_Device{
  lgfx::Panel_ILI9341 _panel_instance;
  lgfx::Bus_SPI       _bus_instance;
  lgfx::Light_PWM     _light_instance;
  lgfx::Touch_XPT2046 _touch_instance;

public:
  LGFX(void){
    {
      auto cfg = _bus_instance.config();
      cfg.spi_host   = HSPI_HOST;
      cfg.spi_mode   = 0;
      cfg.freq_write = 40000000;
      cfg.freq_read  = 16000000;
      cfg.spi_3wire  = false;
      cfg.use_lock   = true;
      cfg.dma_channel=  1;
      cfg.pin_sclk   = 14;
      cfg.pin_mosi   = 13;
      cfg.pin_miso   = 12;
      cfg.pin_dc     =  2;
      _bus_instance.config(cfg);
      _panel_instance.setBus(&_bus_instance);
    }
    {
      auto cfg = _panel_instance.config();
      cfg.pin_cs          =    15;
      cfg.pin_rst         =    -1;
      cfg.pin_busy        =    -1;
      cfg.memory_width    =   240;
      cfg.memory_height   =   320;
      cfg.panel_width     =   240;
      cfg.panel_height    =   320;
      cfg.offset_x        =     0;
      cfg.offset_y        =     0;
      cfg.offset_rotation =     0;
      cfg.dummy_read_pixel=     8;
      cfg.dummy_read_bits =     1;
      cfg.readable        =  true;
      cfg.invert          = false;
      cfg.rgb_order       = false;
      cfg.dlen_16bit      = false;
      cfg.bus_shared      = false;
      _panel_instance.config(cfg);
    }
    {
      auto cfg = _light_instance.config();
      cfg.pin_bl = 21;
      cfg.invert = false;
      cfg.freq   = 44100;
      cfg.pwm_channel = 7;
      _light_instance.config(cfg);
      _panel_instance.setLight(&_light_instance);
    }
    {
      auto cfg = _touch_instance.config();
      cfg.x_min      = 300;
      cfg.x_max      = 3900;
      cfg.y_min      = 200;
      cfg.y_max      = 3700;
      cfg.pin_int    = 36;
      cfg.bus_shared = false;
      cfg.offset_rotation = 6;
      cfg.spi_host = VSPI_HOST;
      cfg.freq = 1000000;
      cfg.pin_sclk = 25;
      cfg.pin_mosi = 32;
      cfg.pin_miso = 39;
      cfg.pin_cs   = 33;
      _touch_instance.config(cfg);
      _panel_instance.setTouch(&_touch_instance);
    }
    setPanel(&_panel_instance);
  }
};

LGFX tft;

/* Change to your screen resolution */
static const uint32_t screenWidth  = 320;
static const uint32_t screenHeight = 240;

static lv_disp_draw_buf_t draw_buf;
static lv_color_t buf[ screenWidth * 10 ];

lv_obj_t *screenMain;
lv_obj_t *label1;
lv_obj_t *label2;
lv_obj_t *label3;

// Function declarations
void setTimezone(String timezone);
void cbSyncTime(struct timeval *tv);
void my_disp_flush(lv_disp_drv_t *disp, const lv_area_t *area, lv_color_t *color_p);
void update_labels();
void printLocalTime();

void setTimezone(String timezone)
{
  Serial.printf("Setting Timezone to %s\n",timezone.c_str());
  setenv("TZ",timezone.c_str(),1);
  tzset();
}

void cbSyncTime(struct timeval *tv)
{
  Serial.println(F("NTP time synched"));
  printLocalTime();
}

void my_disp_flush( lv_disp_drv_t *disp, const lv_area_t *area, lv_color_t *color_p )
{
   uint32_t w = ( area->x2 - area->x1 + 1 );
   uint32_t h = ( area->y2 - area->y1 + 1 );

   tft.startWrite();
   tft.setAddrWindow( area->x1, area->y1, w, h );
   tft.writePixels((lgfx::rgb565_t *)&color_p->full, w * h);
   tft.endWrite();

   lv_disp_flush_ready( disp );
}

void setup()
{
    Serial.begin(115200);
    Serial.println("🚀 Starting ESP32 Clock with LVGL...");

    tft.begin();
    tft.setRotation(1);
    tft.setBrightness(32);

    // Initialize lvgl library
    lv_init();
    lv_disp_draw_buf_init( &draw_buf, buf, NULL, screenWidth * 10 );

    /*Initialize the display*/
    static lv_disp_drv_t disp_drv;
    lv_disp_drv_init(&disp_drv);

    /*Change the following line to your display resolution*/
    disp_drv.hor_res = screenWidth;
    disp_drv.ver_res = screenHeight;
    disp_drv.flush_cb = my_disp_flush;
    disp_drv.draw_buf = &draw_buf;
    lv_disp_drv_register(&disp_drv);

    screenMain = lv_obj_create(NULL);
    lv_obj_set_style_bg_color(screenMain, lv_color_black(), LV_PART_MAIN);

    // Create labels with built-in fonts
    static lv_style_t style_font1;
    lv_style_init(&style_font1);
    lv_style_set_text_font(&style_font1, &lv_font_montserrat_22);

    static lv_style_t style_font2;
    lv_style_init(&style_font2);
    lv_style_set_text_font(&style_font2, &lv_font_montserrat_22);

    // Date label
    label1 = lv_label_create(screenMain);
    lv_obj_add_style(label1, &style_font1, 0);
    lv_obj_set_style_text_align(label1, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN);
    lv_obj_set_style_text_color(label1, lv_color_hex(0xffffff), LV_PART_MAIN);
    lv_obj_set_size(label1, 320, 30);
    lv_obj_set_pos(label1, 0, 0);

    // Time label
    label2 = lv_label_create(screenMain);
    lv_obj_add_style(label2, &style_font2, 0);
    lv_obj_set_style_text_align(label2, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN);
    lv_obj_set_style_text_color(label2, lv_color_hex(0xffffff), LV_PART_MAIN);
    lv_obj_set_size(label2, 320, 80);
    lv_obj_set_pos(label2, 0, (240-80)/2);
    lv_label_set_long_mode(label2, LV_LABEL_LONG_CLIP);

    // Location label
    label3 = lv_label_create(screenMain);
    lv_label_set_text(label3, timezone_text);
    lv_obj_add_style(label3, &style_font1, 0);
    lv_obj_set_style_text_align(label3, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN);
    lv_obj_set_style_text_color(label3, lv_color_hex(0xffffff), LV_PART_MAIN);
    lv_obj_set_size(label3, 320, 30);
    lv_obj_set_pos(label3, 0, 240-30);

    lv_scr_load(screenMain);

    // Show connecting message
    char status[64];
    snprintf(status, 64, "Connecting to %s", SSID);
    lv_label_set_text(label1, status);
    lv_timer_handler();

    // Connect to WiFi
    WiFi.begin(SSID, wifi_password);
    while (WiFi.status() != WL_CONNECTED)
    {
      delay(1000);
      Serial.println("Connecting to WiFi...");
    }
    Serial.println("Connected to WiFi");

    // Init NTP
    sntp_set_sync_interval(12 * 60 * 60 * 1000UL);
    sntp_set_time_sync_notification_cb(cbSyncTime);
    configTime(0, 0, ntpServer);

    setTimezone(timezone);
    printLocalTime();

    Serial.println("Setup completed!");
}

void loop()
{
    lv_timer_handler(); /* let the GUI do its work */
    update_labels();
    delay(100);
}

void update_labels()
{
  struct tm timeinfo;
  if(!getLocalTime(&timeinfo))
  {
    Serial.println("Failed to obtain time");
    return;
  }

  char current_date[64];
  strftime( current_date, 64, "%A, %B %d %Y", &timeinfo );
  lv_label_set_text(label1, current_date);

  char current_time[64];
  strftime( current_time, 64, time_format, &timeinfo );
  lv_label_set_text(label2, current_time);
}

void printLocalTime()
{
  struct tm timeinfo;
  if(!getLocalTime(&timeinfo))
  {
    Serial.println("Failed to obtain time");
  }
  else
  {
    Serial.println(&timeinfo, "%A, %B %d %Y %H:%M:%S zone %Z %z");
  }
}
