#include "hardware.h"
#include <esp32-hal-cpu.h>

// Hardware instances
TFT_eSPI tft = TFT_eSPI();
SPIClass touchscreenSPI = SPIClass(VSPI);
XPT2046_Touchscreen ts(XPT2046_CS, XPT2046_IRQ);

// Hardware status
HardwareStatus hwStatus;

void initializeHardware() {
  Serial.println("🔧 Initializing hardware components...");

  // Initialize display
  initializeDisplay();

  // Initialize touchscreen
  initializeTouchscreen();

  // Initialize sensors
  initializeSensors();

  // Initialize audio
  initializeAudio();

  // Set CPU frequency for optimal performance
  setCPUFrequency(240); // 240MHz

  Serial.println("✅ Hardware initialization complete");
}

void initializeDisplay() {
  Serial.println("📺 Initializing TFT display...");

  // Initialize TFT
  tft.init();
  tft.setRotation(1); // Landscape mode
  tft.fillScreen(TFT_BLACK);

  // Initialize backlight
  pinMode(TFT_BL, OUTPUT);
  setBacklight(80); // Default brightness

  // Enable hardware acceleration
  tft.setSwapBytes(true);

  hwStatus.displayReady = true;
  Serial.printf("✅ Display initialized: %dx%d\n", tft.width(), tft.height());
}

void initializeTouchscreen() {
  Serial.println("👆 Initializing touchscreen...");

  // Initialize SPI for touchscreen
  touchscreenSPI.begin(XPT2046_CLK, XPT2046_MISO, XPT2046_MOSI, XPT2046_CS);

  // Initialize touchscreen
  ts.begin(touchscreenSPI);
  ts.setRotation(1); // Match display rotation

  hwStatus.touchReady = true;
  Serial.println("✅ Touchscreen initialized");
}

void initializeSensors() {
  Serial.println("🌡️ Initializing sensors...");

  // Initialize LDR sensor
  pinMode(LDR_PIN, INPUT);

  // Initialize boot button
  pinMode(BOOT_BUTTON, INPUT_PULLUP);

  // Test sensors
  hwStatus.ldrValue = readLDR();
  hwStatus.bootButtonPressed = isBootButtonPressed();

  hwStatus.sensorsReady = true;
  Serial.printf("✅ Sensors initialized - LDR: %d, Boot button: %s\n",
                hwStatus.ldrValue, hwStatus.bootButtonPressed ? "Pressed" : "Released");
}

void initializeAudio() {
  Serial.println("🔊 Initializing audio...");

  // Initialize speaker pin
  pinMode(SPEAKER_PIN, OUTPUT);
  digitalWrite(SPEAKER_PIN, LOW);

  // Test audio with a short beep
  playBeep();

  hwStatus.audioReady = true;
  Serial.println("✅ Audio initialized");
}

void setBacklight(uint8_t brightness) {
  brightness = constrain(brightness, 0, 100);
  uint16_t pwmValue = map(brightness, 0, 100, 0, 255);

  analogWrite(TFT_BL, pwmValue);
  hwStatus.brightness = brightness;

  Serial.printf("💡 Backlight set to %d%% (PWM: %d)\n", brightness, pwmValue);
}

uint8_t getBacklight() {
  return hwStatus.brightness;
}

void displaySleep() {
  Serial.println("😴 Display entering sleep mode...");
  setBacklight(0);
  tft.writecommand(0x10); // Sleep in command
  hwStatus.displayReady = false;
}

void displayWakeup() {
  Serial.println("😊 Display waking up...");
  tft.writecommand(0x11); // Sleep out command
  delay(120); // Wait for display to wake up
  setBacklight(hwStatus.brightness);
  hwStatus.displayReady = true;
}

bool isTouchPressed() {
  if (!hwStatus.touchReady) return false;

  bool pressed = ts.tirqTouched() && ts.touched();
  hwStatus.touchPressed = pressed;

  if (pressed) {
    hwStatus.lastTouchTime = millis();
  }

  return pressed;
}

bool getTouchPoint(int16_t &x, int16_t &y) {
  if (!isTouchPressed()) {
    return false;
  }

  TS_Point p = ts.getPoint();

  // Apply calibration
  x = map(p.x, TOUCH_CALIBRATION_X1, TOUCH_CALIBRATION_X2, 0, TFT_WIDTH);
  y = map(p.y, TOUCH_CALIBRATION_Y1, TOUCH_CALIBRATION_Y2, 0, TFT_HEIGHT);

  // Constrain to screen bounds
  x = constrain(x, 0, TFT_WIDTH - 1);
  y = constrain(y, 0, TFT_HEIGHT - 1);

  hwStatus.lastTouchX = x;
  hwStatus.lastTouchY = y;

  return true;
}

void calibrateTouch() {
  Serial.println("📐 Starting touch calibration...");

  tft.fillScreen(TFT_BLACK);
  tft.setTextColor(TFT_WHITE);
  tft.setTextSize(2);
  tft.setCursor(50, 100);
  tft.println("Touch Calibration");
  tft.setCursor(30, 130);
  tft.println("Touch corners when");
  tft.setCursor(80, 160);
  tft.println("prompted");

  delay(3000);

  // TODO: Implement proper touch calibration routine
  Serial.println("✅ Touch calibration complete (using default values)");
}

int readLDR() {
  int value = analogRead(LDR_PIN);
  hwStatus.ldrValue = value;
  return value;
}

float readTemperature() {
  // ESP32 internal temperature sensor (rough approximation)
  // Note: temperatureRead() is available in newer ESP32 Arduino Core
  hwStatus.temperature = 25.0; // Default temperature for now
  return hwStatus.temperature;
}

bool isBootButtonPressed() {
  bool pressed = digitalRead(BOOT_BUTTON) == LOW;

  if (pressed && !hwStatus.bootButtonPressed) {
    hwStatus.bootButtonPressTime = millis();
  }

  hwStatus.bootButtonPressed = pressed;
  return pressed;
}

void playTone(uint16_t frequency, uint16_t duration) {
  if (!hwStatus.audioReady) return;

  // Generate tone using PWM
  ledcSetup(0, frequency, 8);
  ledcAttachPin(SPEAKER_PIN, 0);
  ledcWrite(0, 128); // 50% duty cycle

  delay(duration);

  ledcWrite(0, 0); // Stop tone
  ledcDetachPin(SPEAKER_PIN);
}

void playBeep() {
  playTone(1000, 100); // 1kHz for 100ms
}

void playSuccess() {
  playTone(800, 100);
  delay(50);
  playTone(1000, 100);
  delay(50);
  playTone(1200, 150);
}

void playError() {
  playTone(400, 200);
  delay(100);
  playTone(300, 300);
}

void enterDeepSleep() {
  Serial.println("😴 Entering deep sleep...");

  // Turn off display
  displaySleep();

  // Configure wake up source (boot button)
  esp_sleep_enable_ext0_wakeup(GPIO_NUM_0, 0); // Wake on boot button press

  // Enter deep sleep
  esp_deep_sleep_start();
}

void enterLightSleep() {
  Serial.println("😴 Entering light sleep...");

  // Configure wake up sources
  esp_sleep_enable_ext0_wakeup(GPIO_NUM_0, 0); // Boot button
  esp_sleep_enable_timer_wakeup(30 * 1000000); // 30 seconds

  // Enter light sleep
  esp_light_sleep_start();

  Serial.println("😊 Waking up from light sleep...");
}

void setCPUFrequency(uint32_t freq) {
  setCpuFrequencyMhz(freq);
  Serial.printf("⚡ CPU frequency set to %dMHz\n", getCpuFrequencyMhz());
}
