/*******************************************************************************
 * Size: 30 px
 * Bpp: 1
 * Opts: 
 ******************************************************************************/

#include "lvgl.h"

#ifndef OPENSANS_30
#define OPENSANS_30 1
#endif

#if OPENSANS_30

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */
    0x0,

    /* U+0021 "!" */
    0x55, 0x55, 0x55, 0x55, 0xf, 0xc0,

    /* U+0022 "\"" */
    0x86, 0x18, 0x61, 0x86, 0x18, 0x40,

    /* U+0023 "#" */
    0x8, 0x40, 0x84, 0x8, 0x40, 0x8c, 0x8, 0x80,
    0x88, 0x7f, 0xf1, 0x88, 0x18, 0x81, 0x8, 0x10,
    0x81, 0x8, 0x11, 0x8f, 0xfe, 0x11, 0x1, 0x10,
    0x31, 0x3, 0x10, 0x21, 0x2, 0x10, 0x23, 0x0,

    /* U+0024 "$" */
    0x8, 0x4, 0x2, 0x7, 0xe6, 0x8e, 0x42, 0x21,
    0x10, 0x88, 0x64, 0x1a, 0x7, 0x0, 0xe0, 0x58,
    0x26, 0x11, 0x8, 0x84, 0x42, 0x71, 0x6f, 0xc0,
    0x40, 0x20, 0x10, 0x8, 0x0,

    /* U+0025 "%" */
    0x30, 0x20, 0x90, 0x42, 0x11, 0x84, 0x22, 0x8,
    0x44, 0x10, 0x98, 0x21, 0x30, 0x42, 0x40, 0x85,
    0x99, 0xb, 0x49, 0x25, 0x9, 0x9a, 0x10, 0x34,
    0x20, 0x48, 0x41, 0x90, 0x83, 0x21, 0x4, 0x42,
    0x8, 0x84, 0x31, 0x8, 0x41, 0x20, 0x81, 0x80,

    /* U+0026 "&" */
    0x1c, 0x6, 0xc0, 0x88, 0x11, 0x2, 0x20, 0x44,
    0x8, 0x81, 0xb0, 0x14, 0x3, 0x0, 0x60, 0x16,
    0x4, 0x45, 0x8c, 0xa0, 0x94, 0x1e, 0x81, 0x90,
    0x32, 0x7, 0x23, 0x23, 0xc6,

    /* U+0027 "'" */
    0xfe,

    /* U+0028 "(" */
    0x18, 0x8c, 0x46, 0x21, 0x8, 0x84, 0x21, 0x8,
    0x42, 0x10, 0x84, 0x10, 0x84, 0x30, 0x84, 0x10,
    0xc0,

    /* U+0029 ")" */
    0xc2, 0x18, 0x43, 0x8, 0x42, 0x8, 0x42, 0x10,
    0x84, 0x21, 0x8, 0x44, 0x21, 0x18, 0x84, 0x46,
    0x0,

    /* U+002A "*" */
    0x8, 0x4, 0x2, 0x1f, 0xe1, 0xc0, 0xa0, 0xd8,
    0x44,

    /* U+002B "+" */
    0x4, 0x1, 0x0, 0x40, 0x10, 0x4, 0x1, 0xf,
    0xfc, 0x10, 0x4, 0x1, 0x0, 0x40, 0x10, 0x4,
    0x0,

    /* U+002C "," */
    0x26, 0x64, 0x44, 0x80,

    /* U+002D "-" */
    0xfc,

    /* U+002E "." */
    0xfc,

    /* U+002F "/" */
    0x1, 0x80, 0x80, 0x40, 0x60, 0x20, 0x30, 0x18,
    0x8, 0xc, 0x4, 0x2, 0x3, 0x1, 0x1, 0x80,
    0xc0, 0x40, 0x60, 0x30, 0x10, 0x18, 0x8, 0x0,

    /* U+0030 "0" */
    0x1c, 0x11, 0x10, 0x48, 0x24, 0x14, 0x6, 0x3,
    0x1, 0x80, 0xc0, 0x60, 0x30, 0x18, 0xc, 0x6,
    0x3, 0x1, 0xc1, 0x20, 0x90, 0x44, 0x41, 0xc0,

    /* U+0031 "1" */
    0x9, 0xdb, 0x90, 0x84, 0x21, 0x8, 0x42, 0x10,
    0x84, 0x21, 0x8, 0x42, 0x10, 0x80,

    /* U+0032 "2" */
    0x3e, 0x10, 0xc0, 0x18, 0x2, 0x0, 0x80, 0x20,
    0x8, 0x2, 0x1, 0x80, 0x40, 0x30, 0x8, 0x6,
    0x1, 0x0, 0x80, 0x60, 0x10, 0x8, 0x6, 0x3,
    0x0, 0xff, 0xc0,

    /* U+0033 "3" */
    0x3e, 0x38, 0x40, 0x8, 0x2, 0x0, 0x80, 0x20,
    0x8, 0x4, 0x3, 0x7, 0x0, 0x30, 0x2, 0x0,
    0x40, 0x10, 0x4, 0x1, 0x0, 0x40, 0x10, 0xb,
    0x4, 0x7e, 0x0,

    /* U+0034 "4" */
    0x1, 0x80, 0x30, 0xe, 0x1, 0x40, 0x68, 0x9,
    0x3, 0x20, 0x44, 0x18, 0x82, 0x10, 0xc2, 0x10,
    0x46, 0x8, 0x81, 0x30, 0x27, 0xff, 0x0, 0x80,
    0x10, 0x2, 0x0, 0x40, 0x8,

    /* U+0035 "5" */
    0x7f, 0x90, 0x4, 0x1, 0x0, 0x40, 0x10, 0x4,
    0x1, 0x0, 0x7e, 0x0, 0x40, 0x8, 0x1, 0x0,
    0x40, 0x10, 0x4, 0x1, 0x0, 0x40, 0x20, 0xb,
    0xc, 0x7e, 0x0,

    /* U+0036 "6" */
    0xf, 0x8, 0x8, 0x8, 0x4, 0x4, 0x2, 0x1,
    0x0, 0x9e, 0x71, 0xb0, 0x50, 0x18, 0xc, 0x6,
    0x3, 0x1, 0x80, 0xa0, 0x50, 0x44, 0x61, 0xc0,

    /* U+0037 "7" */
    0xff, 0xc0, 0x30, 0x8, 0x2, 0x1, 0x80, 0x60,
    0x10, 0xc, 0x3, 0x0, 0x80, 0x60, 0x18, 0x4,
    0x3, 0x0, 0xc0, 0x20, 0x18, 0x6, 0x1, 0x0,
    0xc0, 0x30, 0x0,

    /* U+0038 "8" */
    0x1e, 0x8, 0x44, 0x9, 0x2, 0x40, 0x90, 0x24,
    0x8, 0x84, 0x33, 0x7, 0x81, 0xe0, 0x8c, 0x41,
    0xb0, 0x38, 0x6, 0x1, 0x80, 0x60, 0x14, 0x9,
    0x86, 0x1e, 0x0,

    /* U+0039 "9" */
    0x3c, 0x31, 0x10, 0x50, 0x28, 0xc, 0x6, 0x3,
    0x1, 0x80, 0xa0, 0xd8, 0xe7, 0x90, 0x8, 0x4,
    0x2, 0x2, 0x1, 0x0, 0x80, 0x80, 0x87, 0x80,

    /* U+003A ":" */
    0xfc, 0x0, 0x0, 0x3f,

    /* U+003B ";" */
    0x6d, 0x80, 0x0, 0x0, 0x0, 0xdb, 0x4a, 0x40,

    /* U+003C "<" */
    0x0, 0x20, 0x1c, 0xe, 0x3, 0x1, 0x80, 0xc0,
    0x30, 0x3, 0x0, 0x18, 0x1, 0xc0, 0xe, 0x0,
    0x70, 0x2,

    /* U+003D "=" */
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xfc,

    /* U+003E ">" */
    0x0, 0x18, 0x1, 0xc0, 0xe, 0x0, 0x70, 0x3,
    0x80, 0x18, 0xe, 0x7, 0x3, 0x80, 0xc0, 0x60,
    0x8, 0x0,

    /* U+003F "?" */
    0x78, 0x88, 0x8, 0x10, 0x20, 0x40, 0x83, 0x4,
    0x18, 0x20, 0xc1, 0x2, 0x4, 0x8, 0x0, 0x0,
    0xc1, 0x83, 0x0,

    /* U+0040 "@" */
    0x7, 0xc0, 0x18, 0x60, 0x40, 0x61, 0x0, 0x46,
    0x0, 0x48, 0x78, 0x91, 0x10, 0xe2, 0x21, 0x88,
    0x43, 0x10, 0x86, 0x21, 0xc, 0x42, 0x18, 0x84,
    0x31, 0x8, 0x62, 0x31, 0x42, 0x52, 0x87, 0x38,
    0x80, 0x1, 0x0, 0x3, 0x0, 0x2, 0x0, 0x3,
    0x8, 0x3, 0xf0, 0x0,

    /* U+0041 "A" */
    0x6, 0x0, 0x60, 0x6, 0x0, 0xf0, 0xb, 0x0,
    0x90, 0x9, 0x1, 0x98, 0x11, 0x81, 0x8, 0x10,
    0x83, 0xc, 0x3f, 0xc2, 0x4, 0x20, 0x46, 0x6,
    0x60, 0x64, 0x2, 0x40, 0x2c, 0x3, 0xc0, 0x30,

    /* U+0042 "B" */
    0xfe, 0x20, 0xc8, 0x1a, 0x2, 0x80, 0xa0, 0x28,
    0xa, 0x6, 0x83, 0x3f, 0x8, 0x32, 0x2, 0x80,
    0x60, 0x18, 0x6, 0x1, 0x80, 0x60, 0x18, 0xa,
    0x6, 0xfe, 0x0,

    /* U+0043 "C" */
    0xf, 0xc6, 0x12, 0x1, 0x80, 0x40, 0x10, 0x8,
    0x2, 0x0, 0x80, 0x20, 0x8, 0x2, 0x0, 0x80,
    0x20, 0x8, 0x1, 0x0, 0x40, 0x18, 0x2, 0x0,
    0x60, 0xf, 0xc0,

    /* U+0044 "D" */
    0xfc, 0x20, 0xc8, 0x12, 0x2, 0x80, 0xa0, 0x38,
    0x6, 0x1, 0x80, 0x60, 0x18, 0x6, 0x1, 0x80,
    0x60, 0x18, 0x6, 0x2, 0x80, 0xa0, 0x28, 0x12,
    0x8, 0xfc, 0x0,

    /* U+0045 "E" */
    0xff, 0x2, 0x4, 0x8, 0x10, 0x20, 0x40, 0x81,
    0xfe, 0x4, 0x8, 0x10, 0x20, 0x40, 0x81, 0x2,
    0x4, 0xf, 0xe0,

    /* U+0046 "F" */
    0xff, 0x2, 0x4, 0x8, 0x10, 0x20, 0x40, 0x81,
    0x3, 0xfc, 0x8, 0x10, 0x20, 0x40, 0x81, 0x2,
    0x4, 0x8, 0x0,

    /* U+0047 "G" */
    0x7, 0xe1, 0x83, 0x20, 0x6, 0x0, 0x40, 0x4,
    0x0, 0x80, 0x8, 0x0, 0x80, 0x8, 0x0, 0x80,
    0xf8, 0x1, 0x80, 0x18, 0x1, 0x80, 0x14, 0x1,
    0x40, 0x16, 0x1, 0x20, 0x11, 0x81, 0x7, 0xe0,

    /* U+0048 "H" */
    0x80, 0x60, 0x18, 0x6, 0x1, 0x80, 0x60, 0x18,
    0x6, 0x1, 0x80, 0x7f, 0xf8, 0x6, 0x1, 0x80,
    0x60, 0x18, 0x6, 0x1, 0x80, 0x60, 0x18, 0x6,
    0x1, 0x80, 0x40,

    /* U+0049 "I" */
    0xff, 0xff, 0xf8,

    /* U+004A "J" */
    0x8, 0x42, 0x10, 0x84, 0x21, 0x8, 0x42, 0x10,
    0x84, 0x21, 0x8, 0x42, 0x10, 0x84, 0x21, 0x17,
    0x80,

    /* U+004B "K" */
    0x81, 0xc1, 0xa0, 0x90, 0xc8, 0x44, 0x62, 0x21,
    0x30, 0xb0, 0x58, 0x3c, 0x1b, 0x8, 0x84, 0x62,
    0x11, 0xc, 0x82, 0x41, 0xa0, 0xd0, 0x38, 0x18,

    /* U+004C "L" */
    0x81, 0x2, 0x4, 0x8, 0x10, 0x20, 0x40, 0x81,
    0x2, 0x4, 0x8, 0x10, 0x20, 0x40, 0x81, 0x2,
    0x4, 0xf, 0xe0,

    /* U+004D "M" */
    0xc0, 0x3, 0xc0, 0x3, 0xe0, 0x7, 0xa0, 0x5,
    0xa0, 0x5, 0xb0, 0xd, 0xb0, 0x9, 0x90, 0x9,
    0x98, 0x19, 0x98, 0x19, 0x88, 0x11, 0x8c, 0x31,
    0x8c, 0x31, 0x84, 0x21, 0x84, 0x21, 0x86, 0x61,
    0x82, 0x41, 0x82, 0x41, 0x83, 0xc1, 0x81, 0x81,
    0x81, 0x81,

    /* U+004E "N" */
    0xc0, 0x38, 0x7, 0x80, 0xd0, 0x1b, 0x3, 0x60,
    0x66, 0xc, 0xc1, 0x88, 0x31, 0x86, 0x10, 0xc3,
    0x18, 0x23, 0x6, 0x60, 0x4c, 0xd, 0x81, 0xb0,
    0x1e, 0x3, 0xc0, 0x38, 0x6,

    /* U+004F "O" */
    0xf, 0x3, 0xc, 0x20, 0x44, 0x2, 0x40, 0x2c,
    0x2, 0x80, 0x18, 0x1, 0x80, 0x18, 0x1, 0x80,
    0x18, 0x1, 0x80, 0x18, 0x1, 0x80, 0x18, 0x2,
    0x40, 0x24, 0x2, 0x20, 0x43, 0xc, 0xf, 0x0,

    /* U+0050 "P" */
    0xfc, 0x41, 0xa0, 0x50, 0x18, 0xc, 0x6, 0x3,
    0x1, 0x80, 0xc0, 0xa0, 0x9f, 0x88, 0x4, 0x2,
    0x1, 0x0, 0x80, 0x40, 0x20, 0x10, 0x8, 0x0,

    /* U+0051 "Q" */
    0xf, 0x3, 0xc, 0x20, 0x44, 0x2, 0x40, 0x2c,
    0x2, 0x80, 0x18, 0x1, 0x80, 0x18, 0x1, 0x80,
    0x18, 0x1, 0x80, 0x18, 0x1, 0x80, 0x18, 0x3,
    0x40, 0x24, 0x2, 0x20, 0x43, 0xc, 0xf, 0x80,
    0x18, 0x0, 0x80, 0xc, 0x0, 0x60, 0x2,

    /* U+0052 "R" */
    0xfc, 0x20, 0xc8, 0x12, 0x2, 0x80, 0xa0, 0x28,
    0xa, 0x2, 0x80, 0xa0, 0x48, 0x33, 0xf0, 0x8c,
    0x21, 0x8, 0x62, 0x8, 0x83, 0x20, 0x48, 0x1a,
    0x2, 0x80, 0xc0,

    /* U+0053 "S" */
    0x1f, 0x30, 0x50, 0x10, 0x8, 0x4, 0x2, 0x1,
    0x80, 0x60, 0x1c, 0x7, 0x0, 0xe0, 0x10, 0x4,
    0x2, 0x1, 0x0, 0x80, 0x40, 0x50, 0x4f, 0xc0,

    /* U+0054 "T" */
    0xff, 0xc2, 0x0, 0x80, 0x20, 0x8, 0x2, 0x0,
    0x80, 0x20, 0x8, 0x2, 0x0, 0x80, 0x20, 0x8,
    0x2, 0x0, 0x80, 0x20, 0x8, 0x2, 0x0, 0x80,
    0x20, 0x8, 0x0,

    /* U+0055 "U" */
    0x80, 0x60, 0x18, 0x6, 0x1, 0x80, 0x60, 0x18,
    0x6, 0x1, 0x80, 0x60, 0x18, 0x6, 0x1, 0x80,
    0x60, 0x18, 0x6, 0x1, 0x80, 0x60, 0x14, 0x8,
    0x86, 0x1e, 0x0,

    /* U+0056 "V" */
    0xc0, 0x3c, 0x2, 0x40, 0x24, 0x6, 0x60, 0x66,
    0x4, 0x20, 0x42, 0xc, 0x30, 0xc3, 0x8, 0x10,
    0x81, 0x18, 0x11, 0x81, 0x90, 0x19, 0x0, 0xb0,
    0xb, 0x0, 0xe0, 0xe, 0x0, 0x60, 0x6, 0x0,

    /* U+0057 "W" */
    0xc0, 0x60, 0x1c, 0x6, 0x3, 0x40, 0x70, 0x34,
    0x7, 0x3, 0x60, 0xd0, 0x26, 0xd, 0x2, 0x20,
    0x90, 0x62, 0x9, 0x86, 0x20, 0x98, 0x43, 0x18,
    0x84, 0x31, 0x88, 0x41, 0x10, 0x8c, 0x11, 0xc,
    0xc1, 0x10, 0xc8, 0x1b, 0x4, 0x80, 0xb0, 0x58,
    0xa, 0x5, 0x80, 0xa0, 0x70, 0xa, 0x7, 0x0,
    0xe0, 0x30, 0x6, 0x3, 0x0,

    /* U+0058 "X" */
    0x60, 0x48, 0x32, 0x8, 0xc6, 0x11, 0x84, 0x41,
    0xb0, 0x28, 0xe, 0x1, 0x80, 0x60, 0x28, 0xa,
    0x6, 0xc1, 0x10, 0xc4, 0x21, 0x88, 0x26, 0x9,
    0x3, 0xc0, 0xc0,

    /* U+0059 "Y" */
    0x80, 0xe0, 0xd0, 0x48, 0x26, 0x31, 0x10, 0x88,
    0x64, 0x14, 0xa, 0x5, 0x1, 0x0, 0x80, 0x40,
    0x20, 0x10, 0x8, 0x4, 0x2, 0x1, 0x0, 0x80,

    /* U+005A "Z" */
    0xff, 0x3, 0x2, 0x2, 0x6, 0x4, 0x4, 0xc,
    0x8, 0x8, 0x18, 0x10, 0x10, 0x30, 0x20, 0x20,
    0x60, 0x40, 0x40, 0xc0, 0xff,

    /* U+005B "[" */
    0xfc, 0x21, 0x8, 0x42, 0x10, 0x84, 0x21, 0x8,
    0x42, 0x10, 0x84, 0x21, 0x8, 0x42, 0x10, 0xf8,

    /* U+005C "\\" */
    0x80, 0x60, 0x10, 0x8, 0x6, 0x1, 0x0, 0xc0,
    0x60, 0x10, 0xc, 0x2, 0x1, 0x0, 0xc0, 0x20,
    0x10, 0xc, 0x2, 0x1, 0x80, 0xc0, 0x20, 0x18,

    /* U+005D "]" */
    0xf8, 0x42, 0x10, 0x84, 0x21, 0x8, 0x42, 0x10,
    0x84, 0x21, 0x8, 0x42, 0x10, 0x84, 0x21, 0xf8,

    /* U+005E "^" */
    0xc, 0x3, 0x1, 0xe0, 0x48, 0x33, 0x8, 0x46,
    0x19, 0x2, 0xc0, 0xe0, 0x10,

    /* U+005F "_" */
    0xff, 0xf0,

    /* U+0060 "`" */
    0xc3, 0x8, 0x20, 0x80,

    /* U+0061 "a" */
    0x3c, 0x42, 0x1, 0x1, 0x1, 0x1, 0x1, 0x1f,
    0x61, 0xc1, 0x81, 0x81, 0x81, 0x83, 0x47, 0x39,

    /* U+0062 "b" */
    0x80, 0x40, 0x20, 0x10, 0x8, 0x4, 0x2, 0x1,
    0x38, 0xa3, 0x60, 0xb0, 0x50, 0x18, 0xc, 0x6,
    0x3, 0x1, 0x80, 0xc0, 0x60, 0x38, 0x2c, 0x15,
    0x1a, 0x70,

    /* U+0063 "c" */
    0x1e, 0x41, 0x2, 0x8, 0x10, 0x20, 0x40, 0x81,
    0x2, 0x4, 0x4, 0x8, 0x8, 0xf,

    /* U+0064 "d" */
    0x0, 0x80, 0x40, 0x20, 0x10, 0x8, 0x4, 0x2,
    0x39, 0x63, 0xa0, 0xd0, 0x70, 0x18, 0xc, 0x6,
    0x3, 0x1, 0x80, 0xc0, 0x60, 0x30, 0x34, 0x1b,
    0x14, 0xf2,

    /* U+0065 "e" */
    0x1e, 0x11, 0x90, 0x50, 0x38, 0xc, 0x6, 0x3,
    0xff, 0x80, 0x40, 0x20, 0x10, 0x4, 0x2, 0x0,
    0x82, 0x3e,

    /* U+0066 "f" */
    0x1e, 0x20, 0x81, 0x2, 0x4, 0x8, 0x7e, 0x20,
    0x40, 0x81, 0x2, 0x4, 0x8, 0x10, 0x20, 0x40,
    0x81, 0x2, 0x4, 0x8, 0x0,

    /* U+0067 "g" */
    0x1f, 0xc8, 0x46, 0x19, 0x2, 0x40, 0x90, 0x24,
    0x9, 0x2, 0x21, 0x7, 0x81, 0x0, 0x80, 0x20,
    0x8, 0x1, 0xf0, 0x82, 0x40, 0x60, 0x18, 0x6,
    0x1, 0x80, 0x90, 0x63, 0xe0,

    /* U+0068 "h" */
    0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x9c,
    0xe2, 0xc1, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81,
    0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81,

    /* U+0069 "i" */
    0xc7, 0xff, 0xf8,

    /* U+006A "j" */
    0x24, 0x0, 0x49, 0x24, 0x92, 0x49, 0x24, 0x92,
    0x49, 0x24, 0xe0,

    /* U+006B "k" */
    0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x82,
    0x84, 0x8c, 0x88, 0x98, 0x90, 0xb0, 0xf0, 0xd8,
    0x88, 0x8c, 0x8c, 0x84, 0x86, 0x82, 0x82,

    /* U+006C "l" */
    0xff, 0xff, 0xfe,

    /* U+006D "m" */
    0x9c, 0x79, 0xc5, 0x8b, 0x6, 0xc, 0x8, 0x18,
    0x10, 0x30, 0x20, 0x60, 0x40, 0xc0, 0x81, 0x81,
    0x3, 0x2, 0x6, 0x4, 0xc, 0x8, 0x18, 0x10,
    0x30, 0x20, 0x60, 0x40, 0xc0, 0x81,

    /* U+006E "n" */
    0x9c, 0xa2, 0xc1, 0x81, 0x81, 0x81, 0x81, 0x81,
    0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81,

    /* U+006F "o" */
    0x1c, 0x31, 0x10, 0x58, 0x28, 0xc, 0x6, 0x3,
    0x1, 0x80, 0xc0, 0x60, 0x30, 0x14, 0x12, 0x8,
    0x88, 0x38,

    /* U+0070 "p" */
    0x9c, 0x51, 0xb0, 0x50, 0x28, 0xc, 0x6, 0x3,
    0x1, 0x80, 0xc0, 0x60, 0x30, 0x1c, 0x16, 0xb,
    0x8d, 0x38, 0x80, 0x40, 0x20, 0x10, 0x8, 0x4,
    0x2, 0x0,

    /* U+0071 "q" */
    0x1c, 0xb1, 0xd0, 0x68, 0x38, 0xc, 0x6, 0x3,
    0x1, 0x80, 0xc0, 0x60, 0x30, 0x18, 0x1a, 0xd,
    0x8a, 0x39, 0x0, 0x80, 0x40, 0x20, 0x10, 0x8,
    0x4, 0x2,

    /* U+0072 "r" */
    0x9d, 0x31, 0x88, 0x42, 0x10, 0x84, 0x21, 0x8,
    0x42, 0x10,

    /* U+0073 "s" */
    0x3e, 0x86, 0x4, 0x8, 0x18, 0x18, 0x18, 0x1c,
    0x8, 0x8, 0x10, 0x20, 0x61, 0x7c,

    /* U+0074 "t" */
    0x21, 0x8, 0x4f, 0x90, 0x84, 0x21, 0x8, 0x42,
    0x10, 0x84, 0x21, 0x8, 0x30,

    /* U+0075 "u" */
    0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81,
    0x81, 0x81, 0x81, 0x81, 0x81, 0x83, 0x45, 0x39,

    /* U+0076 "v" */
    0xc0, 0xe0, 0x50, 0x68, 0x36, 0x13, 0x8, 0x8c,
    0x46, 0x32, 0x19, 0x4, 0x82, 0xc1, 0x40, 0xe0,
    0x30, 0x18,

    /* U+0077 "w" */
    0xc1, 0x81, 0x41, 0x83, 0x41, 0xc3, 0x41, 0xc2,
    0x61, 0x42, 0x63, 0x42, 0x22, 0x46, 0x22, 0x66,
    0x22, 0x64, 0x36, 0x24, 0x16, 0x24, 0x14, 0x2c,
    0x14, 0x38, 0x14, 0x38, 0x1c, 0x18, 0xc, 0x18,

    /* U+0078 "x" */
    0xc1, 0xc3, 0x42, 0x66, 0x24, 0x3c, 0x18, 0x18,
    0x18, 0x38, 0x24, 0x24, 0x46, 0x42, 0xc3, 0x81,

    /* U+0079 "y" */
    0xc0, 0xa0, 0x50, 0x68, 0x36, 0x11, 0x8, 0x84,
    0x46, 0x32, 0x9, 0x4, 0x82, 0xc1, 0x40, 0x60,
    0x30, 0x18, 0x8, 0x4, 0x2, 0x3, 0x1, 0x1,
    0x83, 0x80,

    /* U+007A "z" */
    0xfc, 0x30, 0x82, 0x18, 0x41, 0xc, 0x20, 0x86,
    0x10, 0x43, 0x8, 0x3f,

    /* U+007B "{" */
    0x7, 0x8, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10,
    0x10, 0x10, 0x10, 0x20, 0xc0, 0x20, 0x10, 0x10,
    0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10,
    0x8, 0x7,

    /* U+007C "|" */
    0xff, 0xff, 0xff, 0xfc,

    /* U+007D "}" */
    0xc0, 0x20, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10,
    0x10, 0x10, 0x10, 0x8, 0x7, 0xc, 0x10, 0x10,
    0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10,
    0x20, 0xc0,

    /* U+007E "~" */
    0x78, 0x39, 0x88, 0xe, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 90, .box_w = 1, .box_h = 1, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1, .adv_w = 106, .box_w = 2, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 7, .adv_w = 161, .box_w = 6, .box_h = 7, .ofs_x = 2, .ofs_y = 14},
    {.bitmap_index = 13, .adv_w = 203, .box_w = 12, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 45, .adv_w = 192, .box_w = 9, .box_h = 25, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 74, .adv_w = 283, .box_w = 15, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 114, .adv_w = 195, .box_w = 11, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 143, .adv_w = 91, .box_w = 1, .box_h = 7, .ofs_x = 2, .ofs_y = 14},
    {.bitmap_index = 144, .adv_w = 132, .box_w = 5, .box_h = 26, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 161, .adv_w = 132, .box_w = 5, .box_h = 26, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 178, .adv_w = 175, .box_w = 9, .box_h = 8, .ofs_x = 1, .ofs_y = 14},
    {.bitmap_index = 187, .adv_w = 192, .box_w = 10, .box_h = 13, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 204, .adv_w = 99, .box_w = 4, .box_h = 7, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 208, .adv_w = 121, .box_w = 6, .box_h = 1, .ofs_x = 1, .ofs_y = 8},
    {.bitmap_index = 209, .adv_w = 99, .box_w = 2, .box_h = 3, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 210, .adv_w = 167, .box_w = 9, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 234, .adv_w = 192, .box_w = 9, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 258, .adv_w = 192, .box_w = 5, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 272, .adv_w = 192, .box_w = 10, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 299, .adv_w = 192, .box_w = 10, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 326, .adv_w = 192, .box_w = 11, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 355, .adv_w = 192, .box_w = 10, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 382, .adv_w = 192, .box_w = 9, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 406, .adv_w = 192, .box_w = 10, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 433, .adv_w = 192, .box_w = 10, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 460, .adv_w = 192, .box_w = 9, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 484, .adv_w = 99, .box_w = 2, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 488, .adv_w = 99, .box_w = 3, .box_h = 20, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 496, .adv_w = 192, .box_w = 11, .box_h = 13, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 514, .adv_w = 192, .box_w = 10, .box_h = 7, .ofs_x = 1, .ofs_y = 7},
    {.bitmap_index = 523, .adv_w = 192, .box_w = 11, .box_h = 13, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 541, .adv_w = 135, .box_w = 7, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 560, .adv_w = 289, .box_w = 15, .box_h = 23, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 604, .adv_w = 191, .box_w = 12, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 636, .adv_w = 213, .box_w = 10, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 663, .adv_w = 202, .box_w = 10, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 690, .adv_w = 233, .box_w = 10, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 717, .adv_w = 185, .box_w = 7, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 736, .adv_w = 171, .box_w = 7, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 755, .adv_w = 252, .box_w = 12, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 787, .adv_w = 240, .box_w = 10, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 814, .adv_w = 98, .box_w = 1, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 817, .adv_w = 94, .box_w = 5, .box_h = 26, .ofs_x = -2, .ofs_y = -5},
    {.bitmap_index = 834, .adv_w = 193, .box_w = 9, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 858, .adv_w = 167, .box_w = 7, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 877, .adv_w = 327, .box_w = 16, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 919, .adv_w = 255, .box_w = 11, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 948, .adv_w = 250, .box_w = 12, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 980, .adv_w = 197, .box_w = 9, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1004, .adv_w = 250, .box_w = 12, .box_h = 26, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 1043, .adv_w = 204, .box_w = 10, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1070, .adv_w = 190, .box_w = 9, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1094, .adv_w = 167, .box_w = 10, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1121, .adv_w = 237, .box_w = 10, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1148, .adv_w = 187, .box_w = 12, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1180, .adv_w = 327, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1233, .adv_w = 168, .box_w = 10, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1260, .adv_w = 161, .box_w = 9, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1284, .adv_w = 151, .box_w = 8, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1305, .adv_w = 151, .box_w = 5, .box_h = 25, .ofs_x = 3, .ofs_y = -4},
    {.bitmap_index = 1321, .adv_w = 167, .box_w = 9, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1345, .adv_w = 151, .box_w = 5, .box_h = 25, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 1361, .adv_w = 192, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 11},
    {.bitmap_index = 1374, .adv_w = 197, .box_w = 12, .box_h = 1, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 1376, .adv_w = 264, .box_w = 5, .box_h = 5, .ofs_x = 6, .ofs_y = 18},
    {.bitmap_index = 1380, .adv_w = 184, .box_w = 8, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1396, .adv_w = 209, .box_w = 9, .box_h = 23, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1422, .adv_w = 149, .box_w = 7, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1436, .adv_w = 209, .box_w = 9, .box_h = 23, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1462, .adv_w = 188, .box_w = 9, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1480, .adv_w = 106, .box_w = 7, .box_h = 23, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1501, .adv_w = 174, .box_w = 10, .box_h = 23, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 1530, .adv_w = 204, .box_w = 8, .box_h = 23, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1553, .adv_w = 90, .box_w = 1, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1556, .adv_w = 90, .box_w = 3, .box_h = 28, .ofs_x = 0, .ofs_y = -7},
    {.bitmap_index = 1567, .adv_w = 167, .box_w = 8, .box_h = 23, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1590, .adv_w = 90, .box_w = 1, .box_h = 23, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1593, .adv_w = 305, .box_w = 15, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1623, .adv_w = 204, .box_w = 8, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1639, .adv_w = 199, .box_w = 9, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1657, .adv_w = 209, .box_w = 9, .box_h = 23, .ofs_x = 2, .ofs_y = -7},
    {.bitmap_index = 1683, .adv_w = 209, .box_w = 9, .box_h = 23, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 1709, .adv_w = 131, .box_w = 5, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1719, .adv_w = 151, .box_w = 7, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1733, .adv_w = 105, .box_w = 5, .box_h = 20, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1746, .adv_w = 204, .box_w = 8, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1762, .adv_w = 153, .box_w = 9, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1780, .adv_w = 263, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1812, .adv_w = 158, .box_w = 8, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1828, .adv_w = 153, .box_w = 9, .box_h = 23, .ofs_x = 0, .ofs_y = -7},
    {.bitmap_index = 1854, .adv_w = 127, .box_w = 6, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1866, .adv_w = 173, .box_w = 8, .box_h = 26, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 1892, .adv_w = 192, .box_w = 1, .box_h = 30, .ofs_x = 5, .ofs_y = -7},
    {.bitmap_index = 1896, .adv_w = 173, .box_w = 8, .box_h = 26, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 1922, .adv_w = 192, .box_w = 11, .box_h = 3, .ofs_x = 1, .ofs_y = 9}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/



/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    }
};



/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR >= 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = NULL,
    .kern_scale = 0,
    .cmap_num = 1,
    .bpp = 1,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR >= 8
    .cache = &cache
#endif
};


/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t opensans_30 = {
#else
lv_font_t opensans_30 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 30,          /*The maximum line height required by the font*/
    .base_line = 7,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -2,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
    .fallback = NULL,
    .user_data = NULL
};



#endif /*#if OPENSANS_30*/

