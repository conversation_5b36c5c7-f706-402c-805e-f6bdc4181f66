2025-05-25 16:01:39,609 - INFO - Data saved to inverter_data/inverter_data_20250525.csv
2025-05-25 16:01:39,610 - INFO - Data saved to inverter_data/inverter_data_20250525_160139.json
2025-05-25 16:02:16,669 - INFO - Data saved to inverter_data/inverter_data_20250525.csv
2025-05-25 16:02:16,670 - INFO - Data saved to inverter_data/inverter_data_20250525_160216.json
2025-05-25 16:02:19,226 - INFO - generated new fontManager
2025-05-25 16:03:15,702 - INFO - Data saved to inverter_data/inverter_data_20250525.csv
2025-05-25 16:03:15,703 - INFO - Data saved to inverter_data/inverter_data_20250525_160315.json
2025-05-25 16:03:48,350 - INFO - Data saved to inverter_data/inverter_data_20250525.csv
2025-05-25 16:03:48,350 - INFO - Data saved to inverter_data/inverter_data_20250525_160348.json
2025-05-25 16:03:57,177 - INFO - ====== WebDriver manager ======
2025-05-25 16:03:57,864 - INFO - Get LATEST chromedriver version for google-chrome
2025-05-25 16:03:57,891 - INFO - Get LATEST chromedriver version for google-chrome
2025-05-25 16:03:57,913 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.113\chromedriver-win32/chromedriver.exe] found in cache
2025-05-25 16:03:58,883 - INFO - Chrome WebDriver initialized successfully
2025-05-25 16:03:58,883 - INFO - Attempting to login to DESS Monitor...
2025-05-25 16:04:10,819 - ERROR - Login failed: Message: 
Stacktrace:
	GetHandleVerifier [0x00E1FC03+61635]
	GetHandleVerifier [0x00E1FC44+61700]
	(No symbol) [0x00C405D3]
	(No symbol) [0x00C8899E]
	(No symbol) [0x00C88D3B]
	(No symbol) [0x00CD0E12]
	(No symbol) [0x00CAD2E4]
	(No symbol) [0x00CCE61B]
	(No symbol) [0x00CAD096]
	(No symbol) [0x00C7C840]
	(No symbol) [0x00C7D6A4]
	GetHandleVerifier [0x010A4523+2701795]
	GetHandleVerifier [0x0109FCA6+2683238]
	GetHandleVerifier [0x010BA9EE+2793134]
	GetHandleVerifier [0x00E368C5+155013]
	GetHandleVerifier [0x00E3CFAD+181357]
	GetHandleVerifier [0x00E27458+92440]
	GetHandleVerifier [0x00E27600+92864]
	GetHandleVerifier [0x00E11FF0+5296]
	BaseThreadInitThunk [0x766E5D49+25]
	RtlInitializeExceptionChain [0x7728D03B+107]
	RtlGetAppContainerNamedObjectPath [0x7728CFC1+561]

