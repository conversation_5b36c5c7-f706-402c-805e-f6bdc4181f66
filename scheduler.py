import schedule
import time
import signal
import sys
from dess_monitor import DESSMonitor
from config import Config
import logging

class DataScheduler:
    def __init__(self):
        self.config = Config()
        self.monitor = DESSMonitor()
        self.running = True
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        # Setup logging
        logging.basicConfig(
            level=getattr(logging, self.config.LOG_LEVEL),
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('scheduler.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        self.logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.running = False
        self.monitor.cleanup()
        sys.exit(0)
        
    def collect_data_job(self):
        """Job function for scheduled data collection"""
        try:
            self.logger.info("Starting scheduled data collection...")
            success = self.monitor.run_once()
            if success:
                self.logger.info("Scheduled data collection completed successfully")
            else:
                self.logger.error("Scheduled data collection failed")
        except Exception as e:
            self.logger.error(f"Error in scheduled data collection: {e}")
            
    def run_continuous(self):
        """Run continuous data collection"""
        self.logger.info("Starting continuous data collection scheduler...")
        
        # Schedule data collection every 5 minutes (or as configured)
        interval_minutes = self.config.COLLECTION_INTERVAL // 60
        schedule.every(interval_minutes).minutes.do(self.collect_data_job)
        
        # Run initial collection
        self.collect_data_job()
        
        # Keep running scheduled jobs
        while self.running:
            try:
                schedule.run_pending()
                time.sleep(1)
            except KeyboardInterrupt:
                self.logger.info("Keyboard interrupt received, shutting down...")
                break
            except Exception as e:
                self.logger.error(f"Error in scheduler loop: {e}")
                time.sleep(10)  # Wait before retrying
                
        self.monitor.cleanup()
        self.logger.info("Scheduler stopped")

if __name__ == "__main__":
    scheduler = DataScheduler()
    scheduler.run_continuous()
