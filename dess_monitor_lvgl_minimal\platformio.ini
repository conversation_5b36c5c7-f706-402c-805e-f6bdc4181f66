[env:esp32-2432S028R]
platform = espressif32
board = esp32dev
framework = arduino

; Build options
build_flags =
    -DCORE_DEBUG_LEVEL=0
    -DBOARD_HAS_PSRAM
    -mfix-esp32-psram-cache-issue
    -DARDUINO_USB_CDC_ON_BOOT=0

    ; TFT_eSPI Configuration for ESP32-2432S028R (ทดสอบแบบง่าย)
    -DUSER_SETUP_LOADED=1
    -DILI9341_DRIVER=1
    -DTFT_MISO=12
    -DTFT_MOSI=13
    -DTFT_SCLK=14
    -DTFT_CS=15
    -DTFT_DC=2
    -DTFT_RST=-1
    -DTFT_BL=21
    -DSPI_FREQUENCY=27000000

    ; LVGL Configuration
    -DLV_CONF_INCLUDE_SIMPLE
    -DLV_USE_LOG=0
    -DLV_COLOR_DEPTH=16
    -DLV_COLOR_16_SWAP=1
    -DLV_MEM_SIZE=49152
    -DLV_USE_ANIMATION=0
    -DLV_FONT_MONTSERRAT_14=1
    -DLV_FONT_MONTSERRAT_16=1

    ; Performance optimizations
    -Os
    -DCONFIG_FREERTOS_HZ=1000

; Libraries
lib_deps =
    https://github.com/Bodmer/TFT_eSPI.git

; Monitor settings
monitor_speed = 115200
monitor_filters = esp32_exception_decoder

; Upload settings
upload_speed = 921600

; Build filter - uncomment to test display only
build_src_filter = +<*> -<main.cpp> +<simple_tft_test.cpp>
