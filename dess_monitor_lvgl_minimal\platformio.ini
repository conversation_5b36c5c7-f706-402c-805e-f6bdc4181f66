[env:esp32-2432S028R]
platform = espressif32
board = esp32dev
framework = arduino

; Build options
build_flags =
    -DCORE_DEBUG_LEVEL=0
    -DBOARD_HAS_PSRAM
    -mfix-esp32-psram-cache-issue
    -DARDUINO_USB_CDC_ON_BOOT=0

    ; TFT_eSPI Configuration for ESP32-2432S028R (ตรงกับโปรเจคหลัก)
    -DUSER_SETUP_LOADED=1
    -DILI9341_DRIVER=1
    -DTFT_WIDTH=240
    -DTFT_HEIGHT=320
    -DTFT_MISO=12
    -DTFT_MOSI=13
    -DTFT_SCLK=14
    -DTFT_CS=15
    -DTFT_DC=2
    -DTFT_RST=4
    -DTFT_BL=21
    -DTOUCH_CS=33
    -DLOAD_GLCD=1
    -DLOAD_FONT2=1
    -DLOAD_FONT4=1
    -DLOAD_FONT6=1
    -DLOAD_FONT7=1
    -DLOAD_FONT8=1
    -DLOAD_GFXFF=1
    -DSMOOTH_FONT=1
    -DSPI_FREQUENCY=40000000
    -DSPI_READ_FREQUENCY=20000000
    -DSPI_TOUCH_FREQUENCY=2500000

    ; LVGL Configuration
    -DLV_CONF_INCLUDE_SIMPLE
    -DLV_USE_LOG=0
    -DLV_COLOR_DEPTH=16
    -DLV_COLOR_16_SWAP=1
    -DLV_MEM_SIZE=49152
    -DLV_USE_ANIMATION=0
    -DLV_FONT_MONTSERRAT_14=1
    -DLV_FONT_MONTSERRAT_16=1

    ; Performance optimizations
    -Os
    -DCONFIG_FREERTOS_HZ=1000

; Libraries
lib_deps =
    bodmer/TFT_eSPI@^2.5.43
    lvgl/lvgl@^8.3.0
    bblanchon/ArduinoJson@^6.21.5
    https://github.com/PaulStoffregen/XPT2046_Touchscreen.git

; Monitor settings
monitor_speed = 115200
monitor_filters = esp32_exception_decoder

; Upload settings
upload_speed = 921600

; Build filter - uncomment to test display only
; build_src_filter = +<*> -<main.cpp> +<display_test.cpp>
