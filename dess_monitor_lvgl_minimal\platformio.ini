; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32-2432S028R]
platform = espressif32
board = esp32dev
framework = arduino
build_flags =
	-DCORE_DEBUG_LEVEL=0
	-DBOARD_HAS_PSRAM
	-mfix-esp32-psram-cache-issue
	-DARDUINO_USB_CDC_ON_BOOT=0

	-DUSER_SETUP_LOADED=1
	-DILI9341_2_DRIVER=1
	-DTFT_WIDTH=240
	-DTFT_HEIGHT=320
	-DTFT_MISO=12
	-DTFT_MOSI=13
	-DTFT_SCLK=14
	-DTFT_CS=15
	-DTFT_DC=2
	-DTFT_RST=4
	-DTFT_BL=21
	-DTOUCH_CS=33
	-DSPI_FREQUENCY=27000000
	-DTFT_INVERSION_ON=1

	-Os
	-DCONFIG_FREERTOS_HZ=1000
lib_deps =
	https://github.com/Bodmer/TFT_eSPI.git
monitor_speed = 115200
monitor_filters = esp32_exception_decoder
upload_speed = 921600
build_src_filter = +<*> -<main.cpp> +<simple_test.cpp>
