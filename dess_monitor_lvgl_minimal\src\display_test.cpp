#include <Arduino.h>
#include <TFT_eSPI.h>

// Hardware Configuration
#define SCREEN_WIDTH  320
#define SCREEN_HEIGHT 240
#define TFT_BL       21

TFT_eSPI tft = TFT_eSPI();

void setup() {
  Serial.begin(115200);
  delay(1000);
  
  Serial.println("\n=== TFT Display Test ===");
  
  // Initialize backlight
  pinMode(TFT_BL, OUTPUT);
  digitalWrite(TFT_BL, HIGH);
  Serial.println("✅ Backlight ON");
  
  // Initialize TFT
  tft.init();
  tft.setRotation(1); // Landscape mode
  Serial.printf("✅ TFT initialized: %dx%d\n", tft.width(), tft.height());
  
  // Test basic colors
  Serial.println("🎨 Testing basic colors...");
  
  // Red screen
  tft.fillScreen(TFT_RED);
  delay(1000);
  
  // Green screen
  tft.fillScreen(TFT_GREEN);
  delay(1000);
  
  // Blue screen
  tft.fillScreen(TFT_BLUE);
  delay(1000);
  
  // White screen
  tft.fillScreen(TFT_WHITE);
  delay(1000);
  
  // Black screen
  tft.fillScreen(TFT_BLACK);
  delay(500);
  
  // Test text
  Serial.println("📝 Testing text display...");
  tft.setTextColor(TFT_WHITE, TFT_BLACK);
  tft.setTextSize(2);
  tft.setCursor(50, 50);
  tft.println("TFT Test OK!");
  
  tft.setTextSize(1);
  tft.setCursor(50, 80);
  tft.printf("Screen: %dx%d", tft.width(), tft.height());
  
  tft.setCursor(50, 100);
  tft.println("ESP32-2432S028R");
  
  // Test shapes
  Serial.println("🔷 Testing shapes...");
  tft.drawRect(10, 10, 100, 50, TFT_CYAN);
  tft.fillRect(120, 10, 100, 50, TFT_MAGENTA);
  tft.drawCircle(50, 150, 30, TFT_YELLOW);
  tft.fillCircle(150, 150, 30, TFT_GREEN);
  
  Serial.println("✅ Display test complete!");
}

void loop() {
  // Blink test
  static unsigned long lastBlink = 0;
  static bool state = false;
  
  if (millis() - lastBlink > 2000) {
    state = !state;
    
    if (state) {
      tft.fillCircle(280, 30, 20, TFT_RED);
      Serial.println("🔴 Blink ON");
    } else {
      tft.fillCircle(280, 30, 20, TFT_BLACK);
      Serial.println("⚫ Blink OFF");
    }
    
    lastBlink = millis();
  }
  
  delay(100);
}
