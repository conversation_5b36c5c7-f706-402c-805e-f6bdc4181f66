Write-Host "Testing common data logger ports on ***********..." -ForegroundColor Yellow

$targetIP = "***********"
$ports = @(80,8080,443,502,1883,8883,8000,8888,9000,10000,8081,8082,8083,2376,2377,2378,8090,8091,8092,8093,8094,8095)

Write-Host "Scanning $($ports.Count) ports..." -ForegroundColor Cyan

foreach ($port in $ports) {
    Write-Host "Testing port $port..." -NoNewline
    $result = Test-NetConnection -ComputerName $targetIP -Port $port -InformationLevel Quiet -WarningAction SilentlyContinue
    if ($result) {
        Write-Host " OPEN" -ForegroundColor Green
        Write-Host "  Try: http://$targetIP`:$port" -ForegroundColor Yellow
    } else {
        Write-Host " CLOSED" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Also testing other potential data logger IPs..." -ForegroundColor Yellow

$otherIPs = @("***********","***********","***********","************","************","***********2","***********4","************","*************")
$commonPorts = @(80,8080,8000,8888,502)

foreach ($ip in $otherIPs) {
    Write-Host "Testing $ip..." -ForegroundColor Cyan
    foreach ($port in $commonPorts) {
        $result = Test-NetConnection -ComputerName $ip -Port $port -InformationLevel Quiet -WarningAction SilentlyContinue
        if ($result) {
            Write-Host "  Port $port OPEN - Try: http://$ip`:$port" -ForegroundColor Green
        }
    }
}
