#ifndef HARDWARE_H
#define HAR<PERSON><PERSON>RE_H

#include <Arduino.h>
#include <TFT_eSPI.h>
#include <XPT2046_Touchscreen.h>
#include <SPI.h>

// Hardware Configuration for ESP32-2432S028R
#define TFT_WIDTH  320
#define TFT_HEIGHT 240

// Pin definitions
#define TFT_MISO   12
#define TFT_MOSI   13
#define TFT_SCLK   14
#define TFT_CS     15
#define TFT_DC     2
#define TFT_RST    4
#define TFT_BL     21

// Touch screen pins
#define XPT2046_IRQ  36
#define XPT2046_MOSI 32
#define XPT2046_MISO 39
#define XPT2046_CLK  25
#define XPT2046_CS   33

// Other hardware pins
#define BOOT_BUTTON  0
#define LDR_PIN      34
#define SPEAKER_PIN  26
#define LED_PIN      22

// Touch calibration values
#define TOUCH_CALIBRATION_X1 200
#define TOUCH_CALIBRATION_Y1 200
#define TOUCH_CALIBRATION_X2 3700
#define TOUCH_CALIBRATION_Y2 3700

// Hardware classes
extern TFT_eSPI tft;
extern SPIClass touchscreenSPI;
extern XPT2046_Touchscreen ts;

// Hardware initialization functions
void initializeHardware();
void initializeDisplay();
void initializeTouchscreen();
void initializeSensors();
void initializeAudio();

// Display control functions
void setBacklight(uint8_t brightness);
uint8_t getBacklight();
void displaySleep();
void displayWakeup();

// Touch functions
bool isTouchPressed();
bool getTouchPoint(int16_t &x, int16_t &y);
void calibrateTouch();

// Sensor functions
int readLDR();
float readTemperature();
bool isBootButtonPressed();

// Audio functions
void playTone(uint16_t frequency, uint16_t duration);
void playBeep();
void playSuccess();
void playError();

// Power management
void enterDeepSleep();
void enterLightSleep();
void setCPUFrequency(uint32_t freq);

// Hardware status
struct HardwareStatus {
  bool displayReady = false;
  bool touchReady = false;
  bool sensorsReady = false;
  bool audioReady = false;
  uint8_t brightness = 80;
  int16_t lastTouchX = -1;
  int16_t lastTouchY = -1;
  uint32_t lastTouchTime = 0;
  bool touchPressed = false;
  int ldrValue = 0;
  float temperature = 0.0f;
  bool bootButtonPressed = false;
  uint32_t bootButtonPressTime = 0;
};

extern HardwareStatus hwStatus;

#endif // HARDWARE_H
