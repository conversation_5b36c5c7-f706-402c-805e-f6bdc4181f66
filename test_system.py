#!/usr/bin/env python3
"""
Test script for DESS Monitor system
Tests individual components without requiring actual login
"""

import json
import os
import sys
from datetime import datetime
import requests

def test_imports():
    """Test if all required modules can be imported"""
    print("🔍 Testing imports...")
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from webdriver_manager.chrome import ChromeDriverManager
        import pandas as pd
        import schedule
        from config import Config
        from dess_monitor import DESSMonitor
        print("✅ All imports successful")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_config():
    """Test configuration loading"""
    print("\n🔍 Testing configuration...")
    try:
        from config import Config
        config = Config()
        print(f"✅ DESS_USERNAME: {config.DESS_USERNAME}")
        print(f"✅ DEVICE_CODE: {config.DEVICE_CODE}")
        print(f"✅ DATA_SAVE_PATH: {config.DATA_SAVE_PATH}")
        return True
    except Exception as e:
        print(f"❌ Config error: {e}")
        return False

def test_chrome_driver():
    """Test Chrome WebDriver setup"""
    print("\n🔍 Testing Chrome WebDriver...")
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service
        from selenium.webdriver.chrome.options import Options
        from webdriver_manager.chrome import ChromeDriverManager
        
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Test basic functionality
        driver.get("https://www.google.com")
        title = driver.title
        driver.quit()
        
        print(f"✅ Chrome WebDriver working, test page title: {title}")
        return True
    except Exception as e:
        print(f"❌ Chrome WebDriver error: {e}")
        return False

def test_data_parsing():
    """Test data parsing with sample data"""
    print("\n🔍 Testing data parsing...")
    try:
        from dess_monitor import DESSMonitor
        
        # Sample data (similar to what we got from the API)
        sample_data = {
            "err": 0,
            "desc": "ERR_NONE",
            "dat": {
                "gts": "2025-05-25 15:46:34",
                "pars": {
                    "gd_": [
                        {"id": "gd_eybond_read_15", "par": "Grid Voltage", "val": "227.5", "unit": "V"},
                        {"id": "gd_eybond_read_16", "par": "Grid Frequency", "val": "50.04", "unit": "Hz"}
                    ],
                    "pv_": [
                        {"id": "pv_eybond_read_32", "par": "PV Voltage", "val": "324.1", "unit": "V"},
                        {"id": "pv_output_power", "par": "PV Power", "val": "365", "unit": "W"}
                    ],
                    "bt_": [
                        {"id": "bt_eybond_read_28", "par": "Battery Voltage", "val": "55.9", "unit": "V"}
                    ]
                }
            }
        }
        
        monitor = DESSMonitor()
        parsed_data = monitor.parse_device_data(sample_data)
        
        if parsed_data:
            print("✅ Data parsing successful")
            print(f"   Timestamp: {parsed_data['timestamp']}")
            print(f"   Grid Voltage: {parsed_data['grid_voltage']['value']} {parsed_data['grid_voltage']['unit']}")
            print(f"   PV Power: {parsed_data['pv_power']['value']} {parsed_data['pv_power']['unit']}")
            return True
        else:
            print("❌ Data parsing failed")
            return False
    except Exception as e:
        print(f"❌ Data parsing error: {e}")
        return False

def test_data_saving():
    """Test data saving functionality"""
    print("\n🔍 Testing data saving...")
    try:
        from dess_monitor import DESSMonitor
        
        # Sample parsed data
        test_data = {
            'timestamp': '2025-05-25 15:46:34',
            'collected_at': datetime.now().isoformat(),
            'grid_voltage': {'value': '227.5', 'unit': 'V', 'id': 'gd_eybond_read_15'},
            'pv_power': {'value': '365', 'unit': 'W', 'id': 'pv_output_power'},
            'battery_voltage': {'value': '55.9', 'unit': 'V', 'id': 'bt_eybond_read_28'}
        }
        
        monitor = DESSMonitor()
        
        # Test CSV saving
        csv_success = monitor.save_data_to_csv(test_data)
        
        # Test JSON saving
        json_success = monitor.save_data_to_json(test_data)
        
        if csv_success and json_success:
            print("✅ Data saving successful")
            return True
        else:
            print("❌ Data saving failed")
            return False
    except Exception as e:
        print(f"❌ Data saving error: {e}")
        return False

def test_api_request():
    """Test API request (using the provided URL)"""
    print("\n🔍 Testing API request...")
    try:
        # Use the URL you provided earlier
        url = "https://web.dessmonitor.com/public/?sign=8743221c28ad40664baa48193bbf4b03caa726f1&salt=1748162984217&token=c8075906465cca180724b25d151680e31e2267ff1319ccf2b43c88ba979a7576&action=querySPDeviceLastData&source=1&devcode=2376&pn=Q0046526082082&devaddr=1&sn=Q0046526082082094801&i18n=en_US"
        
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('err') == 0:
                print("✅ API request successful")
                print(f"   Timestamp: {data['dat']['gts']}")
                print(f"   Parameters found: {len(data['dat']['pars'])}")
                return True
            else:
                print(f"❌ API error: {data.get('desc', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API request error: {e}")
        return False

def test_data_analyzer():
    """Test data analyzer functionality"""
    print("\n🔍 Testing data analyzer...")
    try:
        from data_analyzer import DataAnalyzer
        
        analyzer = DataAnalyzer()
        print("✅ Data analyzer initialized successfully")
        return True
    except Exception as e:
        print(f"❌ Data analyzer error: {e}")
        return False

def run_all_tests():
    """Run all tests"""
    print("🚀 Starting DESS Monitor System Tests\n")
    
    tests = [
        ("Imports", test_imports),
        ("Configuration", test_config),
        ("Chrome WebDriver", test_chrome_driver),
        ("Data Parsing", test_data_parsing),
        ("Data Saving", test_data_saving),
        ("API Request", test_api_request),
        ("Data Analyzer", test_data_analyzer)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*50)
    print("📊 TEST SUMMARY")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready to use.")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
