#!/usr/bin/env python3
"""
Direct API Data Collector
Collects data directly from DESS Monitor API without browser automation
"""

import json
import time
import logging
import requests
import pandas as pd
import schedule
from datetime import datetime
from config import Config
import os

class DirectAPICollector:
    def __init__(self, api_url=None):
        self.config = Config()
        # Use the working API URL you provided
        self.api_url = api_url or "https://web.dessmonitor.com/public/?sign=8743221c28ad40664baa48193bbf4b03caa726f1&salt=1748162984217&token=c8075906465cca180724b25d151680e31e2267ff1319ccf2b43c88ba979a7576&action=querySPDeviceLastData&source=1&devcode=2376&pn=Q0046526082082&devaddr=1&sn=Q0046526082082094801&i18n=en_US"
        self.setup_logging()
        self.setup_data_directory()
        
    def setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=getattr(logging, self.config.LOG_LEVEL),
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('direct_api_collector.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_data_directory(self):
        """Create data directory if it doesn't exist"""
        if not os.path.exists(self.config.DATA_SAVE_PATH):
            os.makedirs(self.config.DATA_SAVE_PATH)
            
    def fetch_device_data(self):
        """Fetch device data from API"""
        try:
            self.logger.info("Fetching data from DESS Monitor API...")
            response = requests.get(self.api_url, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('err') == 0:
                    self.logger.info("Device data fetched successfully")
                    return data
                else:
                    self.logger.error(f"API error: {data.get('desc', 'Unknown error')}")
                    return None
            else:
                self.logger.error(f"HTTP error: {response.status_code}")
                return None
                
        except Exception as e:
            self.logger.error(f"Failed to fetch device data: {e}")
            return None
            
    def parse_device_data(self, raw_data):
        """Parse raw device data into structured format"""
        try:
            if not raw_data or 'dat' not in raw_data:
                return None
                
            parsed_data = {
                'timestamp': raw_data['dat']['gts'],
                'collected_at': datetime.now().isoformat()
            }
            
            # Parse different parameter groups
            for group_key, group_data in raw_data['dat']['pars'].items():
                for param in group_data:
                    param_name = param['par'].replace(' ', '_').lower()
                    parsed_data[param_name] = {
                        'value': param['val'],
                        'unit': param.get('unit', ''),
                        'id': param['id']
                    }
            
            return parsed_data
            
        except Exception as e:
            self.logger.error(f"Failed to parse device data: {e}")
            return None
            
    def save_data_to_csv(self, data):
        """Save data to CSV file"""
        try:
            if not data:
                return False
                
            # Flatten data for CSV
            flat_data = {'timestamp': data['timestamp'], 'collected_at': data['collected_at']}
            
            for key, value in data.items():
                if isinstance(value, dict) and 'value' in value:
                    flat_data[key] = value['value']
                    flat_data[f"{key}_unit"] = value.get('unit', '')
            
            # Create DataFrame
            df = pd.DataFrame([flat_data])
            
            # Save to CSV
            filename = f"{self.config.DATA_SAVE_PATH}/inverter_data_{datetime.now().strftime('%Y%m%d')}.csv"
            
            # Append to existing file or create new one
            if os.path.exists(filename):
                df.to_csv(filename, mode='a', header=False, index=False, lineterminator='\n')
            else:
                df.to_csv(filename, index=False, lineterminator='\n')
                
            self.logger.info(f"Data saved to {filename}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save data to CSV: {e}")
            return False
            
    def save_data_to_json(self, data):
        """Save data to JSON file"""
        try:
            if not data:
                return False
                
            filename = f"{self.config.DATA_SAVE_PATH}/inverter_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            with open(filename, 'w') as f:
                json.dump(data, f, indent=2)
                
            self.logger.info(f"Data saved to {filename}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save data to JSON: {e}")
            return False
            
    def collect_data_once(self):
        """Collect data once"""
        try:
            self.logger.info("Starting data collection...")
            
            # Fetch raw data
            raw_data = self.fetch_device_data()
            if not raw_data:
                return False
                
            # Parse data
            parsed_data = self.parse_device_data(raw_data)
            if not parsed_data:
                return False
                
            # Display current data
            self.display_current_data(parsed_data)
                
            # Save data
            self.save_data_to_csv(parsed_data)
            self.save_data_to_json(parsed_data)
            
            self.logger.info("Data collection completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Data collection failed: {e}")
            return False
            
    def display_current_data(self, data):
        """Display current data in a nice format"""
        print(f"\n📊 Current Inverter Data ({data['timestamp']}):")
        print("="*60)
        
        for key, value in data.items():
            if isinstance(value, dict) and 'value' in value:
                print(f"{key.replace('_', ' ').title():<30}: {value['value']} {value['unit']}")
        print("="*60)
        
    def run_continuous(self):
        """Run continuous data collection"""
        self.logger.info("Starting continuous data collection...")
        
        # Schedule data collection
        interval_minutes = self.config.COLLECTION_INTERVAL // 60
        schedule.every(interval_minutes).minutes.do(self.collect_data_once)
        
        # Run initial collection
        self.collect_data_once()
        
        # Keep running scheduled jobs
        try:
            while True:
                schedule.run_pending()
                time.sleep(1)
        except KeyboardInterrupt:
            self.logger.info("Stopping continuous collection...")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Direct API Data Collector')
    parser.add_argument('--mode', choices=['once', 'continuous'], default='once',
                       help='Collection mode: once or continuous')
    parser.add_argument('--interval', type=int, default=300,
                       help='Collection interval in seconds (for continuous mode)')
    parser.add_argument('--url', type=str, 
                       help='Custom API URL (optional)')
    
    args = parser.parse_args()
    
    # Update config
    Config.COLLECTION_INTERVAL = args.interval
    
    try:
        collector = DirectAPICollector(api_url=args.url)
        
        if args.mode == 'once':
            print("Running single data collection...")
            success = collector.collect_data_once()
            if success:
                print("✅ Data collection completed successfully!")
                return 0
            else:
                print("❌ Data collection failed!")
                return 1
                
        elif args.mode == 'continuous':
            print(f"Starting continuous data collection (interval: {args.interval}s)...")
            print("Press Ctrl+C to stop")
            collector.run_continuous()
            return 0
            
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        return 0
    except Exception as e:
        print(f"Error: {e}")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
