#ifndef DATA_TYPES_H
#define DATA_TYPES_H

#include <Arduino.h>

struct InverterData {
  // Grid data
  float gridVoltage = 0.0;
  float gridFrequency = 0.0;
  float gridPower = 0.0;
  
  // PV data
  float pvVoltage = 0.0;
  float pvCurrent = 0.0;
  float pvPower = 0.0;
  float pvChargingCurrent = 0.0;
  
  // Battery data
  float batteryVoltage = 0.0;
  float batteryCurrent = 0.0;
  
  // Output data
  float outputVoltage = 0.0;
  float outputCurrent = 0.0;
  float outputPower = 0.0;
  float outputApparentPower = 0.0;
  float loadPercent = 0.0;
  float outputFrequency = 0.0;
  
  // System data
  int dcTemp = 0;
  int invTemp = 0;
  String operatingMode = "";
  String outputPriority = "";
  String chargerSourcePriority = "";
  
  // Charging data
  float acChargingCurrent = 0.0;
  
  // Metadata
  String timestamp = "";
  unsigned long lastUpdate = 0;
  bool dataValid = false;
  int errorCount = 0;
  
  // Helper methods
  bool isCharging() const {
    return batteryCurrent > 0.1; // Positive current = charging
  }
  
  bool isDischarging() const {
    return batteryCurrent < -0.1; // Negative current = discharging
  }
  
  float getBatteryLevel() const {
    // Estimate battery level based on voltage (48V system)
    // This is a rough estimate and should be calibrated for your specific battery
    float minVoltage = 48.0; // 0%
    float maxVoltage = 58.4; // 100%
    
    float level = ((batteryVoltage - minVoltage) / (maxVoltage - minVoltage)) * 100.0;
    return constrain(level, 0.0, 100.0);
  }
  
  bool isGridAvailable() const {
    return gridVoltage > 100.0; // Assume grid is available if voltage > 100V
  }
  
  bool isPVGenerating() const {
    return pvPower > 10.0; // Assume PV is generating if power > 10W
  }
  
  String getOperatingModeShort() const {
    if (operatingMode.indexOf("Off-Grid") >= 0) return "OFF-GRID";
    if (operatingMode.indexOf("Grid-Tie") >= 0) return "GRID-TIE";
    if (operatingMode.indexOf("Hybrid") >= 0) return "HYBRID";
    return operatingMode.substring(0, 10); // Truncate long names
  }
  
  void reset() {
    gridVoltage = 0.0;
    gridFrequency = 0.0;
    gridPower = 0.0;
    pvVoltage = 0.0;
    pvCurrent = 0.0;
    pvPower = 0.0;
    pvChargingCurrent = 0.0;
    batteryVoltage = 0.0;
    batteryCurrent = 0.0;
    outputVoltage = 0.0;
    outputCurrent = 0.0;
    outputPower = 0.0;
    outputApparentPower = 0.0;
    loadPercent = 0.0;
    outputFrequency = 0.0;
    dcTemp = 0;
    invTemp = 0;
    operatingMode = "";
    outputPriority = "";
    chargerSourcePriority = "";
    acChargingCurrent = 0.0;
    timestamp = "";
    dataValid = false;
  }
};

enum class ConnectionStatus {
  DISCONNECTED,
  CONNECTING,
  CONNECTED,
  ERROR
};

enum class DataStatus {
  NO_DATA,
  FETCHING,
  VALID,
  ERROR,
  TIMEOUT
};

struct SystemStatus {
  ConnectionStatus wifiStatus = ConnectionStatus::DISCONNECTED;
  DataStatus dataStatus = DataStatus::NO_DATA;
  String lastError = "";
  unsigned long uptime = 0;
  int wifiRSSI = 0;
  IPAddress ipAddress;
  
  void updateUptime() {
    uptime = millis();
  }
  
  String getUptimeString() const {
    unsigned long seconds = uptime / 1000;
    unsigned long minutes = seconds / 60;
    unsigned long hours = minutes / 60;
    
    seconds %= 60;
    minutes %= 60;
    hours %= 24;
    
    char buffer[20];
    sprintf(buffer, "%02lu:%02lu:%02lu", hours, minutes, seconds);
    return String(buffer);
  }
};

#endif // DATA_TYPES_H
