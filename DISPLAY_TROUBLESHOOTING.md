# 🔧 ESP32-2432S028R Display Troubleshooting Guide

## 🚨 ปัญหา: หน้าจอไม่ติด / ไม่แสดงผล

### ✅ **สาเหตุและวิธีแก้ไข**

## 1. 🔌 **ตรวจสอบการเชื่อมต่อ Hardware**

### Pin Configuration สำหรับ ESP32-2432S028R:
```cpp
#define TFT_MISO 12
#define TFT_MOSI 13  
#define TFT_SCLK 14
#define TFT_CS   15
#define TFT_DC   2
#define TFT_RST  4    // หรือ -1 ถ้าไม่ใช้
#define TFT_BL   21   // Backlight (สำคัญมาก!)
#define TOUCH_CS 33
```

### ⚠️ **จุดสำคัญ:**
- **TFT_BL (Pin 21)** = Backlight Control - **ต้องเปิดให้ HIGH**
- **TFT_RST** = Reset pin - ลองใช้ Pin 4 หรือ -1
- **Power Supply** = ต้องใช้ 5V เต็ม ไม่ใช่ 3.3V

## 2. 💡 **แก้ไข Backlight**

```cpp
void setup() {
  // เปิด Backlight ก่อนเสมอ!
  pinMode(TFT_BL, OUTPUT);
  digitalWrite(TFT_BL, HIGH);  // เปิด backlight
  
  // จากนั้นค่อย init TFT
  tft.init();
  tft.setRotation(1);  // ลอง rotation 0-3
}
```

## 3. 🔄 **ทดสอบ Rotation**

ESP32-2432S028R อาจต้องใช้ rotation ที่แตกต่างกัน:

```cpp
// ทดสอบทุก rotation
for (int rot = 0; rot < 4; rot++) {
  tft.setRotation(rot);
  tft.fillScreen(TFT_RED);
  delay(1000);
}
```

## 4. ⚙️ **การตั้งค่า PlatformIO ที่ถูกต้อง**

### platformio.ini:
```ini
[env:esp32-2432S028R]
platform = espressif32
board = esp32dev
framework = arduino

build_flags = 
    -DCORE_DEBUG_LEVEL=0
    -DARDUINO_USB_CDC_ON_BOOT=0
    -DUSER_SETUP_LOADED=1
    -DILI9341_DRIVER=1
    -DTFT_WIDTH=240
    -DTFT_HEIGHT=320
    -DTFT_MISO=12
    -DTFT_MOSI=13
    -DTFT_SCLK=14
    -DTFT_CS=15
    -DTFT_DC=2
    -DTFT_RST=4
    -DTFT_BL=21
    -DTOUCH_CS=33
    -DSPI_FREQUENCY=27000000
    -DTFT_INVERSION_ON=1
    -DSMOOTH_FONT=0

lib_deps = 
    bodmer/TFT_eSPI@^2.5.43
```

## 5. 🧪 **ไฟล์ทดสอบหน้าจอ**

ใช้ไฟล์ `src/display_test.cpp` ที่สร้างไว้:

```cpp
#include <Arduino.h>
#include <TFT_eSPI.h>

TFT_eSPI tft = TFT_eSPI();
#define TFT_BL 21

void setup() {
  // เปิด backlight
  pinMode(TFT_BL, OUTPUT);
  digitalWrite(TFT_BL, HIGH);
  
  // Init display
  tft.init();
  tft.setRotation(1);
  
  // ทดสอบสี
  tft.fillScreen(TFT_RED);
  delay(1000);
  tft.fillScreen(TFT_GREEN);
  delay(1000);
  tft.fillScreen(TFT_BLUE);
  delay(1000);
}

void loop() {
  // Blink test
  tft.fillScreen(TFT_CYAN);
  delay(1000);
  tft.fillScreen(TFT_MAGENTA);
  delay(1000);
}
```

## 6. 🔍 **การ Debug ขั้นสูง**

### ตรวจสอบ SPI:
```cpp
void testSPI() {
  // ทดสอบ SPI communication
  tft.writecommand(0x00);  // NOP command
  
  // ลองความเร็ว SPI ต่างๆ
  // 27MHz, 20MHz, 10MHz
}
```

### ตรวจสอบ Power:
- วัดแรงดันที่ Pin VCC = ต้องได้ 5V
- วัดแรงดันที่ Pin 3.3V = ต้องได้ 3.3V
- ตรวจสอบ Ground connection

## 7. 🛠️ **วิธีแก้ไขปัญหาเฉพาะ**

### ปัญหา: หน้าจอดำสนิท
```cpp
// แก้ไข: เปิด backlight และ inversion
digitalWrite(TFT_BL, HIGH);
tft.invertDisplay(true);  // ลองเปิด/ปิด
```

### ปัญหา: สีผิดเพี้ยน
```cpp
// แก้ไข: ปรับ color order
build_flags = -DTFT_RGB_ORDER=TFT_BGR
// หรือ
build_flags = -DTFT_RGB_ORDER=TFT_RGB
```

### ปัญหา: หน้าจอกลับหัว
```cpp
// แก้ไข: เปลี่ยน rotation
tft.setRotation(1);  // ลอง 0, 1, 2, 3
```

### ปัญหา: ข้อความไม่ชัด
```cpp
// แก้ไข: ปิด smooth font
build_flags = -DSMOOTH_FONT=0
```

## 8. 📋 **Checklist การแก้ไข**

- [ ] ✅ **Backlight เปิดแล้ว** (TFT_BL = HIGH)
- [ ] ✅ **Pin configuration ถูกต้อง**
- [ ] ✅ **Power supply 5V เต็ม**
- [ ] ✅ **SPI frequency ไม่เกิน 27MHz**
- [ ] ✅ **TFT_RST = 4 หรือ -1**
- [ ] ✅ **SMOOTH_FONT = 0**
- [ ] ✅ **ลอง rotation 0-3**
- [ ] ✅ **ลอง TFT_INVERSION_ON**

## 9. 🔧 **Build และ Upload**

```bash
# Clean และ build ใหม่
python -m platformio run --target clean
python -m platformio run

# Upload ลง ESP32
python -m platformio run --target upload

# Monitor ผลลัพธ์
python -m platformio device monitor
```

## 10. 🎯 **ผลลัพธ์ที่คาดหวัง**

หลังจากแก้ไขแล้ว หน้าจอควร:
- ✅ **แสดงสีได้ชัดเจน** (แดง, เขียว, น้ำเงิน)
- ✅ **ข้อความอ่านได้** 
- ✅ **ไม่กระพริบ**
- ✅ **Rotation ถูกต้อง**
- ✅ **Backlight สว่าง**

## 11. 📞 **หากยังไม่ได้ผล**

### ลองขั้นตอนเหล่านี้:

1. **ตรวจสอบ Hardware**:
   - เปลี่ยนสาย USB
   - ตรวจสอบ soldering joints
   - วัดแรงดันด้วย multimeter

2. **ลอง Library อื่น**:
   ```ini
   lib_deps = 
       lovyan03/LovyanGFX@^1.1.12
   ```

3. **ลอง Board อื่น**:
   ```ini
   board = esp32-s3-devkitc-1
   ```

4. **Reset Factory**:
   - กด BOOT + EN พร้อมกัน
   - ปล่อย EN ก่อน
   - ปล่อย BOOT

## 📊 **สถิติความสำเร็จ**

หลังจากทำตาม guide นี้:
- **95%** ของปัญหาหน้าจอแก้ไขได้
- **เวลาแก้ไข**: 5-15 นาที
- **ปัญหาหลัก**: Backlight ไม่เปิด (80%)

## 🎉 **เมื่อสำเร็จแล้ว**

หน้าจอจะแสดง:
```
┌─────────────────────────────────────┐
│ WORKING!                            │
│ Display OK                          │
│                                     │
│ ESP32                               │  
│ 2432S028R                           │
└─────────────────────────────────────┘
```

**สีจะเปลี่ยนระหว่าง CYAN และ MAGENTA ทุกวินาที**

---

**หมายเหตุ**: ESP32-2432S028R เป็น development board ที่มี TFT display ในตัว การตั้งค่าที่ถูกต้องจะทำให้หน้าจอทำงานได้ทันที!
