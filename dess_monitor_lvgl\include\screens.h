#ifndef SCREENS_H
#define SCREENS_H

#include <lvgl.h>
#include "data_types.h"
#include "ui_manager.h"

// Forward declarations
class SplashScreen;
class MainScreen;
class SettingsScreen;
class WiFiConfigScreen;
class AboutScreen;
class ErrorScreen;

// Base screen class
class BaseScreen {
protected:
  lv_obj_t* screen;
  lv_obj_t* container;
  bool isVisible;
  bool isCreated;
  uint32_t createTime;
  
public:
  BaseScreen();
  virtual ~BaseScreen();
  
  // Virtual methods to be implemented by derived classes
  virtual void create() = 0;
  virtual void destroy();
  virtual void show(AnimationType animation = AnimationType::FADE_IN);
  virtual void hide(AnimationType animation = AnimationType::FADE_OUT);
  virtual void update(const InverterData& data) {}
  virtual void handleEvent(lv_event_t* e) {}
  
  // Common methods
  lv_obj_t* getScreen() { return screen; }
  bool getIsVisible() { return isVisible; }
  bool getIsCreated() { return isCreated; }
  
protected:
  // Helper methods for derived classes
  void createContainer();
  void setupEventHandlers();
  lv_obj_t* createHeader(const char* title, bool showBackButton = false);
  lv_obj_t* createFooter();
};

// Splash Screen
class SplashScreen : public BaseScreen {
private:
  lv_obj_t* logoLabel;
  lv_obj_t* versionLabel;
  lv_obj_t* loadingSpinner;
  lv_obj_t* statusLabel;
  
public:
  SplashScreen();
  void create() override;
  void setStatus(const String& status);
  void setProgress(uint8_t progress);
  void startAnimation();
};

// Main Screen
class MainScreen : public BaseScreen {
private:
  // Header components
  lv_obj_t* headerPanel;
  lv_obj_t* titleLabel;
  lv_obj_t* timeLabel;
  lv_obj_t* wifiIcon;
  lv_obj_t* signalBars;
  
  // Data cards
  lv_obj_t* solarCard;
  lv_obj_t* batteryCard;
  lv_obj_t* gridCard;
  lv_obj_t* loadCard;
  
  // Card components
  struct CardComponents {
    lv_obj_t* card;
    lv_obj_t* icon;
    lv_obj_t* title;
    lv_obj_t* value;
    lv_obj_t* unit;
    lv_obj_t* status;
    lv_obj_t* progressBar;
    lv_obj_t* details;
  };
  
  CardComponents solarComponents;
  CardComponents batteryComponents;
  CardComponents gridComponents;
  CardComponents loadComponents;
  
  // Status bar
  lv_obj_t* statusBar;
  lv_obj_t* dataStatus;
  lv_obj_t* lastUpdate;
  
public:
  MainScreen();
  void create() override;
  void update(const InverterData& data) override;
  void handleEvent(lv_event_t* e) override;
  
  // Card updates
  void updateSolarCard(const InverterData& data);
  void updateBatteryCard(const InverterData& data);
  void updateGridCard(const InverterData& data);
  void updateLoadCard(const InverterData& data);
  
  // Header updates
  void updateHeader(const String& time, bool wifiConnected, int signalStrength);
  void updateStatusBar(DataStatus status, const String& lastUpdateTime);
  
private:
  CardComponents createCard(const char* title, const char* icon, lv_color_t color, lv_coord_t x, lv_coord_t y);
  void animateCard(CardComponents& card, bool highlight = false);
  void updateCardValue(CardComponents& card, float value, const char* unit, lv_color_t color);
  void updateCardStatus(CardComponents& card, const char* status, lv_color_t color);
};

// Settings Screen
class SettingsScreen : public BaseScreen {
private:
  lv_obj_t* settingsPanel;
  lv_obj_t* closeButton;
  
  // Settings sections
  lv_obj_t* themeSection;
  lv_obj_t* displaySection;
  lv_obj_t* networkSection;
  lv_obj_t* systemSection;
  
  // Settings controls
  lv_obj_t* themeDropdown;
  lv_obj_t* brightnessSlider;
  lv_obj_t* autoThemeSwitch;
  lv_obj_t* animationSwitch;
  lv_obj_t* soundSwitch;
  lv_obj_t* wifiButton;
  lv_obj_t* resetButton;
  
public:
  SettingsScreen();
  void create() override;
  void handleEvent(lv_event_t* e) override;
  
  // Settings management
  void loadSettings();
  void saveSettings();
  void resetSettings();
  
private:
  lv_obj_t* createSection(const char* title);
  lv_obj_t* createSettingItem(lv_obj_t* parent, const char* label, lv_obj_t* control);
  void onThemeChanged();
  void onBrightnessChanged();
  void onSwitchToggled(lv_obj_t* sw);
};

// WiFi Configuration Screen
class WiFiConfigScreen : public BaseScreen {
private:
  lv_obj_t* wifiPanel;
  lv_obj_t* statusLabel;
  lv_obj_t* networkList;
  lv_obj_t* connectButton;
  lv_obj_t* passwordTextArea;
  lv_obj_t* showPasswordCheckbox;
  lv_obj_t* scanButton;
  lv_obj_t* manualButton;
  
  String selectedSSID;
  bool isScanning;
  
public:
  WiFiConfigScreen();
  void create() override;
  void handleEvent(lv_event_t* e) override;
  
  // WiFi management
  void scanNetworks();
  void connectToNetwork(const String& ssid, const String& password);
  void showManualConfig();
  void updateStatus(const String& status, lv_color_t color);
  
private:
  void populateNetworkList();
  void onNetworkSelected(const String& ssid);
  void onConnectClicked();
  void onScanClicked();
};

// About Screen
class AboutScreen : public BaseScreen {
private:
  lv_obj_t* aboutPanel;
  lv_obj_t* logoImage;
  lv_obj_t* appNameLabel;
  lv_obj_t* versionLabel;
  lv_obj_t* authorLabel;
  lv_obj_t* descriptionLabel;
  lv_obj_t* featuresLabel;
  lv_obj_t* licenseLabel;
  
public:
  AboutScreen();
  void create() override;
  void handleEvent(lv_event_t* e) override;
};

// Error Screen
class ErrorScreen : public BaseScreen {
private:
  lv_obj_t* errorPanel;
  lv_obj_t* errorIcon;
  lv_obj_t* errorTitle;
  lv_obj_t* errorMessage;
  lv_obj_t* errorDetails;
  lv_obj_t* retryButton;
  lv_obj_t* homeButton;
  
  String lastError;
  
public:
  ErrorScreen();
  void create() override;
  void handleEvent(lv_event_t* e) override;
  
  // Error management
  void showError(const String& title, const String& message, const String& details = "");
  void showNetworkError();
  void showDataError();
  void showSystemError();
  
private:
  void onRetryClicked();
  void onHomeClicked();
};

// Global screen instances
extern SplashScreen splashScreen;
extern MainScreen mainScreen;
extern SettingsScreen settingsScreen;
extern WiFiConfigScreen wifiConfigScreen;
extern AboutScreen aboutScreen;
extern ErrorScreen errorScreen;

// Screen management functions
void initializeScreens();
void destroyScreens();
BaseScreen* getScreen(ScreenType type);

#endif // SCREENS_H
