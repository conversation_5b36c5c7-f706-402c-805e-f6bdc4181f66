@echo off
echo Installing DESS Monitor Data Collector...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo Python found, installing dependencies...
pip install -r requirements.txt

if errorlevel 1 (
    echo Error: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo Creating environment file...
if not exist .env (
    copy .env.example .env
    echo Please edit .env file with your DESS Monitor credentials
) else (
    echo .env file already exists
)

echo.
echo Creating data directory...
if not exist inverter_data mkdir inverter_data

echo.
echo Installation completed!
echo.
echo Next steps:
echo 1. Edit .env file with your DESS Monitor username and password
echo 2. Edit config.py with your device information
echo 3. Run: python main.py --mode once
echo.
pause
