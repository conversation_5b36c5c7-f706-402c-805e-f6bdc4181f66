# 🌟 DESS Monitor Pro LVGL v2.0

**Professional Solar Inverter Monitoring System with LVGL UI Framework**

## 📋 Overview

DESS Monitor Pro LVGL v2.0 is a complete rewrite of the original monitoring system, built with the powerful LVGL graphics library for smooth animations, professional UI design, and emoji support. This version features a modular architecture for easy maintenance and extensibility.

## ✨ Key Features

### 🎨 **Modern UI with LVGL**
- **Smooth 60 FPS animations** with hardware acceleration
- **Professional themes** (Dark, Light, Solar, Blue, Green)
- **Emoji support** for better visual communication
- **Touch gestures** and responsive design
- **Auto theme switching** based on time/light sensor

### 📊 **Real-time Data Monitoring**
- **Solar panel** voltage, current, and power
- **Battery** voltage, current, and capacity percentage
- **Grid** voltage, frequency, and power
- **Load** voltage, current, power, and percentage
- **System** temperatures and operating modes

### 🌐 **Advanced Networking**
- **WiFi Manager** with captive portal setup
- **Auto-reconnection** with retry logic
- **Signal strength monitoring**
- **HTTP data fetching** with error handling
- **Real-time status updates**

### 🎬 **Rich Animations**
- **Card animations** (flip, slide, bounce)
- **Screen transitions** (fade, slide, scale)
- **Data update animations** with smooth value changes
- **Loading spinners** and progress indicators
- **Gesture feedback** animations

## 📁 Project Structure

```
dess_monitor_lvgl/
├── platformio.ini          # PlatformIO configuration
├── include/                 # Header files
│   ├── lv_conf.h           # LVGL configuration
│   ├── config.h            # Hardware and app configuration
│   ├── data_types.h        # Data structures and types
│   ├── hardware.h          # Hardware abstraction layer
│   ├── network.h           # Network and API management
│   ├── ui_manager.h        # UI management and coordination
│   ├── themes.h            # Theme system and styling
│   ├── screens.h           # Screen definitions and management
│   └── animations.h        # Animation system
├── src/                    # Source files
│   ├── main.cpp            # Main application entry point
│   ├── hardware.cpp        # Hardware implementation
│   ├── network.cpp         # Network implementation
│   ├── ui_manager.cpp      # UI manager implementation
│   ├── themes.cpp          # Theme system implementation
│   ├── screens/            # Individual screen implementations
│   │   ├── splash_screen.cpp
│   │   ├── main_screen.cpp
│   │   ├── settings_screen.cpp
│   │   ├── wifi_config_screen.cpp
│   │   ├── about_screen.cpp
│   │   └── error_screen.cpp
│   └── animations.cpp      # Animation system implementation
└── README.md              # This file
```

## 🔧 Hardware Requirements

### **ESP32-2432S028R Development Board**
- **ESP32-D0WD-V3** microcontroller (240MHz, dual-core)
- **2.8" ILI9341 TFT Display** (320x240 pixels)
- **XPT2046 Touch Controller**
- **Built-in WiFi** and Bluetooth
- **LDR sensor** for auto-brightness
- **Speaker** for audio feedback

### **Pin Configuration**
```cpp
// Display pins
#define TFT_MISO   12
#define TFT_MOSI   13
#define TFT_SCLK   14
#define TFT_CS     15
#define TFT_DC     2
#define TFT_RST    4
#define TFT_BL     21

// Touch pins
#define XPT2046_IRQ  36
#define XPT2046_MOSI 32
#define XPT2046_MISO 39
#define XPT2046_CLK  25
#define XPT2046_CS   33

// Other pins
#define BOOT_BUTTON  0
#define LDR_PIN      34
#define SPEAKER_PIN  26
```

## 🚀 Getting Started

### **1. Prerequisites**
- **PlatformIO IDE** (VS Code extension recommended)
- **ESP32-2432S028R** development board
- **USB cable** for programming

### **2. Installation**
```bash
# Clone the repository
git clone <repository-url>
cd dess_monitor_lvgl

# Install dependencies (PlatformIO will handle this)
pio lib install

# Build the project
pio run

# Upload to device
pio run --target upload

# Monitor serial output
pio device monitor
```

### **3. First Setup**
1. **Power on** the device
2. **Connect to WiFi** using the configuration portal
3. **Configure API settings** if needed
4. **Enjoy** the professional monitoring interface!

## 🎨 UI Components

### **Screens**
- **Splash Screen**: Welcome animation and system initialization
- **Main Screen**: Real-time data display with animated cards
- **Settings Screen**: Theme, brightness, and system configuration
- **WiFi Config Screen**: Network setup and management
- **About Screen**: Application information and credits
- **Error Screen**: Error handling and recovery options

### **Themes**
- **Dark Theme**: Professional dark interface (default)
- **Light Theme**: Clean light interface
- **Solar Theme**: Solar-inspired colors
- **Blue Theme**: Cool blue tones
- **Green Theme**: Nature-inspired greens

### **Animations**
- **Fade In/Out**: Smooth opacity transitions
- **Slide**: Directional movement animations
- **Scale**: Size change animations
- **Bounce**: Elastic bounce effects
- **Rotate**: Rotation animations
- **Custom**: Specialized animations for data updates

## 🔧 Configuration

### **WiFi Setup**
The device creates a WiFi access point for initial configuration:
- **SSID**: `DESS_Monitor_Setup`
- **Password**: None (open network)
- **Portal**: `http://192.168.4.1`

### **API Configuration**
Edit `src/network.cpp` to configure your DESS Monitor API:
```cpp
const char* API_BASE_URL = "https://web.dessmonitor.com/public/";
const char* DEVICE_CODE = "your_device_code";
const char* DEVICE_SN = "your_serial_number";
```

### **Theme Customization**
Modify `src/themes.cpp` to create custom themes:
```cpp
ThemeColors customColors = {
  .background = lv_color_hex(0x000000),
  .primary = lv_color_hex(0x1E88E5),
  // ... more colors
};
```

## 🎯 Performance Features

### **Hardware Acceleration**
- **DMA transfers** for fast screen updates
- **Hardware byte swapping** for color processing
- **240MHz CPU frequency** for maximum performance
- **PSRAM support** for large graphics buffers

### **Memory Optimization**
- **Smart redraw system** updates only changed areas
- **Object pooling** for animations
- **Efficient data structures** minimize RAM usage
- **Garbage collection** prevents memory leaks

### **FreeRTOS Tasks**
- **Data Task**: Fetches inverter data (Core 0)
- **UI Task**: Handles interface updates (Core 1)
- **Network Task**: Manages WiFi connection (Core 0)

## 🐛 Troubleshooting

### **Common Issues**
1. **Display not working**: Check TFT connections and power
2. **Touch not responding**: Verify touch controller wiring
3. **WiFi connection fails**: Use configuration portal
4. **Data not updating**: Check API credentials and network
5. **Animations stuttering**: Ensure adequate power supply

### **Debug Mode**
Enable debug output in `platformio.ini`:
```ini
build_flags = 
    -DCORE_DEBUG_LEVEL=5
    -DLV_USE_LOG=1
    -DLV_LOG_LEVEL=LV_LOG_LEVEL_TRACE
```

## 📈 Future Enhancements

- **Data logging** to SD card
- **Historical charts** and graphs
- **Remote control** capabilities
- **Multiple device support**
- **Cloud synchronization**
- **Mobile app integration**

## 🤝 Contributing

Contributions are welcome! Please:
1. **Fork** the repository
2. **Create** a feature branch
3. **Commit** your changes
4. **Push** to the branch
5. **Create** a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **LVGL Team** for the amazing graphics library
- **Bodmer** for the excellent TFT_eSPI library
- **ESP32 Community** for hardware support and examples
- **PlatformIO** for the development environment

---

**Made with ❤️ for the solar energy community**
