#include "ui_manager.h"
#include "themes.h"
#include "animations.h"
#include "screens.h"
#include "hardware.h"

// Global UI manager instance
UIManager uiManager;

UIManager::UIManager() {
  currentScreen = ScreenType::SPLASH;
  previousScreen = ScreenType::SPLASH;
  themeManager = &::themeManager;
  animationsEnabled = true;
  lastUpdate = 0;
  isAnimating = false;
  animationStartTime = 0;
  animationDuration = 400;
  
  // Initialize screen pointers
  for (int i = 0; i < 6; i++) {
    screens[i] = nullptr;
  }
}

UIManager::~UIManager() {
  // Cleanup will be handled by LVGL
}

bool UIManager::initialize() {
  Serial.println("🖥️ Initializing UI Manager...");
  
  // Initialize theme manager
  themeManager->initialize();
  
  // Setup screens
  setupScreens();
  
  Serial.println("✅ UI Manager initialized successfully");
  return true;
}

void UIManager::setupScreens() {
  Serial.println("🖼️ Setting up screens...");
  
  // Initialize all screens
  splashScreen.create();
  mainScreen.create();
  settingsScreen.create();
  wifiConfigScreen.create();
  aboutScreen.create();
  errorScreen.create();
  
  // Store screen references
  screens[0] = splashScreen.getScreen();
  screens[1] = mainScreen.getScreen();
  screens[2] = settingsScreen.getScreen();
  screens[3] = wifiConfigScreen.getScreen();
  screens[4] = aboutScreen.getScreen();
  screens[5] = errorScreen.getScreen();
  
  Serial.println("✅ All screens created successfully");
}

void UIManager::showScreen(ScreenType screen, AnimationType animation) {
  if (isAnimating) {
    Serial.println("⚠️ Animation in progress, ignoring screen change request");
    return;
  }
  
  Serial.printf("🖼️ Showing screen: %d with animation: %d\n", (int)screen, (int)animation);
  
  previousScreen = currentScreen;
  currentScreen = screen;
  
  // Hide previous screen
  if (previousScreen != currentScreen) {
    hideScreen(previousScreen, AnimationType::FADE_OUT);
  }
  
  // Show new screen
  BaseScreen* targetScreen = getScreen(screen);
  if (targetScreen) {
    targetScreen->show(animation);
  }
  
  // Load the screen
  lv_scr_load(screens[(int)screen]);
}

void UIManager::hideScreen(ScreenType screen, AnimationType animation) {
  BaseScreen* targetScreen = getScreen(screen);
  if (targetScreen) {
    targetScreen->hide(animation);
  }
}

ScreenType UIManager::getCurrentScreen() {
  return currentScreen;
}

ScreenType UIManager::getPreviousScreen() {
  return previousScreen;
}

void UIManager::setAnimationsEnabled(bool enabled) {
  animationsEnabled = enabled;
  animationManager.setEnabled(enabled);
  Serial.printf("🎬 Animations %s\n", enabled ? "enabled" : "disabled");
}

bool UIManager::areAnimationsEnabled() {
  return animationsEnabled;
}

void UIManager::setAnimationDuration(uint32_t duration) {
  animationDuration = duration;
}

void UIManager::setTheme(ThemeType theme) {
  themeManager->setTheme(theme);
  
  // Apply theme to all screens
  for (int i = 0; i < 6; i++) {
    if (screens[i]) {
      themeManager->applyToObject(screens[i]);
    }
  }
  
  Serial.printf("🎨 Theme changed to: %d\n", (int)theme);
}

ThemeType UIManager::getCurrentTheme() {
  return themeManager->getCurrentTheme();
}

void UIManager::toggleTheme() {
  themeManager->nextTheme();
}

void UIManager::updateData(const InverterData& data) {
  // Update main screen with new data
  if (currentScreen == ScreenType::MAIN) {
    mainScreen.update(data);
  }
  
  lastUpdate = millis();
}

void UIManager::updateNetworkStatus(const NetworkInfo& networkInfo) {
  // Update WiFi status on main screen
  if (currentScreen == ScreenType::MAIN) {
    mainScreen.updateHeader("", networkInfo.status == NetworkStatus::CONNECTED, networkInfo.signalStrength);
  }
}

void UIManager::updateTime(const String& timeString) {
  // Update time display on main screen
  if (currentScreen == ScreenType::MAIN) {
    mainScreen.updateHeader(timeString, true, 0);
  }
}

void UIManager::handleTouch(int16_t x, int16_t y) {
  // Create touch event
  lv_indev_data_t data;
  data.point.x = x;
  data.point.y = y;
  data.state = LV_INDEV_STATE_PR;
  
  // Send to current screen
  BaseScreen* screen = getScreen(currentScreen);
  if (screen) {
    // Create a dummy event for the screen
    lv_event_t e;
    e.code = LV_EVENT_CLICKED;
    e.target = screen->getScreen();
    screen->handleEvent(&e);
  }
  
  Serial.printf("👆 Touch at (%d, %d)\n", x, y);
}

void UIManager::handleGesture(const TouchGesture& gesture) {
  switch (gesture.type) {
    case TouchGesture::SWIPE_LEFT:
      if (currentScreen == ScreenType::MAIN) {
        showScreen(ScreenType::SETTINGS, AnimationType::SLIDE_LEFT);
      }
      break;
      
    case TouchGesture::SWIPE_RIGHT:
      if (currentScreen == ScreenType::SETTINGS) {
        showScreen(ScreenType::MAIN, AnimationType::SLIDE_RIGHT);
      }
      break;
      
    case TouchGesture::SWIPE_DOWN:
      if (currentScreen == ScreenType::MAIN) {
        showScreen(ScreenType::SETTINGS, AnimationType::SLIDE_DOWN);
      }
      break;
      
    default:
      break;
  }
}

void UIManager::handleButton(uint8_t button) {
  if (button == 0) { // Boot button
    toggleTheme();
  }
}

void UIManager::showNotification(const String& message, lv_color_t color, uint32_t duration) {
  Serial.printf("📢 Notification: %s\n", message.c_str());
  
  // Create notification popup
  lv_obj_t* popup = lv_obj_create(lv_scr_act());
  lv_obj_set_size(popup, 280, 60);
  lv_obj_align(popup, LV_ALIGN_TOP_MID, 0, 20);
  lv_obj_set_style_bg_color(popup, color, 0);
  lv_obj_set_style_radius(popup, 10, 0);
  
  lv_obj_t* label = lv_label_create(popup);
  lv_label_set_text(label, message.c_str());
  lv_obj_center(label);
  lv_obj_set_style_text_color(label, lv_color_white(), 0);
  
  // Animate in
  if (animationsEnabled) {
    animationManager.slideIn(popup, LV_DIR_TOP, 300);
    
    // Auto-hide after duration
    lv_timer_t* timer = lv_timer_create([](lv_timer_t* timer) {
      lv_obj_t* popup = (lv_obj_t*)timer->user_data;
      animationManager.slideOut(popup, LV_DIR_TOP, 300);
      lv_timer_del(timer);
      
      // Delete popup after animation
      lv_timer_create([](lv_timer_t* timer) {
        lv_obj_del((lv_obj_t*)timer->user_data);
        lv_timer_del(timer);
      }, 300, (void*)popup);
    }, duration, popup);
  }
}

void UIManager::showError(const String& error) {
  showNotification("❌ " + error, lv_color_hex(0xF44336), 5000);
}

void UIManager::showSuccess(const String& message) {
  showNotification("✅ " + message, lv_color_hex(0x4CAF50), 3000);
}

void UIManager::showWarning(const String& warning) {
  showNotification("⚠️ " + warning, lv_color_hex(0xFFC107), 4000);
}

void UIManager::setWiFiStatus(bool connected, int signalStrength) {
  // Update WiFi status on current screen
  if (currentScreen == ScreenType::MAIN) {
    mainScreen.updateHeader("", connected, signalStrength);
  }
}

void UIManager::setDataStatus(DataStatus status) {
  String statusText;
  switch (status) {
    case DataStatus::VALID: statusText = "Data OK"; break;
    case DataStatus::ERROR: statusText = "Data Error"; break;
    case DataStatus::TIMEOUT: statusText = "Data Timeout"; break;
    case DataStatus::NO_DATA: statusText = "No Data"; break;
    default: statusText = "Unknown"; break;
  }
  
  if (currentScreen == ScreenType::MAIN) {
    mainScreen.updateStatusBar(status, statusText);
  }
}

void UIManager::setBatteryLevel(float level) {
  // Update battery display if needed
  Serial.printf("🔋 Battery level: %.1f%%\n", level);
}

void UIManager::enableScreenSaver(uint32_t timeout) {
  Serial.printf("😴 Screen saver enabled with %d ms timeout\n", timeout);
  // TODO: Implement screen saver
}

void UIManager::disableScreenSaver() {
  Serial.println("😊 Screen saver disabled");
  // TODO: Implement screen saver disable
}

void UIManager::resetScreenSaver() {
  // TODO: Reset screen saver timer
}

void UIManager::update() {
  // Handle LVGL timer tasks
  lv_timer_handler();
  
  // Update current screen
  BaseScreen* screen = getScreen(currentScreen);
  if (screen) {
    // Screen-specific updates can be added here
  }
  
  // Update animations
  if (animationsEnabled) {
    // Animation updates are handled by LVGL
  }
}

void UIManager::forceUpdate() {
  // Force a complete UI refresh
  lv_obj_invalidate(lv_scr_act());
  update();
}

// Private methods
void UIManager::createSplashScreen() {
  // Implementation moved to screens.cpp
}

void UIManager::createMainScreen() {
  // Implementation moved to screens.cpp
}

void UIManager::createSettingsScreen() {
  // Implementation moved to screens.cpp
}

void UIManager::createWiFiConfigScreen() {
  // Implementation moved to screens.cpp
}

void UIManager::createAboutScreen() {
  // Implementation moved to screens.cpp
}

void UIManager::createErrorScreen() {
  // Implementation moved to screens.cpp
}

void UIManager::startAnimation(lv_obj_t* obj, AnimationType type, uint32_t duration) {
  if (!animationsEnabled) return;
  
  isAnimating = true;
  animationStartTime = millis();
  animationDuration = duration;
  
  switch (type) {
    case AnimationType::FADE_IN:
      animationManager.fadeIn(obj, duration);
      break;
    case AnimationType::FADE_OUT:
      animationManager.fadeOut(obj, duration);
      break;
    case AnimationType::SLIDE_LEFT:
      animationManager.slideIn(obj, LV_DIR_LEFT, duration);
      break;
    case AnimationType::SLIDE_RIGHT:
      animationManager.slideIn(obj, LV_DIR_RIGHT, duration);
      break;
    case AnimationType::SLIDE_UP:
      animationManager.slideIn(obj, LV_DIR_TOP, duration);
      break;
    case AnimationType::SLIDE_DOWN:
      animationManager.slideIn(obj, LV_DIR_BOTTOM, duration);
      break;
    case AnimationType::SCALE_IN:
      animationManager.scaleIn(obj, duration);
      break;
    case AnimationType::SCALE_OUT:
      animationManager.scaleOut(obj, duration);
      break;
    default:
      isAnimating = false;
      break;
  }
}

void UIManager::stopAnimation(lv_obj_t* obj) {
  animationManager.stopAnimation(obj);
  isAnimating = false;
}

bool UIManager::isAnimationRunning() {
  return isAnimating || animationManager.getActiveAnimationCount() > 0;
}

// Event callbacks
void UIManager::screenEventCallback(lv_event_t* e) {
  // Handle screen events
}

void UIManager::buttonEventCallback(lv_event_t* e) {
  // Handle button events
}

void UIManager::animationCallback(void* var, int32_t val) {
  // Handle animation progress
}

void UIManager::animationReadyCallback(lv_anim_t* anim) {
  // Handle animation completion
  uiManager.isAnimating = false;
}
