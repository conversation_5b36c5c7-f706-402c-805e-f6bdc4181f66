#include "screens.h"
#include "themes.h"
#include "animations.h"
#include "ui_manager.h"

// Global screen instances
SplashScreen splashScreen;
MainScreen mainScreen;
SettingsScreen settingsScreen;
WiFiConfigScreen wifiConfigScreen;
AboutScreen aboutScreen;
ErrorScreen errorScreen;

// Base Screen Implementation
BaseScreen::BaseScreen() {
  screen = nullptr;
  container = nullptr;
  isVisible = false;
  isCreated = false;
  createTime = 0;
}

BaseScreen::~BaseScreen() {
  destroy();
}

void BaseScreen::destroy() {
  if (screen) {
    lv_obj_del(screen);
    screen = nullptr;
  }
  isCreated = false;
  isVisible = false;
}

void BaseScreen::show(AnimationType animation) {
  if (!isCreated) {
    create();
  }
  
  isVisible = true;
  
  if (animation != AnimationType::NONE) {
    animationManager.fadeIn(screen, ANIM_DURATION_NORMAL);
  }
}

void BaseScreen::hide(AnimationType animation) {
  isVisible = false;
  
  if (animation != AnimationType::NONE) {
    animationManager.fadeOut(screen, ANIM_DURATION_FAST);
  }
}

void BaseScreen::createContainer() {
  if (!screen) return;
  
  container = lv_obj_create(screen);
  lv_obj_set_size(container, LV_PCT(100), LV_PCT(100));
  lv_obj_set_style_pad_all(container, 0, 0);
  lv_obj_set_style_border_width(container, 0, 0);
  lv_obj_set_style_bg_opa(container, LV_OPA_TRANSP, 0);
}

lv_obj_t* BaseScreen::createHeader(const char* title, bool showBackButton) {
  if (!container) return nullptr;
  
  lv_obj_t* header = lv_obj_create(container);
  lv_obj_set_size(header, LV_PCT(100), 50);
  lv_obj_align(header, LV_ALIGN_TOP_MID, 0, 0);
  
  // Apply theme
  themeManager.applyCardStyle(header, themeManager.getCurrentColors().primary);
  
  // Title label
  lv_obj_t* titleLabel = lv_label_create(header);
  lv_label_set_text(titleLabel, title);
  lv_obj_set_style_text_font(titleLabel, &lv_font_montserrat_18, 0);
  lv_obj_center(titleLabel);
  themeManager.applyLabelStyle(titleLabel, false);
  
  // Back button if requested
  if (showBackButton) {
    lv_obj_t* backBtn = lv_btn_create(header);
    lv_obj_set_size(backBtn, 40, 30);
    lv_obj_align(backBtn, LV_ALIGN_LEFT_MID, 10, 0);
    
    lv_obj_t* backLabel = lv_label_create(backBtn);
    lv_label_set_text(backLabel, "←");
    lv_obj_center(backLabel);
    
    themeManager.applyButtonStyle(backBtn, false);
  }
  
  return header;
}

// Splash Screen Implementation
SplashScreen::SplashScreen() {
  logoLabel = nullptr;
  versionLabel = nullptr;
  loadingSpinner = nullptr;
  statusLabel = nullptr;
}

void SplashScreen::create() {
  if (isCreated) return;
  
  screen = lv_obj_create(nullptr);
  lv_obj_set_style_bg_color(screen, lv_color_black(), 0);
  
  createContainer();
  
  // Logo
  logoLabel = lv_label_create(container);
  lv_label_set_text(logoLabel, "⚡ DESS Monitor Pro");
  lv_obj_set_style_text_font(logoLabel, &lv_font_montserrat_28, 0);
  lv_obj_set_style_text_color(logoLabel, lv_color_white(), 0);
  lv_obj_align(logoLabel, LV_ALIGN_CENTER, 0, -40);
  
  // Version
  versionLabel = lv_label_create(container);
  lv_label_set_text(versionLabel, "LVGL v2.0");
  lv_obj_set_style_text_color(versionLabel, lv_color_hex(0x888888), 0);
  lv_obj_align(versionLabel, LV_ALIGN_CENTER, 0, -10);
  
  // Loading spinner
  loadingSpinner = lv_spinner_create(container, 1000, 60);
  lv_obj_set_size(loadingSpinner, 40, 40);
  lv_obj_align(loadingSpinner, LV_ALIGN_CENTER, 0, 30);
  lv_obj_set_style_arc_color(loadingSpinner, lv_color_hex(0x1E88E5), LV_PART_MAIN);
  
  // Status label
  statusLabel = lv_label_create(container);
  lv_label_set_text(statusLabel, "Initializing...");
  lv_obj_set_style_text_color(statusLabel, lv_color_hex(0xCCCCCC), 0);
  lv_obj_align(statusLabel, LV_ALIGN_CENTER, 0, 80);
  
  isCreated = true;
  createTime = millis();
  
  startAnimation();
}

void SplashScreen::setStatus(const String& status) {
  if (statusLabel) {
    lv_label_set_text(statusLabel, status.c_str());
  }
}

void SplashScreen::setProgress(uint8_t progress) {
  // Update progress if needed
}

void SplashScreen::startAnimation() {
  if (logoLabel) {
    animationManager.scaleIn(logoLabel, 800, 0);
  }
  if (versionLabel) {
    animationManager.fadeIn(versionLabel, 600, 400);
  }
  if (statusLabel) {
    animationManager.slideIn(statusLabel, LV_DIR_BOTTOM, 500, 800);
  }
}

// Main Screen Implementation
MainScreen::MainScreen() {
  headerPanel = nullptr;
  titleLabel = nullptr;
  timeLabel = nullptr;
  wifiIcon = nullptr;
  signalBars = nullptr;
  solarCard = nullptr;
  batteryCard = nullptr;
  gridCard = nullptr;
  loadCard = nullptr;
  statusBar = nullptr;
  dataStatus = nullptr;
  lastUpdate = nullptr;
}

void MainScreen::create() {
  if (isCreated) return;
  
  screen = lv_obj_create(nullptr);
  themeManager.applyToObject(screen);
  
  createContainer();
  
  // Create header
  headerPanel = createHeader("DESS Monitor Pro", false);
  
  // Create time label in header
  timeLabel = lv_label_create(headerPanel);
  lv_label_set_text(timeLabel, "00:00");
  lv_obj_align(timeLabel, LV_ALIGN_RIGHT_MID, -50, 0);
  themeManager.applyLabelStyle(timeLabel, false);
  
  // Create WiFi icon
  wifiIcon = lv_label_create(headerPanel);
  lv_label_set_text(wifiIcon, "📶");
  lv_obj_align(wifiIcon, LV_ALIGN_RIGHT_MID, -10, 0);
  
  // Create data cards
  solarComponents = createCard("☀️ Solar", "☀️", themeManager.getComponentColor("solar"), 10, 60);
  batteryComponents = createCard("🔋 Battery", "🔋", themeManager.getComponentColor("battery"), 170, 60);
  gridComponents = createCard("⚡ Grid", "⚡", themeManager.getComponentColor("grid"), 10, 140);
  loadComponents = createCard("🏠 Load", "🏠", themeManager.getComponentColor("load"), 170, 140);
  
  // Create status bar
  statusBar = lv_obj_create(container);
  lv_obj_set_size(statusBar, LV_PCT(100), 30);
  lv_obj_align(statusBar, LV_ALIGN_BOTTOM_MID, 0, 0);
  themeManager.applyCardStyle(statusBar, themeManager.getCurrentColors().surface);
  
  dataStatus = lv_label_create(statusBar);
  lv_label_set_text(dataStatus, "Data: OK");
  lv_obj_align(dataStatus, LV_ALIGN_LEFT_MID, 10, 0);
  themeManager.applyLabelStyle(dataStatus, true);
  
  lastUpdate = lv_label_create(statusBar);
  lv_label_set_text(lastUpdate, "Updated: Now");
  lv_obj_align(lastUpdate, LV_ALIGN_RIGHT_MID, -10, 0);
  themeManager.applyLabelStyle(lastUpdate, true);
  
  isCreated = true;
  createTime = millis();
  
  // Animate cards in
  animationManager.cardSlideUp(solarComponents.card, ANIM_DURATION_NORMAL, 0);
  animationManager.cardSlideUp(batteryComponents.card, ANIM_DURATION_NORMAL, 100);
  animationManager.cardSlideUp(gridComponents.card, ANIM_DURATION_NORMAL, 200);
  animationManager.cardSlideUp(loadComponents.card, ANIM_DURATION_NORMAL, 300);
}

MainScreen::CardComponents MainScreen::createCard(const char* title, const char* icon, lv_color_t color, lv_coord_t x, lv_coord_t y) {
  CardComponents comp;
  
  // Create card
  comp.card = lv_obj_create(container);
  lv_obj_set_size(comp.card, 140, 70);
  lv_obj_set_pos(comp.card, x, y);
  themeManager.applyCardStyle(comp.card, color);
  
  // Icon
  comp.icon = lv_label_create(comp.card);
  lv_label_set_text(comp.icon, icon);
  lv_obj_set_style_text_font(comp.icon, &lv_font_montserrat_20, 0);
  lv_obj_align(comp.icon, LV_ALIGN_TOP_LEFT, 8, 5);
  
  // Title
  comp.title = lv_label_create(comp.card);
  lv_label_set_text(comp.title, title);
  lv_obj_set_style_text_font(comp.title, &lv_font_montserrat_14, 0);
  lv_obj_align(comp.title, LV_ALIGN_TOP_LEFT, 35, 8);
  themeManager.applyLabelStyle(comp.title, false);
  
  // Value
  comp.value = lv_label_create(comp.card);
  lv_label_set_text(comp.value, "0.0");
  lv_obj_set_style_text_font(comp.value, &lv_font_montserrat_18, 0);
  lv_obj_align(comp.value, LV_ALIGN_CENTER, 0, 5);
  themeManager.applyLabelStyle(comp.value, false);
  
  // Unit
  comp.unit = lv_label_create(comp.card);
  lv_label_set_text(comp.unit, "W");
  lv_obj_align(comp.unit, LV_ALIGN_BOTTOM_RIGHT, -5, -5);
  themeManager.applyLabelStyle(comp.unit, true);
  
  // Status
  comp.status = lv_label_create(comp.card);
  lv_label_set_text(comp.status, "OK");
  lv_obj_align(comp.status, LV_ALIGN_BOTTOM_LEFT, 5, -5);
  lv_obj_set_style_text_color(comp.status, color, 0);
  
  return comp;
}

void MainScreen::update(const InverterData& data) {
  updateSolarCard(data);
  updateBatteryCard(data);
  updateGridCard(data);
  updateLoadCard(data);
}

void MainScreen::updateSolarCard(const InverterData& data) {
  updateCardValue(solarComponents, data.pvPower, "W", themeManager.getComponentColor("solar"));
  updateCardStatus(solarComponents, data.pvPower > 0 ? "Active" : "Idle", 
                  data.pvPower > 0 ? themeManager.getCurrentColors().success : themeManager.getCurrentColors().textSecondary);
}

void MainScreen::updateBatteryCard(const InverterData& data) {
  float level = data.getBatteryLevel();
  updateCardValue(batteryComponents, level, "%", data.getBatteryColor());
  updateCardStatus(batteryComponents, data.getBatteryStatus().c_str(), data.getBatteryColor());
}

void MainScreen::updateGridCard(const InverterData& data) {
  updateCardValue(gridComponents, data.gridPower, "W", themeManager.getComponentColor("grid"));
  updateCardStatus(gridComponents, data.gridVoltage > 200 ? "Online" : "Offline",
                  data.gridVoltage > 200 ? themeManager.getCurrentColors().success : themeManager.getCurrentColors().error);
}

void MainScreen::updateLoadCard(const InverterData& data) {
  updateCardValue(loadComponents, data.outputPower, "W", themeManager.getComponentColor("load"));
  updateCardStatus(loadComponents, String(data.loadPercent, 1) + "%",
                  data.loadPercent > 80 ? themeManager.getCurrentColors().warning : themeManager.getCurrentColors().success);
}

void MainScreen::updateCardValue(CardComponents& card, float value, const char* unit, lv_color_t color) {
  if (card.value) {
    String valueStr = String(value, 1);
    lv_label_set_text(card.value, valueStr.c_str());
    animationManager.pulse(card.value, 300, 105);
  }
  if (card.unit) {
    lv_label_set_text(card.unit, unit);
  }
}

void MainScreen::updateCardStatus(CardComponents& card, const char* status, lv_color_t color) {
  if (card.status) {
    lv_label_set_text(card.status, status);
    lv_obj_set_style_text_color(card.status, color, 0);
  }
}

void MainScreen::updateHeader(const String& time, bool wifiConnected, int signalStrength) {
  if (timeLabel && !time.isEmpty()) {
    lv_label_set_text(timeLabel, time.c_str());
  }
  
  if (wifiIcon) {
    lv_label_set_text(wifiIcon, wifiConnected ? "📶" : "📵");
    lv_obj_set_style_text_color(wifiIcon, 
                                wifiConnected ? themeManager.getCurrentColors().success : themeManager.getCurrentColors().error, 0);
  }
}

void MainScreen::updateStatusBar(DataStatus status, const String& lastUpdateTime) {
  if (dataStatus) {
    String statusText = "Data: ";
    lv_color_t statusColor;
    
    switch (status) {
      case DataStatus::VALID:
        statusText += "OK";
        statusColor = themeManager.getCurrentColors().success;
        break;
      case DataStatus::ERROR:
        statusText += "Error";
        statusColor = themeManager.getCurrentColors().error;
        break;
      case DataStatus::TIMEOUT:
        statusText += "Timeout";
        statusColor = themeManager.getCurrentColors().warning;
        break;
      default:
        statusText += "No Data";
        statusColor = themeManager.getCurrentColors().textSecondary;
        break;
    }
    
    lv_label_set_text(dataStatus, statusText.c_str());
    lv_obj_set_style_text_color(dataStatus, statusColor, 0);
  }
  
  if (lastUpdate) {
    String updateText = "Updated: " + lastUpdateTime;
    lv_label_set_text(lastUpdate, updateText.c_str());
  }
}

void MainScreen::handleEvent(lv_event_t* e) {
  // Handle main screen events
  if (e->code == LV_EVENT_GESTURE) {
    // Handle swipe to settings
    uiManager.showScreen(ScreenType::SETTINGS, AnimationType::SLIDE_DOWN);
  }
}

// Settings Screen Implementation
SettingsScreen::SettingsScreen() {
  settingsPanel = nullptr;
  closeButton = nullptr;
}

void SettingsScreen::create() {
  if (isCreated) return;
  
  screen = lv_obj_create(nullptr);
  themeManager.applyToObject(screen);
  
  createContainer();
  
  // Create header with close button
  lv_obj_t* header = createHeader("⚙️ Settings", false);
  
  // Close button
  closeButton = lv_btn_create(header);
  lv_obj_set_size(closeButton, 40, 30);
  lv_obj_align(closeButton, LV_ALIGN_RIGHT_MID, -10, 0);
  themeManager.applyButtonStyle(closeButton, true);
  
  lv_obj_t* closeLabel = lv_label_create(closeButton);
  lv_label_set_text(closeLabel, "✕");
  lv_obj_center(closeLabel);
  
  // Settings content
  settingsPanel = lv_obj_create(container);
  lv_obj_set_size(settingsPanel, LV_PCT(100), LV_PCT(80));
  lv_obj_align(settingsPanel, LV_ALIGN_BOTTOM_MID, 0, 0);
  lv_obj_set_style_bg_opa(settingsPanel, LV_OPA_TRANSP, 0);
  lv_obj_set_style_border_width(settingsPanel, 0, 0);
  
  // Theme selection
  lv_obj_t* themeBtn = lv_btn_create(settingsPanel);
  lv_obj_set_size(themeBtn, LV_PCT(90), 40);
  lv_obj_align(themeBtn, LV_ALIGN_TOP_MID, 0, 20);
  themeManager.applyButtonStyle(themeBtn, false);
  
  lv_obj_t* themeBtnLabel = lv_label_create(themeBtn);
  lv_label_set_text(themeBtnLabel, "🎨 Change Theme");
  lv_obj_center(themeBtnLabel);
  
  // WiFi button
  lv_obj_t* wifiBtn = lv_btn_create(settingsPanel);
  lv_obj_set_size(wifiBtn, LV_PCT(90), 40);
  lv_obj_align(wifiBtn, LV_ALIGN_TOP_MID, 0, 80);
  themeManager.applyButtonStyle(wifiBtn, false);
  
  lv_obj_t* wifiBtnLabel = lv_label_create(wifiBtn);
  lv_label_set_text(wifiBtnLabel, "📶 WiFi Settings");
  lv_obj_center(wifiBtnLabel);
  
  // About button
  lv_obj_t* aboutBtn = lv_btn_create(settingsPanel);
  lv_obj_set_size(aboutBtn, LV_PCT(90), 40);
  lv_obj_align(aboutBtn, LV_ALIGN_TOP_MID, 0, 140);
  themeManager.applyButtonStyle(aboutBtn, false);
  
  lv_obj_t* aboutBtnLabel = lv_label_create(aboutBtn);
  lv_label_set_text(aboutBtnLabel, "ℹ️ About");
  lv_obj_center(aboutBtnLabel);
  
  isCreated = true;
  createTime = millis();
}

void SettingsScreen::handleEvent(lv_event_t* e) {
  // Handle settings screen events
  if (e->code == LV_EVENT_CLICKED) {
    // Handle button clicks
  }
}

// Initialize all screens
void initializeScreens() {
  Serial.println("🖼️ Initializing all screens...");
  // Screens will be created on demand
  Serial.println("✅ Screen initialization complete");
}

void destroyScreens() {
  splashScreen.destroy();
  mainScreen.destroy();
  settingsScreen.destroy();
  wifiConfigScreen.destroy();
  aboutScreen.destroy();
  errorScreen.destroy();
}

BaseScreen* getScreen(ScreenType type) {
  switch (type) {
    case ScreenType::SPLASH: return &splashScreen;
    case ScreenType::MAIN: return &mainScreen;
    case ScreenType::SETTINGS: return &settingsScreen;
    case ScreenType::WIFI_CONFIG: return &wifiConfigScreen;
    case ScreenType::ABOUT: return &aboutScreen;
    case ScreenType::ERROR: return &errorScreen;
    default: return nullptr;
  }
}

// Placeholder implementations for other screens
void WiFiConfigScreen::create() { 
  screen = lv_obj_create(nullptr);
  isCreated = true;
}
void WiFiConfigScreen::handleEvent(lv_event_t* e) {}

void AboutScreen::create() { 
  screen = lv_obj_create(nullptr);
  isCreated = true;
}
void AboutScreen::handleEvent(lv_event_t* e) {}

void ErrorScreen::create() { 
  screen = lv_obj_create(nullptr);
  isCreated = true;
}
void ErrorScreen::handleEvent(lv_event_t* e) {}
