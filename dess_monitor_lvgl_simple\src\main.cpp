#include <Arduino.h>
#include <lvgl.h>
#include <TFT_eSPI.h>
#include <XPT2046_Touchscreen.h>
#include <WiFi.h>
#include <HTTPClient.h>
#include <ArduinoJson.h>
#include <WiFiManager.h>

// Hardware Configuration
#define SCREEN_WIDTH  320
#define SCREEN_HEIGHT 240

// Touch screen pins
#define XPT2046_IRQ  36
#define XPT2046_MOSI 32
#define XPT2046_MISO 39
#define XPT2046_CLK  25
#define XPT2046_CS   33

// Other pins
#define BOOT_BUTTON  0
#define LDR_PIN      34
#define TFT_BL       21

// Hardware instances
TFT_eSPI tft = TFT_eSPI();
SPIClass touchscreenSPI = SPIClass(VSPI);
XPT2046_Touchscreen ts(XPT2046_CS, XPT2046_IRQ);

// LVGL display buffer
static lv_disp_draw_buf_t draw_buf;
static lv_color_t buf[SCREEN_WIDTH * 10];

// Data structure
struct InverterData {
  float gridVoltage = 0;
  float gridFrequency = 0;
  float gridPower = 0;
  float pvVoltage = 0;
  float pvCurrent = 0;
  float pvPower = 0;
  float batteryVoltage = 0;
  float batteryCurrent = 0;
  float batteryCapacity = 0;
  float outputVoltage = 0;
  float outputCurrent = 0;
  float outputPower = 0;
  float outputFrequency = 0;
  float loadPercent = 0;
  int dcTemp = 0;
  int invTemp = 0;
  String operatingMode = "";
  String timestamp = "";
  bool dataValid = false;
  unsigned long lastUpdate = 0;
  int signalStrength = 0;
};

// Global variables
InverterData data;
lv_obj_t* mainScreen;
lv_obj_t* solarCard;
lv_obj_t* batteryCard;
lv_obj_t* gridCard;
lv_obj_t* loadCard;
lv_obj_t* statusLabel;
lv_obj_t* timeLabel;
lv_obj_t* wifiLabel;

// Theme colors
#define COLOR_PRIMARY   lv_color_hex(0x1E88E5)
#define COLOR_SUCCESS   lv_color_hex(0x4CAF50)
#define COLOR_WARNING   lv_color_hex(0xFFC107)
#define COLOR_ERROR     lv_color_hex(0xF44336)
#define COLOR_DARK      lv_color_hex(0x121212)
#define COLOR_CARD      lv_color_hex(0x1E1E1E)

// API Configuration
const char* API_BASE_URL = "https://web.dessmonitor.com/public/";
const char* DEVICE_CODE = "2376";
const char* DEVICE_SN = "Q0046526082082094801";

// Function declarations
void initializeHardware();
void initializeLVGL();
void createUI();
void updateUI();
bool fetchData();
void parseData(const String& response);
void displayFlushCallback(lv_disp_drv_t* disp, const lv_area_t* area, lv_color_t* color_p);
void touchpadReadCallback(lv_indev_drv_t* indev, lv_indev_data_t* data);
lv_obj_t* createCard(lv_obj_t* parent, const char* title, const char* icon, lv_color_t color, int x, int y);
void updateCard(lv_obj_t* card, float value, const char* unit, const char* status);

void setup() {
  Serial.begin(115200);
  delay(1000);

  Serial.println("\n=== DESS Monitor Pro LVGL Simple v1.0 ===");
  Serial.println("🚀 Starting system initialization...");

  // Initialize hardware
  initializeHardware();

  // Initialize LVGL
  initializeLVGL();

  // Create UI
  createUI();

  // Initialize WiFi
  WiFiManager wm;
  if (!wm.autoConnect("DESS_Monitor_Setup")) {
    Serial.println("❌ Failed to connect WiFi");
    ESP.restart();
  }

  Serial.println("✅ System initialization complete!");
}

void loop() {
  lv_timer_handler();

  static unsigned long lastDataUpdate = 0;
  static unsigned long lastUIUpdate = 0;

  // Fetch data every 10 seconds
  if (millis() - lastDataUpdate > 10000) {
    if (WiFi.status() == WL_CONNECTED) {
      if (fetchData()) {
        Serial.println("✅ Data updated successfully");
      } else {
        Serial.println("❌ Failed to fetch data");
      }
    }
    lastDataUpdate = millis();
  }

  // Update UI every 100ms
  if (millis() - lastUIUpdate > 100) {
    updateUI();
    lastUIUpdate = millis();
  }

  delay(5);
}

void initializeHardware() {
  Serial.println("🔧 Initializing hardware...");

  // Initialize TFT
  tft.init();
  tft.setRotation(1);
  tft.fillScreen(TFT_BLACK);

  // Initialize backlight
  pinMode(TFT_BL, OUTPUT);
  analogWrite(TFT_BL, 200); // 80% brightness

  // Initialize touchscreen
  touchscreenSPI.begin(XPT2046_CLK, XPT2046_MISO, XPT2046_MOSI, XPT2046_CS);
  ts.begin(touchscreenSPI);
  ts.setRotation(1);

  // Initialize sensors
  pinMode(LDR_PIN, INPUT);
  pinMode(BOOT_BUTTON, INPUT_PULLUP);

  Serial.println("✅ Hardware initialized");
}

void initializeLVGL() {
  Serial.println("🎨 Initializing LVGL...");

  lv_init();

  // Initialize display buffer
  lv_disp_draw_buf_init(&draw_buf, buf, NULL, SCREEN_WIDTH * 10);

  // Initialize display driver
  static lv_disp_drv_t disp_drv;
  lv_disp_drv_init(&disp_drv);
  disp_drv.hor_res = SCREEN_WIDTH;
  disp_drv.ver_res = SCREEN_HEIGHT;
  disp_drv.flush_cb = displayFlushCallback;
  disp_drv.draw_buf = &draw_buf;
  lv_disp_drv_register(&disp_drv);

  // Initialize input driver
  static lv_indev_drv_t indev_drv;
  lv_indev_drv_init(&indev_drv);
  indev_drv.type = LV_INDEV_TYPE_POINTER;
  indev_drv.read_cb = touchpadReadCallback;
  lv_indev_drv_register(&indev_drv);

  Serial.println("✅ LVGL initialized");
}

void createUI() {
  Serial.println("🖼️ Creating UI...");

  // Create main screen
  mainScreen = lv_obj_create(NULL);
  lv_obj_set_style_bg_color(mainScreen, COLOR_DARK, 0);
  lv_scr_load(mainScreen);

  // Create header
  lv_obj_t* header = lv_obj_create(mainScreen);
  lv_obj_set_size(header, SCREEN_WIDTH, 40);
  lv_obj_align(header, LV_ALIGN_TOP_MID, 0, 0);
  lv_obj_set_style_bg_color(header, COLOR_PRIMARY, 0);
  lv_obj_set_style_radius(header, 0, 0);

  // Title
  lv_obj_t* titleLabel = lv_label_create(header);
  lv_label_set_text(titleLabel, "⚡ DESS Monitor Pro");
  lv_obj_set_style_text_color(titleLabel, lv_color_white(), 0);
  lv_obj_align(titleLabel, LV_ALIGN_LEFT_MID, 10, 0);

  // Time
  timeLabel = lv_label_create(header);
  lv_label_set_text(timeLabel, "00:00");
  lv_obj_set_style_text_color(timeLabel, lv_color_white(), 0);
  lv_obj_align(timeLabel, LV_ALIGN_RIGHT_MID, -50, 0);

  // WiFi status
  wifiLabel = lv_label_create(header);
  lv_label_set_text(wifiLabel, "📶");
  lv_obj_set_style_text_color(wifiLabel, lv_color_white(), 0);
  lv_obj_align(wifiLabel, LV_ALIGN_RIGHT_MID, -10, 0);

  // Create data cards
  solarCard = createCard(mainScreen, "☀️ Solar", "☀️", COLOR_WARNING, 10, 50);
  batteryCard = createCard(mainScreen, "🔋 Battery", "🔋", COLOR_SUCCESS, 170, 50);
  gridCard = createCard(mainScreen, "⚡ Grid", "⚡", COLOR_PRIMARY, 10, 130);
  loadCard = createCard(mainScreen, "🏠 Load", "🏠", COLOR_ERROR, 170, 130);

  // Create status bar
  lv_obj_t* statusBar = lv_obj_create(mainScreen);
  lv_obj_set_size(statusBar, SCREEN_WIDTH, 30);
  lv_obj_align(statusBar, LV_ALIGN_BOTTOM_MID, 0, 0);
  lv_obj_set_style_bg_color(statusBar, COLOR_CARD, 0);
  lv_obj_set_style_radius(statusBar, 0, 0);

  statusLabel = lv_label_create(statusBar);
  lv_label_set_text(statusLabel, "Status: Initializing...");
  lv_obj_set_style_text_color(statusLabel, lv_color_white(), 0);
  lv_obj_align(statusLabel, LV_ALIGN_LEFT_MID, 10, 0);

  Serial.println("✅ UI created");
}

lv_obj_t* createCard(lv_obj_t* parent, const char* title, const char* icon, lv_color_t color, int x, int y) {
  // Create card container
  lv_obj_t* card = lv_obj_create(parent);
  lv_obj_set_size(card, 140, 70);
  lv_obj_set_pos(card, x, y);
  lv_obj_set_style_bg_color(card, COLOR_CARD, 0);
  lv_obj_set_style_border_color(card, color, 0);
  lv_obj_set_style_border_width(card, 2, 0);
  lv_obj_set_style_radius(card, 10, 0);

  // Icon
  lv_obj_t* iconLabel = lv_label_create(card);
  lv_label_set_text(iconLabel, icon);
  lv_obj_set_style_text_font(iconLabel, &lv_font_montserrat_18, 0);
  lv_obj_align(iconLabel, LV_ALIGN_TOP_LEFT, 8, 5);

  // Title
  lv_obj_t* titleLabel = lv_label_create(card);
  lv_label_set_text(titleLabel, title);
  lv_obj_set_style_text_color(titleLabel, lv_color_white(), 0);
  lv_obj_set_style_text_font(titleLabel, &lv_font_montserrat_14, 0);
  lv_obj_align(titleLabel, LV_ALIGN_TOP_LEFT, 35, 8);

  // Value
  lv_obj_t* valueLabel = lv_label_create(card);
  lv_label_set_text(valueLabel, "0.0");
  lv_obj_set_style_text_color(valueLabel, lv_color_white(), 0);
  lv_obj_set_style_text_font(valueLabel, &lv_font_montserrat_16, 0);
  lv_obj_align(valueLabel, LV_ALIGN_CENTER, 0, 5);

  // Unit
  lv_obj_t* unitLabel = lv_label_create(card);
  lv_label_set_text(unitLabel, "W");
  lv_obj_set_style_text_color(unitLabel, lv_color_hex(0x888888), 0);
  lv_obj_align(unitLabel, LV_ALIGN_BOTTOM_RIGHT, -5, -5);

  // Status
  lv_obj_t* statusLabel = lv_label_create(card);
  lv_label_set_text(statusLabel, "OK");
  lv_obj_set_style_text_color(statusLabel, color, 0);
  lv_obj_align(statusLabel, LV_ALIGN_BOTTOM_LEFT, 5, -5);

  return card;
}

void updateCard(lv_obj_t* card, float value, const char* unit, const char* status) {
  // Update value (3rd child)
  lv_obj_t* valueLabel = lv_obj_get_child(card, 2);
  if (valueLabel) {
    char valueStr[32];
    snprintf(valueStr, sizeof(valueStr), "%.1f", value);
    lv_label_set_text(valueLabel, valueStr);
  }

  // Update unit (4th child)
  lv_obj_t* unitLabel = lv_obj_get_child(card, 3);
  if (unitLabel) {
    lv_label_set_text(unitLabel, unit);
  }

  // Update status (5th child)
  lv_obj_t* statusLabel = lv_obj_get_child(card, 4);
  if (statusLabel) {
    lv_label_set_text(statusLabel, status);
  }
}

void updateUI() {
  // Update time
  struct tm timeinfo;
  if (getLocalTime(&timeinfo)) {
    char timeStr[16];
    snprintf(timeStr, sizeof(timeStr), "%02d:%02d", timeinfo.tm_hour, timeinfo.tm_min);
    lv_label_set_text(timeLabel, timeStr);
  }

  // Update WiFi status
  if (WiFi.status() == WL_CONNECTED) {
    lv_label_set_text(wifiLabel, "📶");
    lv_obj_set_style_text_color(wifiLabel, COLOR_SUCCESS, 0);
  } else {
    lv_label_set_text(wifiLabel, "📵");
    lv_obj_set_style_text_color(wifiLabel, COLOR_ERROR, 0);
  }

  // Update cards with data
  if (data.dataValid) {
    updateCard(solarCard, data.pvPower, "W", data.pvPower > 0 ? "Active" : "Idle");

    float battLevel = data.batteryCapacity > 0 ? data.batteryCapacity :
                     ((data.batteryVoltage - 48.0) / (58.4 - 48.0)) * 100;
    battLevel = constrain(battLevel, 0, 100);
    updateCard(batteryCard, battLevel, "%", battLevel > 60 ? "Good" : battLevel > 30 ? "Low" : "Critical");

    updateCard(gridCard, data.gridPower, "W", data.gridVoltage > 200 ? "Online" : "Offline");
    updateCard(loadCard, data.outputPower, "W", String(data.loadPercent, 1) + "%");

    lv_label_set_text(statusLabel, "Status: Data OK - Last update: Now");
  } else {
    lv_label_set_text(statusLabel, "Status: No Data - Check connection");
  }
}

bool fetchData() {
  if (WiFi.status() != WL_CONNECTED) return false;

  HTTPClient http;
  String url = String(API_BASE_URL) +
               "?sign=8743221c28ad40664baa48193bbf4b03caa726f1" +
               "&salt=" + String(millis()) +
               "&token=c8075906465cca180724b25d151680e31e2267ff1319ccf2b43c88ba979a7576" +
               "&action=querySPDeviceLastData" +
               "&source=1" +
               "&devcode=" + String(DEVICE_CODE) +
               "&pn=" + String(DEVICE_SN) +
               "&devaddr=1" +
               "&sn=" + String(DEVICE_SN) +
               "&i18n=en_US";

  http.begin(url);
  http.setTimeout(10000);

  int httpCode = http.GET();

  if (httpCode == 200) {
    String response = http.getString();
    parseData(response);
    http.end();
    return true;
  }

  http.end();
  return false;
}

void parseData(const String& response) {
  DynamicJsonDocument doc(8192);
  DeserializationError error = deserializeJson(doc, response);

  if (error) {
    Serial.printf("❌ JSON parsing failed: %s\n", error.c_str());
    return;
  }

  if (doc["err"] != 0) {
    Serial.printf("❌ API error: %s\n", doc["desc"].as<String>().c_str());
    return;
  }

  // Parse timestamp
  data.timestamp = doc["dat"]["gts"].as<String>();

  // Parse different parameter groups
  JsonObject pars = doc["dat"]["pars"];

  // Parse PV data
  if (pars["pv_"].is<JsonArray>()) {
    for (JsonObject param : pars["pv_"].as<JsonArray>()) {
      String parName = param["par"].as<String>();
      float value = param["val"].as<float>();

      if (parName == "PV Voltage") data.pvVoltage = value;
      else if (parName == "PV Current") data.pvCurrent = value;
      else if (parName == "PV Power") data.pvPower = value;
    }
  }

  // Parse Battery data
  if (pars["bt_"].is<JsonArray>()) {
    for (JsonObject param : pars["bt_"].as<JsonArray>()) {
      String parName = param["par"].as<String>();
      float value = param["val"].as<float>();

      if (parName == "Battery Voltage") data.batteryVoltage = value;
      else if (parName == "Battery Current") data.batteryCurrent = value;
      else if (parName == "bt_battery_capacity") data.batteryCapacity = value;
    }
  }

  // Parse Grid data
  if (pars["gd_"].is<JsonArray>()) {
    for (JsonObject param : pars["gd_"].as<JsonArray>()) {
      String parName = param["par"].as<String>();
      float value = param["val"].as<float>();

      if (parName == "Grid Voltage") data.gridVoltage = value;
      else if (parName == "Grid Frequency") data.gridFrequency = value;
      else if (parName == "Grid Power") data.gridPower = value;
    }
  }

  // Parse Output data
  if (pars["bc_"].is<JsonArray>()) {
    for (JsonObject param : pars["bc_"].as<JsonArray>()) {
      String parName = param["par"].as<String>();
      float value = param["val"].as<float>();

      if (parName == "Output Voltage") data.outputVoltage = value;
      else if (parName == "Output Current") data.outputCurrent = value;
      else if (parName == "Active Power") data.outputPower = value;
      else if (parName == "Output Frequency") data.outputFrequency = value;
      else if (parName == "Load Percent") data.loadPercent = value;
    }
  }

  data.dataValid = true;
  data.lastUpdate = millis();

  Serial.println("✅ Data parsed successfully");
}

void displayFlushCallback(lv_disp_drv_t* disp, const lv_area_t* area, lv_color_t* color_p) {
  uint32_t w = (area->x2 - area->x1 + 1);
  uint32_t h = (area->y2 - area->y1 + 1);

  tft.startWrite();
  tft.setAddrWindow(area->x1, area->y1, w, h);
  tft.pushColors((uint16_t*)&color_p->full, w * h, true);
  tft.endWrite();

  lv_disp_flush_ready(disp);
}

void touchpadReadCallback(lv_indev_drv_t* indev, lv_indev_data_t* data) {
  bool touched = ts.tirqTouched() && ts.touched();

  if (touched) {
    TS_Point p = ts.getPoint();
    data->state = LV_INDEV_STATE_PR;
    data->point.x = map(p.x, 200, 3700, 0, SCREEN_WIDTH);
    data->point.y = map(p.y, 200, 3700, 0, SCREEN_HEIGHT);
  } else {
    data->state = LV_INDEV_STATE_REL;
  }
}
