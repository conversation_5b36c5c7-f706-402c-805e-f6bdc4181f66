#include "network.h"
#include <WiFiManager.h>
#include <time.h>

// API Configuration
const char* API_BASE_URL = "https://web.dessmonitor.com/public/";
const char* DEVICE_CODE = "2376";
const char* DEVICE_SN = "Q0046526082082094801";

// Global instances
NetworkManager networkManager;
NetworkInfo networkInfo;

// Callback function pointers
WiFiConnectedCallback wifiConnectedCallback = nullptr;
WiFiDisconnectedCallback wifiDisconnectedCallback = nullptr;
DataReceivedCallback dataReceivedCallback = nullptr;
NetworkErrorCallback networkErrorCallback = nullptr;

NetworkManager::NetworkManager() {
  status = NetworkStatus::DISCONNECTED;
  lastConnectionAttempt = 0;
  lastDataFetch = 0;
  retryCount = 0;
  lastError = "";
}

NetworkManager::~NetworkManager() {
  disconnectWiFi();
}

bool NetworkManager::initializeWiFi() {
  Serial.println("📡 Initializing WiFi...");
  
  WiFi.mode(WIFI_STA);
  
  // Set WiFi event handlers
  WiFi.onEvent([this](WiFiEvent_t event, WiFiEventInfo_t info) {
    switch (event) {
      case ARDUINO_EVENT_WIFI_STA_CONNECTED:
        Serial.println("✅ WiFi connected!");
        this->onWiFiConnected();
        break;
        
      case ARDUINO_EVENT_WIFI_STA_DISCONNECTED:
        Serial.println("❌ WiFi disconnected!");
        this->onWiFiDisconnected();
        break;
        
      case ARDUINO_EVENT_WIFI_STA_GOT_IP:
        Serial.printf("🌐 Got IP address: %s\n", WiFi.localIP().toString().c_str());
        break;
        
      default:
        break;
    }
  });
  
  return true;
}

bool NetworkManager::connectWiFi() {
  Serial.println("🔗 Attempting WiFi connection...");
  
  status = NetworkStatus::CONNECTING;
  lastConnectionAttempt = millis();
  
  // Try to connect with saved credentials
  WiFi.begin();
  
  uint32_t startTime = millis();
  while (WiFi.status() != WL_CONNECTED && millis() - startTime < WIFI_TIMEOUT) {
    delay(500);
    Serial.print(".");
  }
  
  if (WiFi.status() == WL_CONNECTED) {
    status = NetworkStatus::CONNECTED;
    retryCount = 0;
    
    // Configure time
    configTime(7 * 3600, 0, "pool.ntp.org", "time.nist.gov");
    
    Serial.printf("\n✅ WiFi connected successfully!\n");
    Serial.printf("📶 SSID: %s\n", WiFi.SSID().c_str());
    Serial.printf("🌐 IP: %s\n", WiFi.localIP().toString().c_str());
    Serial.printf("📡 Signal: %d dBm\n", WiFi.RSSI());
    
    return true;
  } else {
    status = NetworkStatus::ERROR;
    retryCount++;
    lastError = "Connection timeout";
    
    Serial.println("\n❌ WiFi connection failed!");
    return false;
  }
}

void NetworkManager::disconnectWiFi() {
  Serial.println("🔌 Disconnecting WiFi...");
  WiFi.disconnect();
  status = NetworkStatus::DISCONNECTED;
}

bool NetworkManager::isConnected() {
  return WiFi.status() == WL_CONNECTED && status == NetworkStatus::CONNECTED;
}

void NetworkManager::resetWiFiSettings() {
  Serial.println("🔄 Resetting WiFi settings...");
  
  WiFiManager wm;
  wm.resetSettings();
  
  disconnectWiFi();
  
  Serial.println("✅ WiFi settings reset complete");
}

void NetworkManager::startConfigPortal() {
  Serial.println("⚙️ Starting WiFi configuration portal...");
  
  WiFiManager wm;
  
  // Set custom parameters
  wm.setConfigPortalTimeout(300); // 5 minutes timeout
  wm.setAPStaticIPConfig(IPAddress(192,168,4,1), IPAddress(192,168,4,1), IPAddress(255,255,255,0));
  
  // Start portal
  if (!wm.startConfigPortal("DESS_Monitor_Setup")) {
    Serial.println("❌ Failed to connect or hit timeout");
    status = NetworkStatus::ERROR;
    lastError = "Configuration portal failed";
  } else {
    Serial.println("✅ WiFi configured successfully");
    status = NetworkStatus::CONNECTED;
  }
}

void NetworkManager::checkConnection() {
  if (WiFi.status() != WL_CONNECTED) {
    if (status == NetworkStatus::CONNECTED) {
      status = NetworkStatus::DISCONNECTED;
      onWiFiDisconnected();
    }
  } else {
    if (status != NetworkStatus::CONNECTED) {
      status = NetworkStatus::CONNECTED;
      onWiFiConnected();
    }
  }
  
  // Update network info
  networkInfo.status = status;
  networkInfo.ssid = WiFi.SSID();
  networkInfo.ip = WiFi.localIP().toString();
  networkInfo.mac = WiFi.macAddress();
  networkInfo.signalStrength = getSignalStrength();
}

void NetworkManager::handleReconnection() {
  if (status == NetworkStatus::DISCONNECTED || status == NetworkStatus::ERROR) {
    uint32_t currentTime = millis();
    
    if (currentTime - lastConnectionAttempt >= RECONNECT_INTERVAL) {
      if (retryCount < MAX_RETRY_COUNT) {
        Serial.printf("🔄 Reconnection attempt %d/%d\n", retryCount + 1, MAX_RETRY_COUNT);
        connectWiFi();
      } else {
        Serial.println("❌ Max retry count reached, starting config portal");
        startConfigPortal();
        retryCount = 0;
      }
    }
  }
}

NetworkStatus NetworkManager::getStatus() {
  return status;
}

String NetworkManager::getStatusString() {
  switch (status) {
    case NetworkStatus::DISCONNECTED: return "Disconnected";
    case NetworkStatus::CONNECTING: return "Connecting";
    case NetworkStatus::CONNECTED: return "Connected";
    case NetworkStatus::ERROR: return "Error";
    case NetworkStatus::TIMEOUT: return "Timeout";
    default: return "Unknown";
  }
}

int NetworkManager::getSignalStrength() {
  if (!isConnected()) return 0;
  
  int rssi = WiFi.RSSI();
  
  // Convert RSSI to percentage (0-100)
  if (rssi >= -50) return 100;
  if (rssi <= -100) return 0;
  
  return 2 * (rssi + 100);
}

String NetworkManager::getSignalQuality() {
  int strength = getSignalStrength();
  
  if (strength >= 80) return "Excellent";
  if (strength >= 60) return "Good";
  if (strength >= 40) return "Fair";
  if (strength >= 20) return "Poor";
  return "Very Poor";
}

String NetworkManager::getSSID() {
  return WiFi.SSID();
}

String NetworkManager::getLocalIP() {
  return WiFi.localIP().toString();
}

String NetworkManager::getMACAddress() {
  return WiFi.macAddress();
}

bool NetworkManager::fetchInverterData(InverterData& data) {
  if (!isConnected()) {
    lastError = "No network connection";
    return false;
  }
  
  // Build API URL
  String url = String(API_BASE_URL) + 
               "?sign=8743221c28ad40664baa48193bbf4b03caa726f1" +
               "&salt=" + String(millis()) +
               "&token=c8075906465cca180724b25d151680e31e2267ff1319ccf2b43c88ba979a7576" +
               "&action=querySPDeviceLastData" +
               "&source=1" +
               "&devcode=" + String(DEVICE_CODE) +
               "&pn=" + String(DEVICE_SN) +
               "&devaddr=1" +
               "&sn=" + String(DEVICE_SN) +
               "&i18n=en_US";
  
  HTTPResponse response = makeHTTPRequest(url);
  
  if (response.success) {
    if (parseInverterResponse(response.body, data)) {
      data.lastUpdate = millis();
      data.dataValid = true;
      data.errorCount = 0;
      
      if (dataReceivedCallback) {
        dataReceivedCallback(data);
      }
      
      return true;
    } else {
      lastError = "Failed to parse response";
      data.errorCount++;
      return false;
    }
  } else {
    lastError = response.error;
    data.errorCount++;
    return false;
  }
}

bool NetworkManager::sendCommand(const String& command, const String& value) {
  // TODO: Implement command sending to inverter
  Serial.printf("📤 Sending command: %s = %s\n", command.c_str(), value.c_str());
  return true;
}

String NetworkManager::getLastError() {
  return lastError;
}

void NetworkManager::clearError() {
  lastError = "";
  retryCount = 0;
}

void NetworkManager::onWiFiConnected() {
  networkInfo.connectedTime = millis();
  networkInfo.errorCount = 0;
  
  if (wifiConnectedCallback) {
    wifiConnectedCallback();
  }
}

void NetworkManager::onWiFiDisconnected() {
  networkInfo.connectedTime = 0;
  
  if (wifiDisconnectedCallback) {
    wifiDisconnectedCallback();
  }
}

void NetworkManager::onDataReceived(const InverterData& data) {
  networkInfo.lastDataUpdate = millis();
  
  if (dataReceivedCallback) {
    dataReceivedCallback(data);
  }
}

void NetworkManager::onError(const String& error) {
  networkInfo.errorCount++;
  networkInfo.lastError = error;
  
  if (networkErrorCallback) {
    networkErrorCallback(error);
  }
}

// Global functions
bool initializeNetwork() {
  return networkManager.initializeWiFi() && networkManager.connectWiFi();
}

void updateNetworkStatus() {
  networkManager.checkConnection();
}

HTTPResponse makeHTTPRequest(const String& url, const String& method, const String& payload) {
  HTTPResponse response;
  HTTPClient http;
  
  Serial.printf("🌐 HTTP %s: %s\n", method.c_str(), url.c_str());
  
  http.begin(url);
  http.setTimeout(HTTP_TIMEOUT);
  http.addHeader("User-Agent", "DESS Monitor Pro/2.0");
  
  uint32_t startTime = millis();
  
  int httpCode;
  if (method == "POST") {
    http.addHeader("Content-Type", "application/json");
    httpCode = http.POST(payload);
  } else {
    httpCode = http.GET();
  }
  
  response.responseTime = millis() - startTime;
  response.statusCode = httpCode;
  
  if (httpCode > 0) {
    response.body = http.getString();
    response.success = (httpCode == 200);
    
    if (!response.success) {
      response.error = "HTTP " + String(httpCode);
    }
  } else {
    response.success = false;
    response.error = "Connection failed: " + String(httpCode);
  }
  
  http.end();
  
  Serial.printf("📊 HTTP Response: %d (%dms)\n", httpCode, response.responseTime);
  
  return response;
}

bool parseInverterResponse(const String& response, InverterData& data) {
  JsonDocument doc;
  DeserializationError error = deserializeJson(doc, response);
  
  if (error) {
    Serial.printf("❌ JSON parsing failed: %s\n", error.c_str());
    return false;
  }
  
  if (doc["err"] != 0) {
    Serial.printf("❌ API error: %s\n", doc["desc"].as<String>().c_str());
    return false;
  }
  
  // Parse timestamp
  data.timestamp = doc["dat"]["gts"].as<String>();
  
  // Parse different parameter groups
  JsonObject pars = doc["dat"]["pars"];
  
  // Parse PV data
  if (pars["pv_"].is<JsonArray>()) {
    for (JsonObject param : pars["pv_"].as<JsonArray>()) {
      String parName = param["par"].as<String>();
      float value = param["val"].as<float>();
      
      if (parName == "PV Voltage") data.pvVoltage = value;
      else if (parName == "PV Current") data.pvCurrent = value;
      else if (parName == "PV Power") data.pvPower = value;
      else if (parName == "PV Charging Current") data.pvChargingCurrent = value;
    }
  }
  
  // Parse Battery data
  if (pars["bt_"].is<JsonArray>()) {
    for (JsonObject param : pars["bt_"].as<JsonArray>()) {
      String parName = param["par"].as<String>();
      float value = param["val"].as<float>();
      
      if (parName == "Battery Voltage") data.batteryVoltage = value;
      else if (parName == "Battery Current") data.batteryCurrent = value;
      else if (parName == "bt_battery_capacity") data.batteryCapacity = value;
    }
  }
  
  // Parse Grid data
  if (pars["gd_"].is<JsonArray>()) {
    for (JsonObject param : pars["gd_"].as<JsonArray>()) {
      String parName = param["par"].as<String>();
      float value = param["val"].as<float>();
      
      if (parName == "Grid Voltage") data.gridVoltage = value;
      else if (parName == "Grid Frequency") data.gridFrequency = value;
      else if (parName == "Grid Power") data.gridPower = value;
    }
  }
  
  // Parse Output data
  if (pars["bc_"].is<JsonArray>()) {
    for (JsonObject param : pars["bc_"].as<JsonArray>()) {
      String parName = param["par"].as<String>();
      float value = param["val"].as<float>();
      
      if (parName == "Output Voltage") data.outputVoltage = value;
      else if (parName == "Output Current") data.outputCurrent = value;
      else if (parName == "Active Power") data.outputPower = value;
      else if (parName == "Apparent Power") data.outputApparentPower = value;
      else if (parName == "Output Frequency") data.outputFrequency = value;
      else if (parName == "Load Percent") data.loadPercent = value;
    }
  }
  
  // Parse System data
  if (pars["sy_"].is<JsonArray>()) {
    for (JsonObject param : pars["sy_"].as<JsonArray>()) {
      String parName = param["par"].as<String>();
      
      if (parName == "DC Module Termperature") data.dcTemp = param["val"].as<int>();
      else if (parName == "INV Module Termperature") data.invTemp = param["val"].as<int>();
      else if (parName == "Operating Mode") data.operatingMode = param["val"].as<String>();
      else if (parName == "Output Priority") data.outputPriority = param["val"].as<String>();
      else if (parName == "Charger Source Priority") data.chargerSourcePriority = param["val"].as<String>();
    }
  }
  
  Serial.println("✅ Data parsed successfully");
  return true;
}

// Callback setters
void setWiFiConnectedCallback(WiFiConnectedCallback callback) {
  wifiConnectedCallback = callback;
}

void setWiFiDisconnectedCallback(WiFiDisconnectedCallback callback) {
  wifiDisconnectedCallback = callback;
}

void setDataReceivedCallback(DataReceivedCallback callback) {
  dataReceivedCallback = callback;
}

void setNetworkErrorCallback(NetworkErrorCallback callback) {
  networkErrorCallback = callback;
}
