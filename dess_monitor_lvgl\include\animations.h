#ifndef ANIMATIONS_H
#define ANIMATIONS_H

#include <lvgl.h>
#include "data_types.h"

// Animation configuration
#define ANIM_DURATION_FAST   200
#define ANIM_DURATION_NORMAL 400
#define ANIM_DURATION_SLOW   800
#define ANIM_DURATION_VERY_SLOW 1200

// Animation easing types
enum class EasingType {
  LINEAR,
  EASE_IN,
  EASE_OUT,
  EASE_IN_OUT,
  BOUNCE_IN,
  BOUNCE_OUT,
  BOUNCE_IN_OUT,
  ELASTIC_IN,
  ELASTIC_OUT,
  ELASTIC_IN_OUT,
  BACK_IN,
  BACK_OUT,
  BACK_IN_OUT,
  OVERSHOOT,
  STEP
};

// Animation manager class
class AnimationManager {
private:
  struct ActiveAnimation {
    lv_anim_t anim;
    lv_obj_t* target;
    AnimationType type;
    uint32_t startTime;
    uint32_t duration;
    bool isActive;
    void* userData;
  };
  
  static const uint8_t MAX_ANIMATIONS = 16;
  ActiveAnimation animations[MAX_ANIMATIONS];
  uint8_t animationCount;
  bool globalAnimationsEnabled;
  
public:
  AnimationManager();
  ~AnimationManager();
  
  // Global animation control
  void setEnabled(bool enabled);
  bool isEnabled();
  void pauseAll();
  void resumeAll();
  void stopAll();
  
  // Basic animations
  void fadeIn(lv_obj_t* obj, uint32_t duration = ANIM_DURATION_NORMAL, uint32_t delay = 0);
  void fadeOut(lv_obj_t* obj, uint32_t duration = ANIM_DURATION_NORMAL, uint32_t delay = 0);
  void slideIn(lv_obj_t* obj, lv_dir_t direction, uint32_t duration = ANIM_DURATION_NORMAL, uint32_t delay = 0);
  void slideOut(lv_obj_t* obj, lv_dir_t direction, uint32_t duration = ANIM_DURATION_NORMAL, uint32_t delay = 0);
  void scaleIn(lv_obj_t* obj, uint32_t duration = ANIM_DURATION_NORMAL, uint32_t delay = 0);
  void scaleOut(lv_obj_t* obj, uint32_t duration = ANIM_DURATION_NORMAL, uint32_t delay = 0);
  
  // Advanced animations
  void bounce(lv_obj_t* obj, uint32_t duration = ANIM_DURATION_SLOW);
  void shake(lv_obj_t* obj, uint32_t duration = ANIM_DURATION_FAST, int16_t amplitude = 10);
  void pulse(lv_obj_t* obj, uint32_t duration = ANIM_DURATION_SLOW, uint8_t scale = 110);
  void rotate(lv_obj_t* obj, int16_t angle, uint32_t duration = ANIM_DURATION_NORMAL);
  void flip(lv_obj_t* obj, bool horizontal = true, uint32_t duration = ANIM_DURATION_NORMAL);
  
  // Value animations
  void animateValue(lv_obj_t* obj, int32_t startValue, int32_t endValue, uint32_t duration, lv_anim_exec_xcb_t execCb);
  void animateColor(lv_obj_t* obj, lv_color_t startColor, lv_color_t endColor, uint32_t duration);
  void animateOpacity(lv_obj_t* obj, lv_opa_t startOpa, lv_opa_t endOpa, uint32_t duration);
  
  // Screen transitions
  void screenTransition(lv_obj_t* screenOut, lv_obj_t* screenIn, AnimationType type, uint32_t duration = ANIM_DURATION_NORMAL);
  void slideScreenLeft(lv_obj_t* screenOut, lv_obj_t* screenIn, uint32_t duration = ANIM_DURATION_NORMAL);
  void slideScreenRight(lv_obj_t* screenOut, lv_obj_t* screenIn, uint32_t duration = ANIM_DURATION_NORMAL);
  void slideScreenUp(lv_obj_t* screenOut, lv_obj_t* screenIn, uint32_t duration = ANIM_DURATION_NORMAL);
  void slideScreenDown(lv_obj_t* screenOut, lv_obj_t* screenIn, uint32_t duration = ANIM_DURATION_NORMAL);
  
  // Card animations
  void cardFlipIn(lv_obj_t* card, uint32_t duration = ANIM_DURATION_NORMAL, uint32_t delay = 0);
  void cardSlideUp(lv_obj_t* card, uint32_t duration = ANIM_DURATION_NORMAL, uint32_t delay = 0);
  void cardBounceIn(lv_obj_t* card, uint32_t duration = ANIM_DURATION_SLOW, uint32_t delay = 0);
  void cardHighlight(lv_obj_t* card, lv_color_t color, uint32_t duration = ANIM_DURATION_FAST);
  
  // Data update animations
  void animateValueChange(lv_obj_t* label, const String& newValue, uint32_t duration = ANIM_DURATION_FAST);
  void animateProgressBar(lv_obj_t* bar, int32_t newValue, uint32_t duration = ANIM_DURATION_NORMAL);
  void animateArc(lv_obj_t* arc, int32_t newValue, uint32_t duration = ANIM_DURATION_NORMAL);
  
  // Loading animations
  void startLoadingSpinner(lv_obj_t* spinner);
  void stopLoadingSpinner(lv_obj_t* spinner);
  void pulseLoading(lv_obj_t* obj);
  void dotsLoading(lv_obj_t* container);
  
  // Notification animations
  void notificationSlideIn(lv_obj_t* notification, lv_dir_t direction = LV_DIR_TOP);
  void notificationSlideOut(lv_obj_t* notification, lv_dir_t direction = LV_DIR_TOP);
  void notificationBounce(lv_obj_t* notification);
  
  // Gesture animations
  void swipeResponse(lv_obj_t* obj, lv_dir_t direction, uint32_t duration = ANIM_DURATION_FAST);
  void tapResponse(lv_obj_t* obj, uint32_t duration = ANIM_DURATION_FAST);
  void longPressResponse(lv_obj_t* obj, uint32_t duration = ANIM_DURATION_NORMAL);
  
  // Easing functions
  void setEasing(lv_anim_t* anim, EasingType easing);
  lv_anim_path_cb_t getEasingFunction(EasingType easing);
  
  // Animation management
  bool isAnimating(lv_obj_t* obj);
  void stopAnimation(lv_obj_t* obj);
  void stopAnimationType(lv_obj_t* obj, AnimationType type);
  uint8_t getActiveAnimationCount();
  
  // Callbacks
  void setAnimationReadyCallback(lv_anim_ready_cb_t callback);
  void setAnimationDeletedCallback(lv_anim_deleted_cb_t callback);
  
private:
  // Internal animation helpers
  void createAnimation(lv_obj_t* obj, AnimationType type, uint32_t duration);
  void registerAnimation(const lv_anim_t& anim, lv_obj_t* target, AnimationType type);
  void unregisterAnimation(lv_obj_t* target, AnimationType type);
  
  // Animation callbacks
  static void animationReadyCallback(lv_anim_t* anim);
  static void animationDeletedCallback(lv_anim_t* anim);
  static void animationStartCallback(lv_anim_t* anim);
  
  // Execution callbacks for different animation types
  static void opacityExecCallback(void* var, int32_t val);
  static void xExecCallback(void* var, int32_t val);
  static void yExecCallback(void* var, int32_t val);
  static void widthExecCallback(void* var, int32_t val);
  static void heightExecCallback(void* var, int32_t val);
  static void scaleExecCallback(void* var, int32_t val);
  static void rotateExecCallback(void* var, int32_t val);
  static void colorExecCallback(void* var, int32_t val);
};

// Global animation manager instance
extern AnimationManager animationManager;

// Animation utility functions
void initializeAnimations();
lv_anim_t createBaseAnimation(lv_obj_t* obj, uint32_t duration, uint32_t delay = 0);
void chainAnimations(lv_anim_t* anim1, lv_anim_t* anim2);
void parallelAnimations(lv_anim_t* anims[], uint8_t count);

// Predefined animation sequences
void welcomeSequence(lv_obj_t* container);
void dataUpdateSequence(lv_obj_t* cards[], uint8_t count);
void errorSequence(lv_obj_t* errorContainer);
void successSequence(lv_obj_t* successContainer);

// Custom easing functions
int32_t easingBounceOut(int32_t t);
int32_t easingElasticOut(int32_t t);
int32_t easingBackOut(int32_t t);
int32_t easingOvershoot(int32_t t);

#endif // ANIMATIONS_H
