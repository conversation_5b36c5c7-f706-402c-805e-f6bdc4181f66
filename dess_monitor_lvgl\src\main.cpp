#include <Arduino.h>
#include <lvgl.h>
#include "hardware.h"
#include "network.h"
#include "ui_manager.h"
#include "themes.h"
#include "screens.h"
#include "animations.h"
#include "data_types.h"

// Global variables
InverterData inverterData;
AppSettings appSettings;
uint32_t lastDataUpdate = 0;
uint32_t lastUIUpdate = 0;
uint32_t lastNetworkCheck = 0;

// Task handles for FreeRTOS
TaskHandle_t dataTaskHandle = NULL;
TaskHandle_t uiTaskHandle = NULL;
TaskHandle_t networkTaskHandle = NULL;

// Function declarations
void setupSystem();
void setupTasks();
void dataTask(void* parameter);
void uiTask(void* parameter);
void networkTask(void* parameter);
void handleSystemEvents();
void updateSystemStatus();

void setup() {
  // Initialize serial communication
  Serial.begin(115200);
  delay(1000);
  
  Serial.println("\n=== DESS Monitor Pro LVGL v2.0 ===");
  Serial.println("🚀 Starting system initialization...");
  
  // Initialize hardware
  Serial.println("🔧 Initializing hardware...");
  initializeHardware();
  
  // Initialize LVGL
  Serial.println("🎨 Initializing LVGL...");
  lv_init();
  
  // Setup display driver
  static lv_disp_draw_buf_t draw_buf;
  static lv_color_t buf[TFT_WIDTH * 10];
  lv_disp_draw_buf_init(&draw_buf, buf, NULL, TFT_WIDTH * 10);
  
  static lv_disp_drv_t disp_drv;
  lv_disp_drv_init(&disp_drv);
  disp_drv.hor_res = TFT_WIDTH;
  disp_drv.ver_res = TFT_HEIGHT;
  disp_drv.flush_cb = [](lv_disp_drv_t* disp_drv, const lv_area_t* area, lv_color_t* color_p) {
    uint32_t w = (area->x2 - area->x1 + 1);
    uint32_t h = (area->y2 - area->y1 + 1);
    
    tft.startWrite();
    tft.setAddrWindow(area->x1, area->y1, w, h);
    tft.pushColors((uint16_t*)&color_p->full, w * h, true);
    tft.endWrite();
    
    lv_disp_flush_ready(disp_drv);
  };
  disp_drv.draw_buf = &draw_buf;
  lv_disp_drv_register(&disp_drv);
  
  // Setup input driver
  static lv_indev_drv_t indev_drv;
  lv_indev_drv_init(&indev_drv);
  indev_drv.type = LV_INDEV_TYPE_POINTER;
  indev_drv.read_cb = [](lv_indev_drv_t* indev_drv, lv_indev_data_t* data) {
    int16_t x, y;
    bool pressed = getTouchPoint(x, y);
    
    if (pressed) {
      data->state = LV_INDEV_STATE_PR;
      data->point.x = x;
      data->point.y = y;
    } else {
      data->state = LV_INDEV_STATE_REL;
    }
  };
  lv_indev_drv_register(&indev_drv);
  
  // Initialize managers
  Serial.println("🎭 Initializing theme manager...");
  themeManager.initialize();
  
  Serial.println("🎬 Initializing animation manager...");
  initializeAnimations();
  
  Serial.println("🖥️  Initializing UI manager...");
  uiManager.initialize();
  
  Serial.println("📡 Initializing network...");
  initializeNetwork();
  
  Serial.println("🖼️  Initializing screens...");
  initializeScreens();
  
  // Show splash screen
  Serial.println("✨ Showing splash screen...");
  uiManager.showScreen(ScreenType::SPLASH);
  splashScreen.setStatus("Initializing system...");
  
  // Setup system
  setupSystem();
  
  // Setup FreeRTOS tasks
  setupTasks();
  
  Serial.println("✅ System initialization complete!");
  Serial.println("🎯 Starting main application...");
  
  // Transition to main screen after splash
  delay(2000);
  splashScreen.setStatus("Loading interface...");
  delay(1000);
  
  uiManager.showScreen(ScreenType::MAIN, AnimationType::SLIDE_UP);
}

void loop() {
  // Handle LVGL tasks
  lv_timer_handler();
  
  // Handle system events
  handleSystemEvents();
  
  // Update system status
  updateSystemStatus();
  
  // Small delay to prevent watchdog issues
  delay(5);
}

void setupSystem() {
  // Load settings from preferences
  Serial.println("📋 Loading application settings...");
  // TODO: Implement settings loading
  
  // Apply initial theme
  themeManager.setTheme(appSettings.currentTheme);
  
  // Set initial brightness
  setBacklight(appSettings.brightness);
  
  // Enable animations if configured
  animationManager.setEnabled(appSettings.animationsEnabled);
  
  Serial.println("⚙️  System configuration complete");
}

void setupTasks() {
  Serial.println("🔄 Creating FreeRTOS tasks...");
  
  // Create data fetching task
  xTaskCreatePinnedToCore(
    dataTask,           // Task function
    "DataTask",         // Task name
    4096,              // Stack size
    NULL,              // Parameters
    2,                 // Priority
    &dataTaskHandle,   // Task handle
    0                  // Core (0 or 1)
  );
  
  // Create UI update task
  xTaskCreatePinnedToCore(
    uiTask,            // Task function
    "UITask",          // Task name
    8192,              // Stack size
    NULL,              // Parameters
    3,                 // Priority (higher than data task)
    &uiTaskHandle,     // Task handle
    1                  // Core (0 or 1)
  );
  
  // Create network monitoring task
  xTaskCreatePinnedToCore(
    networkTask,       // Task function
    "NetworkTask",     // Task name
    4096,              // Stack size
    NULL,              // Parameters
    1,                 // Priority (lower than others)
    &networkTaskHandle, // Task handle
    0                  // Core (0 or 1)
  );
  
  Serial.println("✅ FreeRTOS tasks created successfully");
}

void dataTask(void* parameter) {
  Serial.println("📊 Data task started");
  
  while (true) {
    uint32_t currentTime = millis();
    
    // Fetch data at configured interval
    if (currentTime - lastDataUpdate >= appSettings.refreshInterval) {
      if (networkManager.isConnected()) {
        Serial.println("📡 Fetching inverter data...");
        
        if (networkManager.fetchInverterData(inverterData)) {
          Serial.println("✅ Data fetched successfully");
          lastDataUpdate = currentTime;
          
          // Notify UI of new data
          uiManager.updateData(inverterData);
        } else {
          Serial.println("❌ Failed to fetch data");
          inverterData.errorCount++;
          
          if (inverterData.errorCount > 5) {
            uiManager.showError("Data fetch failed repeatedly");
          }
        }
      } else {
        Serial.println("⚠️  No network connection - skipping data fetch");
      }
    }
    
    // Task delay
    vTaskDelay(pdMS_TO_TICKS(1000)); // 1 second
  }
}

void uiTask(void* parameter) {
  Serial.println("🎨 UI task started");
  
  while (true) {
    uint32_t currentTime = millis();
    
    // Update UI at high frequency for smooth animations
    if (currentTime - lastUIUpdate >= 16) { // ~60 FPS
      uiManager.update();
      lastUIUpdate = currentTime;
    }
    
    // Handle touch input
    if (isTouchPressed()) {
      int16_t x, y;
      if (getTouchPoint(x, y)) {
        uiManager.handleTouch(x, y);
      }
    }
    
    // Handle hardware buttons
    if (isBootButtonPressed()) {
      uiManager.handleButton(0); // Boot button = button 0
    }
    
    // Task delay
    vTaskDelay(pdMS_TO_TICKS(5)); // 5ms for smooth UI
  }
}

void networkTask(void* parameter) {
  Serial.println("📡 Network task started");
  
  while (true) {
    uint32_t currentTime = millis();
    
    // Check network status periodically
    if (currentTime - lastNetworkCheck >= 5000) { // Every 5 seconds
      networkManager.checkConnection();
      
      // Update UI with network status
      NetworkInfo netInfo = networkInfo; // Get current network info
      uiManager.updateNetworkStatus(netInfo);
      
      lastNetworkCheck = currentTime;
    }
    
    // Handle reconnection if needed
    if (!networkManager.isConnected()) {
      networkManager.handleReconnection();
    }
    
    // Task delay
    vTaskDelay(pdMS_TO_TICKS(1000)); // 1 second
  }
}

void handleSystemEvents() {
  // Handle hardware button events
  static bool lastBootButtonState = false;
  static uint32_t bootButtonPressTime = 0;
  
  bool currentBootButtonState = isBootButtonPressed();
  
  if (currentBootButtonState && !lastBootButtonState) {
    // Button pressed
    bootButtonPressTime = millis();
  } else if (!currentBootButtonState && lastBootButtonState) {
    // Button released
    uint32_t pressDuration = millis() - bootButtonPressTime;
    
    if (pressDuration > 5000) {
      // Long press - reset WiFi
      Serial.println("🔄 Long press detected - resetting WiFi...");
      networkManager.resetWiFiSettings();
      uiManager.showScreen(ScreenType::WIFI_CONFIG);
    } else if (pressDuration > 100) {
      // Short press - toggle theme
      Serial.println("🎨 Short press detected - toggling theme...");
      themeManager.nextTheme();
    }
  }
  
  lastBootButtonState = currentBootButtonState;
  
  // Handle auto theme switching based on light sensor
  if (appSettings.autoTheme) {
    themeManager.updateAutoTheme();
  }
  
  // Handle auto brightness
  if (appSettings.autoBrightness) {
    int ldrValue = readLDR();
    uint8_t brightness = map(ldrValue, 0, 4095, 20, 100);
    brightness = constrain(brightness, 20, 100);
    
    if (abs(brightness - appSettings.brightness) > 5) {
      appSettings.brightness = brightness;
      setBacklight(brightness);
    }
  }
}

void updateSystemStatus() {
  static uint32_t lastStatusUpdate = 0;
  uint32_t currentTime = millis();
  
  // Update system status every 10 seconds
  if (currentTime - lastStatusUpdate >= 10000) {
    // Update time display
    String timeString = String(hour()) + ":" + 
                       (minute() < 10 ? "0" : "") + String(minute());
    uiManager.updateTime(timeString);
    
    // Update data status
    DataStatus dataStatus = DataStatus::NO_DATA;
    if (inverterData.dataValid) {
      if (currentTime - inverterData.lastUpdate < 30000) {
        dataStatus = DataStatus::VALID;
      } else {
        dataStatus = DataStatus::TIMEOUT;
      }
    } else if (inverterData.errorCount > 0) {
      dataStatus = DataStatus::ERROR;
    }
    
    uiManager.setDataStatus(dataStatus);
    
    // Update WiFi status
    uiManager.setWiFiStatus(networkManager.isConnected(), networkManager.getSignalStrength());
    
    // Update battery level for UI
    uiManager.setBatteryLevel(inverterData.getBatteryLevel());
    
    lastStatusUpdate = currentTime;
    
    // Print system status to serial
    Serial.printf("📊 System Status - WiFi: %s, Data: %s, Free Heap: %d, Uptime: %d min\n",
                  networkManager.isConnected() ? "Connected" : "Disconnected",
                  inverterData.dataValid ? "Valid" : "Invalid",
                  ESP.getFreeHeap(),
                  currentTime / 60000);
  }
}
