#ifndef UI_MANAGER_H
#define UI_MANAGER_H

#include <lvgl.h>
#include "data_types.h"
#include "themes.h"

// Screen enumeration
enum class ScreenType {
  SPLASH,
  MAIN,
  SETTINGS,
  WIFI_CONFIG,
  ABOUT,
  ERROR
};

// Animation types
enum class AnimationType {
  NONE,
  FADE_IN,
  FADE_OUT,
  SLIDE_LEFT,
  SLIDE_RIGHT,
  SLIDE_UP,
  SLIDE_DOWN,
  SCALE_IN,
  SCALE_OUT,
  BOUNCE,
  ELASTIC
};

// UI Manager class
class UIManager {
private:
  ScreenType currentScreen;
  ScreenType previousScreen;
  ThemeManager* themeManager;
  bool animationsEnabled;
  uint32_t lastUpdate;
  
  // Screen objects
  lv_obj_t* screens[6]; // One for each ScreenType
  
  // Animation state
  bool isAnimating;
  uint32_t animationStartTime;
  uint32_t animationDuration;
  
public:
  UIManager();
  ~UIManager();
  
  // Initialization
  bool initialize();
  void setupScreens();
  
  // Screen management
  void showScreen(ScreenType screen, AnimationType animation = AnimationType::FADE_IN);
  void hideScreen(ScreenType screen, AnimationType animation = AnimationType::FADE_OUT);
  ScreenType getCurrentScreen();
  ScreenType getPreviousScreen();
  
  // Animation control
  void setAnimationsEnabled(bool enabled);
  bool areAnimationsEnabled();
  void setAnimationDuration(uint32_t duration);
  
  // Theme management
  void setTheme(ThemeType theme);
  ThemeType getCurrentTheme();
  void toggleTheme();
  
  // Data updates
  void updateData(const InverterData& data);
  void updateNetworkStatus(const NetworkInfo& networkInfo);
  void updateTime(const String& timeString);
  
  // Event handling
  void handleTouch(int16_t x, int16_t y);
  void handleGesture(const TouchGesture& gesture);
  void handleButton(uint8_t button);
  
  // Notifications
  void showNotification(const String& message, lv_color_t color = lv_color_white(), uint32_t duration = 3000);
  void showError(const String& error);
  void showSuccess(const String& message);
  void showWarning(const String& warning);
  
  // Status updates
  void setWiFiStatus(bool connected, int signalStrength = 0);
  void setDataStatus(DataStatus status);
  void setBatteryLevel(float level);
  
  // Screen savers
  void enableScreenSaver(uint32_t timeout = 300000); // 5 minutes
  void disableScreenSaver();
  void resetScreenSaver();
  
  // Update loop
  void update();
  void forceUpdate();
  
private:
  // Internal screen creation
  void createSplashScreen();
  void createMainScreen();
  void createSettingsScreen();
  void createWiFiConfigScreen();
  void createAboutScreen();
  void createErrorScreen();
  
  // Animation helpers
  void startAnimation(lv_obj_t* obj, AnimationType type, uint32_t duration);
  void stopAnimation(lv_obj_t* obj);
  bool isAnimationRunning();
  
  // Update helpers
  void updateMainScreenData(const InverterData& data);
  void updateHeaderInfo();
  void updateCards();
  
  // Event callbacks
  static void screenEventCallback(lv_event_t* e);
  static void buttonEventCallback(lv_event_t* e);
  static void animationCallback(void* var, int32_t val);
  static void animationReadyCallback(lv_anim_t* anim);
};

// Global UI manager instance
extern UIManager uiManager;

// UI utility functions
lv_obj_t* createCard(lv_obj_t* parent, const char* title, lv_color_t color);
lv_obj_t* createButton(lv_obj_t* parent, const char* text, lv_event_cb_t callback);
lv_obj_t* createLabel(lv_obj_t* parent, const char* text, const lv_font_t* font = &lv_font_montserrat_16);
lv_obj_t* createIcon(lv_obj_t* parent, const char* symbol, lv_color_t color);
lv_obj_t* createProgressBar(lv_obj_t* parent, int32_t min, int32_t max, int32_t value);
lv_obj_t* createChart(lv_obj_t* parent, uint16_t pointCount);

// Animation utilities
void animateObject(lv_obj_t* obj, lv_anim_exec_xcb_t exec_cb, int32_t start, int32_t end, uint32_t duration);
void fadeIn(lv_obj_t* obj, uint32_t duration = 300);
void fadeOut(lv_obj_t* obj, uint32_t duration = 300);
void slideIn(lv_obj_t* obj, lv_dir_t direction, uint32_t duration = 400);
void slideOut(lv_obj_t* obj, lv_dir_t direction, uint32_t duration = 400);
void scaleIn(lv_obj_t* obj, uint32_t duration = 300);
void scaleOut(lv_obj_t* obj, uint32_t duration = 300);

// Color utilities
lv_color_t getStatusColor(float value, float min, float max);
lv_color_t getBatteryColor(float level);
lv_color_t getTemperatureColor(int temperature);
lv_color_t getSignalColor(int strength);

// Text formatting utilities
String formatPower(float power);
String formatVoltage(float voltage);
String formatCurrent(float current);
String formatFrequency(float frequency);
String formatTemperature(int temperature);
String formatTime(uint32_t timestamp);
String formatDuration(uint32_t duration);

#endif // UI_MANAGER_H
