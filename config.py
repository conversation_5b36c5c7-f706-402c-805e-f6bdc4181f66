import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    # DESS Monitor credentials
    DESS_USERNAME = os.getenv('DESS_USERNAME', '')
    DESS_PASSWORD = os.getenv('DESS_PASSWORD', '')
    
    # URLs
    DESS_LOGIN_URL = 'https://www.dessmonitor.com/'
    DESS_WEB_URL = 'https://web.dessmonitor.com/'
    
    # Device information (จากลิงก์ที่คุณให้มา)
    DEVICE_CODE = '2376'
    DEVICE_PN = 'Q0046526082082'
    DEVICE_ADDR = '1'
    DEVICE_SN = 'Q0046526082082094801'
    
    # Chrome options
    HEADLESS_MODE = True  # เปลี่ยนเป็น False หากต้องการเห็น browser
    
    # Data collection settings
    COLLECTION_INTERVAL = 300  # วินาที (5 นาที)
    DATA_SAVE_PATH = 'inverter_data'
    
    # Logging
    LOG_LEVEL = 'INFO'
    LOG_FILE = 'inverter_monitor.log'
