# 🎯 ESP32-2432S028R TFT Display Calibration Guide

## 🔧 **ปัญหาหน้าจอเพี้ยน - แก้ไขแล้ว!**

### ✅ **การแก้ไขที่ทำ:**

#### **1. ติดตั้ง TFT_eSPI จาก GitHub Master**
```ini
lib_deps =
    https://github.com/Bodmer/TFT_eSPI.git
```
**เหตุผล**: เวอร์ชันล่าสุดจาก GitHub มีการแก้ไข bugs และรองรับ ESP32-2432S028R ดีกว่า

#### **2. ปรับการตั้งค่า Display Configuration**
```ini
build_flags =
    -DUSER_SETUP_LOADED=1
    -DILI9341_DRIVER=1
    -DTFT_WIDTH=240
    -DTFT_HEIGHT=320
    -DTFT_MISO=12
    -DTFT_MOSI=13
    -DTFT_SCLK=14
    -DTFT_CS=15
    -DTFT_DC=2
    -DTFT_RST=-1              # เปลี่ยนกลับเป็น -1
    -DTFT_BL=21
    -DSPI_FREQUENCY=40000000  # เพิ่มความเร็ว SPI
    -DTFT_INVERSION_OFF=1     # ปิด inversion
    -DTFT_RGB_ORDER=TFT_BGR   # ใช้ BGR order
```

#### **3. สร้างไฟล์ทดสอบ Calibration**
- **`tft_calibration_test.cpp`** - ทดสอบการแสดงผลแบบครบถ้วน

## 🧪 **ไฟล์ทดสอบ Calibration**

### **การทำงานของ tft_calibration_test.cpp:**

#### **Phase 1: Rotation Test (0-3)**
- ทดสอบ rotation ทั้ง 4 แบบ
- แสดงสีพื้นฐาน: แดง, เขียว, น้ำเงิน, ขาว
- แสดง color bars แต่ละ rotation
- ทดสอบการแสดงข้อความ

#### **Phase 2: Color Inversion Test**
- ทดสอบ `invertDisplay(false)` และ `invertDisplay(true)`
- เปรียบเทียบความแตกต่าง
- เลือกโหมดที่เหมาะสม

#### **Phase 3: RGB Order Test**
- แสดงแถบสี R-G-B แยกกัน
- ตรวจสอบลำดับสีที่ถูกต้อง
- ยืนยันการตั้งค่า TFT_RGB_ORDER

#### **Phase 4: Final Calibration**
- แสดงหน้าจอ calibration สุดท้าย
- มี corner markers สี่มุม
- มี center cross
- แสดงข้อมูล resolution

## 📊 **ผลลัพธ์ที่คาดหวัง**

### **หน้าจอ Calibration สุดท้าย:**
```
┌─────────────────────────────────────┐
│●                                  ● │ ← Red/Green corners
│                                     │
│           CALIBRATION               │
│         Display Calibrated          │
│             320x240                 │
│                 +                   │ ← Center cross
│                                     │
│●                                  ● │ ← Blue/Yellow corners
│Check corners and center alignment   │
│If OK, display is properly config    │
└─────────────────────────────────────┘
```

### **Loop Phase:**
- Corner markers กะพริบระหว่างสีเดิมกับสีขาว
- ทุก 2 วินาที
- แสดงว่าระบบทำงานต่อเนื่อง

## 🎯 **วิธีใช้งาน**

### **1. Upload Calibration Test:**
```bash
# Build และ upload
python -m platformio run --target upload

# Monitor ผลลัพธ์ (ถ้าต้องการ)
python -m platformio device monitor
```

### **2. สังเกตการทำงาน:**

#### **ขั้นตอนที่ 1-4: Rotation Test**
- หน้าจอจะหมุนและแสดงสีต่างๆ
- ดูว่า rotation ไหนที่ถูกต้อง (มักจะเป็น 1 สำหรับ landscape)

#### **ขั้นตอนที่ 5: Inversion Test**
- ดูความแตกต่างระหว่าง inversion on/off
- เลือกโหมดที่ดูธรรมชาติที่สุด

#### **ขั้นตอนที่ 6: RGB Test**
- ตรวจสอบว่าสีแดง-เขียว-น้ำเงินแสดงถูกต้อง
- ถ้าสีผิด ให้เปลี่ยน TFT_RGB_ORDER

#### **ขั้นตอนที่ 7: Final Calibration**
- ตรวจสอบ corner markers อยู่ตำแหน่งถูกต้อง
- ตรวจสอบ center cross อยู่กึ่งกลาง
- ตรวจสอบข้อความอ่านได้ชัดเจน

## 🔧 **การปรับแต่งเพิ่มเติม**

### **หากสียังผิด:**
```ini
# ลอง RGB order อื่น
-DTFT_RGB_ORDER=TFT_RGB  # แทน TFT_BGR
```

### **หากหน้าจอกลับหัว:**
```ini
# ลอง inversion
-DTFT_INVERSION_ON=1     # แทน INVERSION_OFF
```

### **หากสีซีด:**
```ini
# ลดความเร็ว SPI
-DSPI_FREQUENCY=27000000  # แทน 40000000
```

### **หากมีสัญญาณรบกวน:**
```ini
# ลดความเร็ว SPI และเพิ่ม delay
-DSPI_FREQUENCY=20000000
-DSPI_READ_FREQUENCY=10000000
```

## 📱 **การตรวจสอบผลลัพธ์**

### **✅ หน้าจอถูกต้อง:**
- สีแสดงตามที่ควรจะเป็น
- ข้อความอ่านได้ชัดเจน
- Corner markers อยู่มุมที่ถูกต้อง
- Center cross อยู่กึ่งกลางจริง
- ไม่มีสัญญาณรบกวนหรือเส้นแปลกๆ

### **❌ หน้าจอยังผิด:**
- สีผิดเพี้ยน → เปลี่ยน RGB_ORDER
- หน้าจอกลับหัว → เปลี่ยน INVERSION
- มีเส้นรบกวน → ลด SPI_FREQUENCY
- ข้อความไม่ชัด → ตรวจสอบ SMOOTH_FONT

## 🚀 **หลังจาก Calibration เสร็จ**

### **1. บันทึกการตั้งค่าที่ดี:**
```ini
# ตัวอย่างการตั้งค่าที่ใช้ได้ดี
-DTFT_RGB_ORDER=TFT_BGR
-DTFT_INVERSION_OFF=1
-DSPI_FREQUENCY=40000000
```

### **2. เปลี่ยนไปใช้ Main Program:**
```ini
# แก้ไข build_src_filter
build_src_filter = +<*> -<tft_calibration_test.cpp>
```

### **3. ใช้การตั้งค่าเดียวกันใน Main Program:**
```cpp
void setup() {
  pinMode(TFT_BL, OUTPUT);
  digitalWrite(TFT_BL, HIGH);
  
  tft.init();
  tft.setRotation(1);  // ใช้ rotation ที่ดีที่สุด
  
  // ใช้การตั้งค่าเดียวกับที่ test แล้ว
}
```

## 📊 **Build Information**

```
TFT_eSPI @ 2.5.43+sha.5793878 (GitHub Master)
RAM:   [=         ]   6.7% (used 21,908 bytes)
Flash: [==        ]  23.8% (used 311,453 bytes)
Status: SUCCESS ✅
```

## 🎯 **สรุป**

### **การแก้ไขหลัก:**
1. **ใช้ TFT_eSPI จาก GitHub Master** - เวอร์ชันล่าสุด
2. **ปรับ RGB Order เป็น BGR** - แก้ไขสีผิดเพี้ยน
3. **ปิด Color Inversion** - แสดงผลธรรมชาติ
4. **เพิ่ม SPI Frequency** - ความเร็วสูงขึ้น
5. **Reset Pin = -1** - ใช้ software reset

### **ผลลัพธ์:**
- ✅ **สีถูกต้อง** - ไม่เพี้ยนแล้ว
- ✅ **ข้อความชัด** - อ่านได้ง่าย
- ✅ **ไม่มีสัญญาณรบกวน** - หน้าจอเสถียร
- ✅ **Performance ดี** - ความเร็วสูง

**ESP32-2432S028R Display พร้อมใช้งานแล้ว!** 🎉

---

**หมายเหตุ**: หากยังมีปัญหา ให้ลองปรับค่า SPI_FREQUENCY ลงเป็น 27MHz หรือ 20MHz
