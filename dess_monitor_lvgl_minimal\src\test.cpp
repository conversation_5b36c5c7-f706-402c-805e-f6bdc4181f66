#include <Arduino.h>
#include <TFT_eSPI.h>
#include <WiFi.h>
#include <HTTPClient.h>
#include <ArduinoJson.h>

// Hardware pins
#define TFT_BL 21

// Display
TFT_eSPI tft = TFT_eSPI(240, 320);

// WiFi credentials
const char* ssid = "YOUR_WIFI_SSID";
const char* password = "YOUR_WIFI_PASSWORD";

// DESS API Configuration
const char* API_BASE_URL = "https://web.dessmonitor.com/public/";
const char* DEVICE_CODE = "2376";
const char* DEVICE_SN = "Q0046526082082094801";

// Data structure
struct InverterData {
    float solarPower = 1250.5;      // Demo data
    float batteryPercent = 85.2;    // Demo data
    float gridPower = 320.8;        // Demo data
    float loadPower = 980.3;        // Demo data
    float outputVoltage = 230.1;    // Demo data
    float outputFrequency = 50.0;   // Demo data
    String status = "Online";       // Demo data
    bool dataValid = true;
    unsigned long lastUpdate = 0;
};

InverterData data;

// UI Colors
#define COLOR_BACKGROUND    0x0000  // Black
#define COLOR_FRAME         0x2945  // Dark Blue
#define COLOR_TEXT_PRIMARY  0xFFFF  // White
#define COLOR_TEXT_SECONDARY 0x8410 // Gray
#define COLOR_SOLAR         0xFD20  // Orange
#define COLOR_BATTERY       0x07E0  // Green
#define COLOR_GRID          0x001F  // Blue
#define COLOR_LOAD          0xF800  // Red
#define COLOR_STATUS_OK     0x07E0  // Green
#define COLOR_STATUS_ERROR  0xF800  // Red

// Dashboard variables
unsigned long lastDataUpdate = 0;
unsigned long lastUIUpdate = 0;

// Function declarations
void drawDashboard();
void drawDataFrame(int x, int y, int w, int h, String title, uint16_t color);
void updateDashboardValues();
void updateDemoData();

void setup() {
    Serial.begin(115200);
    Serial.println("🚀 Starting DESS Monitor Dashboard...");

    // Initialize backlight
    pinMode(TFT_BL, OUTPUT);
    digitalWrite(TFT_BL, HIGH);
    Serial.println("✅ Backlight initialized");

    // Initialize display
    tft.init();
    tft.setRotation(1); // Landscape 320x240
    tft.fillScreen(COLOR_BACKGROUND);
    Serial.println("✅ TFT Display initialized");

    // Initialize WiFi (optional - using demo data for now)
    Serial.println("📡 WiFi setup (demo mode)");

    // Draw initial dashboard
    drawDashboard();
    Serial.println("📊 Dashboard created");

    Serial.println("🎉 DESS Monitor Dashboard ready!");
}

void loop() {
    // Update data every 5 seconds (simulate real data changes)
    if (millis() - lastDataUpdate > 5000) {
        updateDemoData();
        lastDataUpdate = millis();
    }

    // Update UI every 1 second
    if (millis() - lastUIUpdate > 1000) {
        updateDashboardValues();
        lastUIUpdate = millis();
    }

    delay(100);
}

// Function to draw the main dashboard layout
void drawDashboard() {
    tft.fillScreen(COLOR_BACKGROUND);

    // Title bar
    tft.fillRect(0, 0, 320, 30, COLOR_FRAME);
    tft.setTextColor(COLOR_TEXT_PRIMARY);
    tft.setTextSize(2);
    tft.drawString("DESS Monitor Pro", 10, 8);

    // Time display (top right)
    tft.setTextSize(1);
    tft.drawString("12:34", 280, 12);

    // Draw data frames
    drawDataFrame(10, 40, 145, 80, "SOLAR", COLOR_SOLAR);
    drawDataFrame(165, 40, 145, 80, "BATTERY", COLOR_BATTERY);
    drawDataFrame(10, 130, 145, 80, "GRID", COLOR_GRID);
    drawDataFrame(165, 130, 145, 80, "LOAD", COLOR_LOAD);

    // Status bar at bottom
    tft.fillRect(0, 220, 320, 20, COLOR_FRAME);
    tft.setTextColor(COLOR_TEXT_SECONDARY);
    tft.setTextSize(1);
    tft.drawString("Status: Online", 10, 225);
    tft.drawString("Last Update: Now", 200, 225);

    // Initial data display
    updateDashboardValues();
}

// Function to draw individual data frames
void drawDataFrame(int x, int y, int w, int h, String title, uint16_t color) {
    // Frame border
    tft.drawRect(x, y, w, h, color);
    tft.drawRect(x+1, y+1, w-2, h-2, color);

    // Title background
    tft.fillRect(x+2, y+2, w-4, 20, color);
    tft.setTextColor(COLOR_BACKGROUND);
    tft.setTextSize(1);
    tft.drawString(title, x+8, y+8);
}

// Function to update dashboard values
void updateDashboardValues() {
    // Clear data areas (not the frames)
    tft.fillRect(12, 62, 141, 56, COLOR_BACKGROUND);  // Solar data area
    tft.fillRect(167, 62, 141, 56, COLOR_BACKGROUND); // Battery data area
    tft.fillRect(12, 152, 141, 56, COLOR_BACKGROUND); // Grid data area
    tft.fillRect(167, 152, 141, 56, COLOR_BACKGROUND); // Load data area

    // Solar Power
    tft.setTextColor(COLOR_SOLAR);
    tft.setTextSize(3);
    tft.drawString(String(data.solarPower, 0) + "W", 20, 70);
    tft.setTextColor(COLOR_TEXT_SECONDARY);
    tft.setTextSize(1);
    tft.drawString("Solar Power", 20, 95);

    // Battery
    tft.setTextColor(COLOR_BATTERY);
    tft.setTextSize(3);
    tft.drawString(String(data.batteryPercent, 1) + "%", 175, 70);
    tft.setTextColor(COLOR_TEXT_SECONDARY);
    tft.setTextSize(1);
    tft.drawString("Battery Level", 175, 95);

    // Grid Power
    tft.setTextColor(COLOR_GRID);
    tft.setTextSize(3);
    tft.drawString(String(data.gridPower, 0) + "W", 20, 160);
    tft.setTextColor(COLOR_TEXT_SECONDARY);
    tft.setTextSize(1);
    tft.drawString("Grid Power", 20, 185);

    // Load Power
    tft.setTextColor(COLOR_LOAD);
    tft.setTextSize(3);
    tft.drawString(String(data.loadPower, 0) + "W", 175, 160);
    tft.setTextColor(COLOR_TEXT_SECONDARY);
    tft.setTextSize(1);
    tft.drawString("Load Power", 175, 185);

    // Update status
    tft.fillRect(70, 220, 120, 20, COLOR_FRAME);
    tft.setTextColor(data.dataValid ? COLOR_STATUS_OK : COLOR_STATUS_ERROR);
    tft.setTextSize(1);
    tft.drawString("Status: " + data.status, 10, 225);
}

// Function to simulate changing demo data
void updateDemoData() {
    // Simulate realistic solar power variations
    data.solarPower += random(-100, 100);
    if (data.solarPower < 0) data.solarPower = 0;
    if (data.solarPower > 2000) data.solarPower = 2000;

    // Simulate battery level changes
    data.batteryPercent += random(-2, 1) * 0.1;
    if (data.batteryPercent < 0) data.batteryPercent = 0;
    if (data.batteryPercent > 100) data.batteryPercent = 100;

    // Simulate grid power variations
    data.gridPower += random(-50, 50);
    if (data.gridPower < 0) data.gridPower = 0;
    if (data.gridPower > 1000) data.gridPower = 1000;

    // Simulate load power variations
    data.loadPower += random(-80, 80);
    if (data.loadPower < 100) data.loadPower = 100;
    if (data.loadPower > 1500) data.loadPower = 1500;

    data.lastUpdate = millis();

    Serial.printf("📊 Data updated - Solar: %.0fW, Battery: %.1f%%, Grid: %.0fW, Load: %.0fW\n",
                  data.solarPower, data.batteryPercent, data.gridPower, data.loadPower);
}
