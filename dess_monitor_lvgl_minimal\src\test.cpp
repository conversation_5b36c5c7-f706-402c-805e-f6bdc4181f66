#include <Arduino.h>
#include <TFT_eSPI.h>
#include <WiFi.h>
#include <HTTPClient.h>
#include <ArduinoJson.h>

// Hardware pins
#define TFT_BL 21

// Display
TFT_eSPI tft = TFT_eSPI(240, 320);

// WiFi credentials
const char* ssid = "YOUR_WIFI_SSID";
const char* password = "YOUR_WIFI_PASSWORD";

// Demo mode flag
bool demoMode = true;

// DESS API Configuration
const char* API_BASE_URL = "https://web.dessmonitor.com/public/";
const char* DEVICE_CODE = "2376";
const char* DEVICE_SN = "Q0046526082082094801";

// Data structure
struct InverterData {
    float solarPower = 1250.5;      // Demo data
    float batteryPercent = 85.2;    // Demo data
    float gridPower = 320.8;        // Demo data
    float loadPower = 980.3;        // Demo data
    float outputVoltage = 230.1;    // Demo data
    float outputFrequency = 50.0;   // Demo data
    String status = "Online";       // Demo data
    bool dataValid = true;
    unsigned long lastUpdate = 0;
};

InverterData data;

// UI Colors - Professional Theme
#define COLOR_BACKGROUND    0x0000  // Black
#define COLOR_FRAME         0x2104  // Dark Gray Frame
#define COLOR_TEXT_PRIMARY  0xFFFF  // White
#define COLOR_TEXT_SECONDARY 0xC618 // Light Gray
#define COLOR_SOLAR         0xFD20  // Orange
#define COLOR_BATTERY       0x07E0  // Green
#define COLOR_GRID          0x051F  // Blue
#define COLOR_LOAD          0xF800  // Red
#define COLOR_STATUS_OK     0x07E0  // Green
#define COLOR_STATUS_ERROR  0xF800  // Red
#define COLOR_VALUE_BG      0x1082  // Dark background for values

// Dashboard variables
unsigned long lastDataUpdate = 0;
unsigned long lastUIUpdate = 0;

// Function declarations
void drawDashboard();
void drawDataFrame(int x, int y, int w, int h, String title, uint16_t color);
void updateDashboardValues();
void updateDemoData();
bool fetchRealData();
void parseApiData(const String& response);
void connectWiFi();

void setup() {
    Serial.begin(115200);
    Serial.println("🚀 Starting DESS Monitor Dashboard...");

    // Initialize backlight
    pinMode(TFT_BL, OUTPUT);
    digitalWrite(TFT_BL, HIGH);
    Serial.println("✅ Backlight initialized");

    // Initialize display
    tft.init();
    tft.setRotation(1); // Landscape 320x240
    tft.fillScreen(COLOR_BACKGROUND);
    Serial.println("✅ TFT Display initialized");

    // Try to connect WiFi for real data
    Serial.println("📡 Attempting WiFi connection...");
    connectWiFi();

    // Draw initial dashboard
    drawDashboard();
    Serial.println("📊 Dashboard created");

    Serial.println("🎉 DESS Monitor Dashboard ready!");
}

void loop() {
    // Update data every 10 seconds
    if (millis() - lastDataUpdate > 10000) {
        if (!demoMode && WiFi.status() == WL_CONNECTED) {
            // Try to fetch real data
            if (fetchRealData()) {
                Serial.println("✅ Real data fetched successfully");
            } else {
                Serial.println("❌ Failed to fetch real data, using demo");
                updateDemoData();
            }
        } else {
            // Use demo data
            updateDemoData();
        }
        lastDataUpdate = millis();
    }

    // Update UI every 1 second
    if (millis() - lastUIUpdate > 1000) {
        updateDashboardValues();
        lastUIUpdate = millis();
    }

    delay(100);
}

// Function to draw the main dashboard layout
void drawDashboard() {
    tft.fillScreen(COLOR_BACKGROUND);

    // Title bar with gradient effect
    tft.fillRect(0, 0, 320, 30, COLOR_FRAME);
    tft.drawRect(0, 0, 320, 30, COLOR_TEXT_SECONDARY);
    tft.setTextColor(COLOR_TEXT_PRIMARY, COLOR_FRAME);
    tft.setTextSize(2);
    tft.drawString("DESS Monitor Pro", 10, 8);

    // Time display (top right)
    tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_FRAME);
    tft.setTextSize(1);
    tft.drawString("12:34", 280, 12);

    // Draw data frames
    drawDataFrame(10, 40, 145, 80, "SOLAR", COLOR_SOLAR);
    drawDataFrame(165, 40, 145, 80, "BATTERY", COLOR_BATTERY);
    drawDataFrame(10, 130, 145, 80, "GRID", COLOR_GRID);
    drawDataFrame(165, 130, 145, 80, "LOAD", COLOR_LOAD);

    // Status bar at bottom
    tft.fillRect(0, 220, 320, 20, COLOR_FRAME);
    tft.setTextColor(COLOR_TEXT_SECONDARY);
    tft.setTextSize(1);
    tft.drawString("Status: Online", 10, 225);
    tft.drawString("Last Update: Now", 200, 225);

    // Initial data display
    updateDashboardValues();
}

// Function to draw individual data frames
void drawDataFrame(int x, int y, int w, int h, String title, uint16_t color) {
    // Frame border
    tft.drawRect(x, y, w, h, color);
    tft.drawRect(x+1, y+1, w-2, h-2, color);

    // Title background
    tft.fillRect(x+2, y+2, w-4, 20, color);
    tft.setTextColor(COLOR_BACKGROUND);
    tft.setTextSize(1);
    tft.drawString(title, x+8, y+8);
}

// Function to update dashboard values
void updateDashboardValues() {
    // Clear data areas with dark background
    tft.fillRect(12, 62, 141, 56, COLOR_VALUE_BG);  // Solar data area
    tft.fillRect(167, 62, 141, 56, COLOR_VALUE_BG); // Battery data area
    tft.fillRect(12, 152, 141, 56, COLOR_VALUE_BG); // Grid data area
    tft.fillRect(167, 152, 141, 56, COLOR_VALUE_BG); // Load data area

    // Solar Power - Professional display
    tft.setTextColor(COLOR_SOLAR, COLOR_VALUE_BG);
    tft.setTextSize(3);
    String solarText = String(data.solarPower, 0);
    tft.drawString(solarText, 20, 65);
    tft.setTextColor(COLOR_SOLAR, COLOR_VALUE_BG);
    tft.setTextSize(1);
    tft.drawString("W", 120, 75);
    tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_VALUE_BG);
    tft.drawString("Solar Power", 20, 95);
    tft.drawString(String(data.outputVoltage, 1) + "V", 20, 105);

    // Battery - Professional display with percentage bar
    tft.setTextColor(COLOR_BATTERY, COLOR_VALUE_BG);
    tft.setTextSize(3);
    String batteryText = String(data.batteryPercent, 1);
    tft.drawString(batteryText, 175, 65);
    tft.setTextColor(COLOR_BATTERY, COLOR_VALUE_BG);
    tft.setTextSize(1);
    tft.drawString("%", 270, 75);
    tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_VALUE_BG);
    tft.drawString("Battery Level", 175, 95);

    // Battery percentage bar
    int barWidth = 120;
    int barHeight = 8;
    int barX = 175;
    int barY = 105;
    tft.drawRect(barX, barY, barWidth, barHeight, COLOR_BATTERY);
    int fillWidth = (barWidth - 2) * data.batteryPercent / 100;
    uint16_t barColor = data.batteryPercent > 50 ? COLOR_BATTERY :
                       data.batteryPercent > 20 ? 0xFFE0 : COLOR_LOAD; // Green/Yellow/Red
    tft.fillRect(barX + 1, barY + 1, fillWidth, barHeight - 2, barColor);

    // Grid Power - Professional display
    tft.setTextColor(COLOR_GRID, COLOR_VALUE_BG);
    tft.setTextSize(3);
    String gridText = String(data.gridPower, 0);
    tft.drawString(gridText, 20, 155);
    tft.setTextColor(COLOR_GRID, COLOR_VALUE_BG);
    tft.setTextSize(1);
    tft.drawString("W", 120, 165);
    tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_VALUE_BG);
    tft.drawString("Grid Power", 20, 185);
    tft.drawString(String(data.outputFrequency, 1) + "Hz", 20, 195);

    // Load Power - Professional display
    tft.setTextColor(COLOR_LOAD, COLOR_VALUE_BG);
    tft.setTextSize(3);
    String loadText = String(data.loadPower, 0);
    tft.drawString(loadText, 175, 155);
    tft.setTextColor(COLOR_LOAD, COLOR_VALUE_BG);
    tft.setTextSize(1);
    tft.drawString("W", 270, 165);
    tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_VALUE_BG);
    tft.drawString("Load Power", 175, 185);

    // Load percentage
    float loadPercent = (data.loadPower / 2000.0) * 100; // Assume max 2000W
    tft.drawString(String(loadPercent, 1) + "%", 175, 195);

    // Update status bar
    tft.fillRect(0, 220, 320, 20, COLOR_FRAME);
    tft.setTextColor(data.dataValid ? COLOR_STATUS_OK : COLOR_STATUS_ERROR, COLOR_FRAME);
    tft.setTextSize(1);
    tft.drawString("Status: " + data.status, 10, 225);

    // Update time
    tft.setTextColor(COLOR_TEXT_SECONDARY, COLOR_FRAME);
    tft.drawString("Last: " + String(millis()/1000) + "s", 200, 225);

    // Connection indicator
    tft.fillCircle(300, 230, 3, demoMode ? 0xFFE0 : COLOR_STATUS_OK); // Yellow for demo, Green for real
}

// Function to simulate changing demo data
void updateDemoData() {
    // Simulate realistic solar power variations
    data.solarPower += random(-100, 100);
    if (data.solarPower < 0) data.solarPower = 0;
    if (data.solarPower > 2000) data.solarPower = 2000;

    // Simulate battery level changes
    data.batteryPercent += random(-2, 1) * 0.1;
    if (data.batteryPercent < 0) data.batteryPercent = 0;
    if (data.batteryPercent > 100) data.batteryPercent = 100;

    // Simulate grid power variations
    data.gridPower += random(-50, 50);
    if (data.gridPower < 0) data.gridPower = 0;
    if (data.gridPower > 1000) data.gridPower = 1000;

    // Simulate load power variations
    data.loadPower += random(-80, 80);
    if (data.loadPower < 100) data.loadPower = 100;
    if (data.loadPower > 1500) data.loadPower = 1500;

    data.lastUpdate = millis();

    Serial.printf("📊 Data updated - Solar: %.0fW, Battery: %.1f%%, Grid: %.0fW, Load: %.0fW\n",
                  data.solarPower, data.batteryPercent, data.gridPower, data.loadPower);
}

// Function to connect WiFi
void connectWiFi() {
    // Skip WiFi if credentials not set
    if (String(ssid) == "YOUR_WIFI_SSID") {
        Serial.println("📡 WiFi credentials not set, using demo mode");
        demoMode = true;
        return;
    }

    WiFi.begin(ssid, password);
    Serial.print("📡 Connecting to WiFi");

    int attempts = 0;
    while (WiFi.status() != WL_CONNECTED && attempts < 20) {
        delay(500);
        Serial.print(".");
        attempts++;
    }

    if (WiFi.status() == WL_CONNECTED) {
        Serial.println();
        Serial.printf("✅ WiFi connected! IP: %s\n", WiFi.localIP().toString().c_str());
        demoMode = false;
    } else {
        Serial.println();
        Serial.println("❌ WiFi connection failed, using demo mode");
        demoMode = true;
    }
}

// Function to fetch real data from API
bool fetchRealData() {
    if (WiFi.status() != WL_CONNECTED) return false;

    HTTPClient http;
    String url = String(API_BASE_URL) +
                 "?sign=8743221c28ad40664baa48193bbf4b03caa726f1" +
                 "&salt=" + String(millis()) +
                 "&token=c8075906465cca180724b25d151680e31e2267ff1319ccf2b43c88ba979a7576" +
                 "&action=querySPDeviceLastData" +
                 "&source=1" +
                 "&devcode=" + String(DEVICE_CODE) +
                 "&pn=" + String(DEVICE_SN) +
                 "&devaddr=1" +
                 "&sn=" + String(DEVICE_SN) +
                 "&i18n=en_US";

    http.begin(url);
    http.setTimeout(10000);

    int httpCode = http.GET();

    if (httpCode == 200) {
        String response = http.getString();
        parseApiData(response);
        http.end();
        return true;
    }

    http.end();
    return false;
}

// Function to parse API data
void parseApiData(const String& response) {
    DynamicJsonDocument doc(4096);
    DeserializationError error = deserializeJson(doc, response);

    if (error) {
        Serial.printf("❌ JSON parsing failed: %s\n", error.c_str());
        return;
    }

    if (doc["err"] != 0) {
        Serial.printf("❌ API error: %s\n", doc["desc"].as<String>().c_str());
        return;
    }

    // Parse different parameter groups
    JsonObject pars = doc["dat"]["pars"];

    // Parse PV data
    if (pars["pv_"].is<JsonArray>()) {
        for (JsonObject param : pars["pv_"].as<JsonArray>()) {
            String parName = param["par"].as<String>();
            if (parName == "PV Power") {
                data.solarPower = param["val"].as<float>();
            }
        }
    }

    // Parse Battery data
    if (pars["bt_"].is<JsonArray>()) {
        for (JsonObject param : pars["bt_"].as<JsonArray>()) {
            String parName = param["par"].as<String>();
            if (parName == "bt_battery_capacity") {
                data.batteryPercent = param["val"].as<float>();
            }
        }
    }

    // Parse Grid data
    if (pars["gd_"].is<JsonArray>()) {
        for (JsonObject param : pars["gd_"].as<JsonArray>()) {
            String parName = param["par"].as<String>();
            if (parName == "Grid Power") {
                data.gridPower = param["val"].as<float>();
            }
        }
    }

    // Parse Output data (bc_ section for output data)
    if (pars["bc_"].is<JsonArray>()) {
        for (JsonObject param : pars["bc_"].as<JsonArray>()) {
            String parName = param["par"].as<String>();
            if (parName == "Active Power") {
                data.loadPower = param["val"].as<float>();
            } else if (parName == "Output Voltage") {
                data.outputVoltage = param["val"].as<float>();
            } else if (parName == "Frequency") {
                data.outputFrequency = param["val"].as<float>();
            }
        }
    }

    data.dataValid = true;
    data.lastUpdate = millis();
    data.status = "Online";

    Serial.println("✅ Real API data parsed successfully");
    Serial.printf("📊 Real Data - Solar: %.0fW, Battery: %.1f%%, Grid: %.0fW, Load: %.0fW\n",
                  data.solarPower, data.batteryPercent, data.gridPower, data.loadPower);
}
