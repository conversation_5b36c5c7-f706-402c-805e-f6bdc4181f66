#include "themes.h"

// Global theme manager instance
ThemeManager themeManager;

// Predefined theme configurations
const ThemeConfig DARK_THEME = {
  .name = "Dark",
  .icon = "🌙",
  .colors = {
    .background = lv_color_hex(0x000000),
    .surface = lv_color_hex(0x121212),
    .card = lv_color_hex(0x1E1E1E),
    .textPrimary = lv_color_hex(0xFFFFFF),
    .textSecondary = lv_color_hex(0xB0B0B0),
    .textDisabled = lv_color_hex(0x666666),
    .primary = lv_color_hex(0x1E88E5),
    .secondary = lv_color_hex(0x43A047),
    .accent = lv_color_hex(0xFF9800),
    .success = lv_color_hex(0x4CAF50),
    .warning = lv_color_hex(0xFFC107),
    .error = lv_color_hex(0xF44336),
    .info = lv_color_hex(0x2196F3),
    .solar = lv_color_hex(0xFFC107),
    .battery = lv_color_hex(0x4CAF50),
    .grid = lv_color_hex(0x2196F3),
    .load = lv_color_hex(0xFF5722),
    .border = lv_color_hex(0x333333),
    .shadow = lv_color_hex(0x000000),
    .highlight = lv_color_hex(0x444444),
    .disabled = lv_color_hex(0x555555)
  },
  .isDark = true,
  .cardRadius = 12,
  .shadowSize = 8,
  .borderWidth = 1,
  .shadowOpacity = LV_OPA_30
};

const ThemeConfig LIGHT_THEME = {
  .name = "Light",
  .icon = "☀️",
  .colors = {
    .background = lv_color_hex(0xFFFFFF),
    .surface = lv_color_hex(0xF5F5F5),
    .card = lv_color_hex(0xFFFFFF),
    .textPrimary = lv_color_hex(0x000000),
    .textSecondary = lv_color_hex(0x666666),
    .textDisabled = lv_color_hex(0x999999),
    .primary = lv_color_hex(0x1976D2),
    .secondary = lv_color_hex(0x388E3C),
    .accent = lv_color_hex(0xF57C00),
    .success = lv_color_hex(0x4CAF50),
    .warning = lv_color_hex(0xFF9800),
    .error = lv_color_hex(0xF44336),
    .info = lv_color_hex(0x2196F3),
    .solar = lv_color_hex(0xFF9800),
    .battery = lv_color_hex(0x4CAF50),
    .grid = lv_color_hex(0x2196F3),
    .load = lv_color_hex(0xFF5722),
    .border = lv_color_hex(0xE0E0E0),
    .shadow = lv_color_hex(0x000000),
    .highlight = lv_color_hex(0xF0F0F0),
    .disabled = lv_color_hex(0xBDBDBD)
  },
  .isDark = false,
  .cardRadius = 12,
  .shadowSize = 4,
  .borderWidth = 1,
  .shadowOpacity = LV_OPA_20
};

const ThemeConfig SOLAR_THEME = {
  .name = "Solar",
  .icon = "🌞",
  .colors = {
    .background = lv_color_hex(0x0D1B2A),
    .surface = lv_color_hex(0x1B263B),
    .card = lv_color_hex(0x415A77),
    .textPrimary = lv_color_hex(0xE0E1DD),
    .textSecondary = lv_color_hex(0x778DA9),
    .textDisabled = lv_color_hex(0x555555),
    .primary = lv_color_hex(0xFFB700),
    .secondary = lv_color_hex(0xFF8500),
    .accent = lv_color_hex(0xFFD60A),
    .success = lv_color_hex(0x4CAF50),
    .warning = lv_color_hex(0xFFC107),
    .error = lv_color_hex(0xF44336),
    .info = lv_color_hex(0x2196F3),
    .solar = lv_color_hex(0xFFD60A),
    .battery = lv_color_hex(0x4CAF50),
    .grid = lv_color_hex(0x2196F3),
    .load = lv_color_hex(0xFF5722),
    .border = lv_color_hex(0x778DA9),
    .shadow = lv_color_hex(0x000000),
    .highlight = lv_color_hex(0x778DA9),
    .disabled = lv_color_hex(0x555555)
  },
  .isDark = true,
  .cardRadius = 15,
  .shadowSize = 10,
  .borderWidth = 2,
  .shadowOpacity = LV_OPA_40
};

const ThemeConfig BLUE_THEME = {
  .name = "Blue",
  .icon = "💙",
  .colors = {
    .background = lv_color_hex(0x0A1929),
    .surface = lv_color_hex(0x1E293B),
    .card = lv_color_hex(0x334155),
    .textPrimary = lv_color_hex(0xF1F5F9),
    .textSecondary = lv_color_hex(0x94A3B8),
    .textDisabled = lv_color_hex(0x64748B),
    .primary = lv_color_hex(0x3B82F6),
    .secondary = lv_color_hex(0x06B6D4),
    .accent = lv_color_hex(0x8B5CF6),
    .success = lv_color_hex(0x10B981),
    .warning = lv_color_hex(0xF59E0B),
    .error = lv_color_hex(0xEF4444),
    .info = lv_color_hex(0x06B6D4),
    .solar = lv_color_hex(0xF59E0B),
    .battery = lv_color_hex(0x10B981),
    .grid = lv_color_hex(0x3B82F6),
    .load = lv_color_hex(0xEF4444),
    .border = lv_color_hex(0x475569),
    .shadow = lv_color_hex(0x000000),
    .highlight = lv_color_hex(0x475569),
    .disabled = lv_color_hex(0x64748B)
  },
  .isDark = true,
  .cardRadius = 10,
  .shadowSize = 6,
  .borderWidth = 1,
  .shadowOpacity = LV_OPA_25
};

const ThemeConfig GREEN_THEME = {
  .name = "Green",
  .icon = "💚",
  .colors = {
    .background = lv_color_hex(0x0F172A),
    .surface = lv_color_hex(0x1E293B),
    .card = lv_color_hex(0x374151),
    .textPrimary = lv_color_hex(0xF9FAFB),
    .textSecondary = lv_color_hex(0x9CA3AF),
    .textDisabled = lv_color_hex(0x6B7280),
    .primary = lv_color_hex(0x059669),
    .secondary = lv_color_hex(0x0D9488),
    .accent = lv_color_hex(0x84CC16),
    .success = lv_color_hex(0x22C55E),
    .warning = lv_color_hex(0xEAB308),
    .error = lv_color_hex(0xDC2626),
    .info = lv_color_hex(0x0EA5E9),
    .solar = lv_color_hex(0xEAB308),
    .battery = lv_color_hex(0x22C55E),
    .grid = lv_color_hex(0x0EA5E9),
    .load = lv_color_hex(0xDC2626),
    .border = lv_color_hex(0x4B5563),
    .shadow = lv_color_hex(0x000000),
    .highlight = lv_color_hex(0x4B5563),
    .disabled = lv_color_hex(0x6B7280)
  },
  .isDark = true,
  .cardRadius = 8,
  .shadowSize = 5,
  .borderWidth = 1,
  .shadowOpacity = LV_OPA_20
};

ThemeManager::ThemeManager() {
  currentTheme = ThemeType::DARK;
  lvglTheme = nullptr;
  autoThemeEnabled = false;
  lastThemeChange = 0;
}

ThemeManager::~ThemeManager() {
  // LVGL will handle cleanup
}

void ThemeManager::initialize() {
  Serial.println("🎨 Initializing Theme Manager...");
  
  setupThemes();
  setTheme(ThemeType::DARK);
  
  Serial.println("✅ Theme Manager initialized");
}

void ThemeManager::setupThemes() {
  themes[0] = DARK_THEME;
  themes[1] = LIGHT_THEME;
  themes[2] = SOLAR_THEME;
  themes[3] = BLUE_THEME;
  themes[4] = GREEN_THEME;
}

void ThemeManager::setTheme(ThemeType theme) {
  if (theme == currentTheme) return;
  
  currentTheme = theme;
  lastThemeChange = millis();
  
  applyTheme();
  
  Serial.printf("🎭 Theme changed to: %s\n", getCurrentThemeConfig().name);
}

ThemeType ThemeManager::getCurrentTheme() {
  return currentTheme;
}

const ThemeConfig& ThemeManager::getCurrentThemeConfig() {
  return themes[(int)currentTheme];
}

const ThemeColors& ThemeManager::getCurrentColors() {
  return themes[(int)currentTheme].colors;
}

void ThemeManager::nextTheme() {
  int next = ((int)currentTheme + 1) % 5;
  setTheme((ThemeType)next);
}

void ThemeManager::previousTheme() {
  int prev = ((int)currentTheme - 1 + 5) % 5;
  setTheme((ThemeType)prev);
}

void ThemeManager::toggleDarkLight() {
  if (currentTheme == ThemeType::DARK) {
    setTheme(ThemeType::LIGHT);
  } else {
    setTheme(ThemeType::DARK);
  }
}

void ThemeManager::enableAutoTheme(bool enabled) {
  autoThemeEnabled = enabled;
  Serial.printf("🤖 Auto theme %s\n", enabled ? "enabled" : "disabled");
}

bool ThemeManager::isAutoThemeEnabled() {
  return autoThemeEnabled;
}

void ThemeManager::updateAutoTheme() {
  if (!autoThemeEnabled) return;
  
  // Get current hour (0-23)
  struct tm timeinfo;
  if (!getLocalTime(&timeinfo)) return;
  
  int hour = timeinfo.tm_hour;
  
  // Auto switch based on time
  if (hour >= 6 && hour < 18) {
    // Daytime - use light theme
    if (currentTheme == ThemeType::DARK) {
      setTheme(ThemeType::LIGHT);
    }
  } else {
    // Nighttime - use dark theme
    if (currentTheme == ThemeType::LIGHT) {
      setTheme(ThemeType::DARK);
    }
  }
}

void ThemeManager::applyTheme() {
  const ThemeConfig& config = getCurrentThemeConfig();
  
  // Create LVGL theme
  lvglTheme = lv_theme_default_init(lv_disp_get_default(),
                                   config.colors.primary,
                                   config.colors.secondary,
                                   config.isDark,
                                   &lv_font_montserrat_16);
  
  // Apply to display
  lv_disp_set_theme(lv_disp_get_default(), lvglTheme);
  
  Serial.printf("🎨 Applied theme: %s\n", config.name);
}

void ThemeManager::applyToObject(lv_obj_t* obj) {
  if (!obj) return;
  
  const ThemeColors& colors = getCurrentColors();
  
  // Apply basic styling
  lv_obj_set_style_bg_color(obj, colors.background, 0);
  lv_obj_set_style_text_color(obj, colors.textPrimary, 0);
}

void ThemeManager::applyCardStyle(lv_obj_t* card, lv_color_t accentColor) {
  if (!card) return;
  
  const ThemeConfig& config = getCurrentThemeConfig();
  const ThemeColors& colors = config.colors;
  
  // Card background
  lv_obj_set_style_bg_color(card, colors.card, 0);
  lv_obj_set_style_bg_opa(card, LV_OPA_COVER, 0);
  
  // Border
  lv_obj_set_style_border_color(card, accentColor, 0);
  lv_obj_set_style_border_width(card, config.borderWidth, 0);
  lv_obj_set_style_border_opa(card, LV_OPA_50, 0);
  
  // Radius
  lv_obj_set_style_radius(card, config.cardRadius, 0);
  
  // Shadow
  lv_obj_set_style_shadow_color(card, colors.shadow, 0);
  lv_obj_set_style_shadow_width(card, config.shadowSize, 0);
  lv_obj_set_style_shadow_opa(card, config.shadowOpacity, 0);
}

void ThemeManager::applyButtonStyle(lv_obj_t* button, bool isPrimary) {
  if (!button) return;
  
  const ThemeColors& colors = getCurrentColors();
  
  if (isPrimary) {
    lv_obj_set_style_bg_color(button, colors.primary, 0);
    lv_obj_set_style_text_color(button, lv_color_white(), 0);
  } else {
    lv_obj_set_style_bg_color(button, colors.surface, 0);
    lv_obj_set_style_text_color(button, colors.textPrimary, 0);
  }
  
  lv_obj_set_style_radius(button, 8, 0);
  lv_obj_set_style_shadow_width(button, 4, 0);
  lv_obj_set_style_shadow_opa(button, LV_OPA_30, 0);
}

void ThemeManager::applyLabelStyle(lv_obj_t* label, bool isSecondary) {
  if (!label) return;
  
  const ThemeColors& colors = getCurrentColors();
  
  if (isSecondary) {
    lv_obj_set_style_text_color(label, colors.textSecondary, 0);
  } else {
    lv_obj_set_style_text_color(label, colors.textPrimary, 0);
  }
}

lv_color_t ThemeManager::getComponentColor(const char* component) {
  const ThemeColors& colors = getCurrentColors();
  
  if (strcmp(component, "solar") == 0) return colors.solar;
  if (strcmp(component, "battery") == 0) return colors.battery;
  if (strcmp(component, "grid") == 0) return colors.grid;
  if (strcmp(component, "load") == 0) return colors.load;
  
  return colors.primary;
}

lv_color_t ThemeManager::getStatusColor(const char* status) {
  const ThemeColors& colors = getCurrentColors();
  
  if (strcmp(status, "success") == 0) return colors.success;
  if (strcmp(status, "warning") == 0) return colors.warning;
  if (strcmp(status, "error") == 0) return colors.error;
  if (strcmp(status, "info") == 0) return colors.info;
  
  return colors.textPrimary;
}

lv_color_t ThemeManager::blendColors(lv_color_t color1, lv_color_t color2, uint8_t ratio) {
  return lv_color_mix(color1, color2, ratio);
}

lv_color_t ThemeManager::adjustBrightness(lv_color_t color, int8_t adjustment) {
  // Simple brightness adjustment
  if (adjustment > 0) {
    return lv_color_lighten(color, adjustment);
  } else {
    return lv_color_darken(color, -adjustment);
  }
}

void ThemeManager::animateThemeChange(uint32_t duration) {
  // TODO: Implement smooth theme transition animation
  Serial.printf("🎬 Animating theme change over %d ms\n", duration);
}

// Utility functions
lv_color_t hexToLvColor(uint32_t hex) {
  return lv_color_hex(hex);
}

uint32_t lvColorToHex(lv_color_t color) {
  return lv_color_to32(color) & 0xFFFFFF;
}

lv_color_t rgbToLvColor(uint8_t r, uint8_t g, uint8_t b) {
  return lv_color_make(r, g, b);
}

void lvColorToRgb(lv_color_t color, uint8_t& r, uint8_t& g, uint8_t& b) {
  uint32_t hex = lv_color_to32(color);
  r = (hex >> 16) & 0xFF;
  g = (hex >> 8) & 0xFF;
  b = hex & 0xFF;
}
