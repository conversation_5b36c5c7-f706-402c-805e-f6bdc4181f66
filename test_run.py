#!/usr/bin/env python3
"""
Test run without actual login - simulates data collection
"""

import json
import requests
from datetime import datetime
from dess_monitor import DESSMonitor

def test_direct_api_call():
    """Test direct API call using the provided URL"""
    print("🔍 Testing direct API data collection...")
    
    try:
        # Use the working URL you provided
        url = "https://web.dessmonitor.com/public/?sign=8743221c28ad40664baa48193bbf4b03caa726f1&salt=1748162984217&token=c8075906465cca180724b25d151680e31e2267ff1319ccf2b43c88ba979a7576&action=querySPDeviceLastData&source=1&devcode=2376&pn=Q0046526082082&devaddr=1&sn=Q0046526082082094801&i18n=en_US"
        
        print("📡 Fetching data from DESS Monitor API...")
        response = requests.get(url, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('err') == 0:
                print("✅ API call successful!")
                
                # Parse the data
                monitor = DESSMonitor()
                parsed_data = monitor.parse_device_data(data)
                
                if parsed_data:
                    print("✅ Data parsing successful!")
                    
                    # Display current data
                    print(f"\n📊 Current Inverter Data ({parsed_data['timestamp']}):")
                    print("="*50)
                    
                    for key, value in parsed_data.items():
                        if isinstance(value, dict) and 'value' in value:
                            print(f"{key.replace('_', ' ').title():<25}: {value['value']} {value['unit']}")
                    
                    # Save data
                    csv_success = monitor.save_data_to_csv(parsed_data)
                    json_success = monitor.save_data_to_json(parsed_data)
                    
                    if csv_success and json_success:
                        print("\n✅ Data saved successfully!")
                        return True
                    else:
                        print("\n❌ Failed to save data")
                        return False
                else:
                    print("❌ Failed to parse data")
                    return False
            else:
                print(f"❌ API error: {data.get('desc', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_data_analysis():
    """Test data analysis on collected data"""
    print("\n🔍 Testing data analysis...")
    
    try:
        from data_analyzer import DataAnalyzer
        
        analyzer = DataAnalyzer()
        
        # Load data
        df = analyzer.load_data(days_back=1)
        
        if df is not None and not df.empty:
            print(f"✅ Loaded {len(df)} data points")
            
            # Generate daily report
            daily_stats = analyzer.generate_daily_report(df)
            if daily_stats:
                print("\n📈 Daily Statistics:")
                for key, value in daily_stats.items():
                    if isinstance(value, (int, float)):
                        print(f"{key.replace('_', ' ').title():<25}: {value:.2f}")
                    else:
                        print(f"{key.replace('_', ' ').title():<25}: {value}")
            
            print("✅ Data analysis completed!")
            return True
        else:
            print("⚠️  No data available for analysis")
            return True  # Not an error, just no data yet
            
    except Exception as e:
        print(f"❌ Data analysis error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing DESS Monitor Data Collection\n")
    
    # Test direct API call
    api_success = test_direct_api_call()
    
    # Test data analysis
    analysis_success = test_data_analysis()
    
    print("\n" + "="*50)
    print("📊 TEST RUN SUMMARY")
    print("="*50)
    
    if api_success:
        print("✅ API Data Collection: SUCCESS")
    else:
        print("❌ API Data Collection: FAILED")
    
    if analysis_success:
        print("✅ Data Analysis: SUCCESS")
    else:
        print("❌ Data Analysis: FAILED")
    
    if api_success and analysis_success:
        print("\n🎉 All tests successful! System is working properly.")
        print("\n📝 Next steps:")
        print("1. Edit .env file with your real DESS Monitor credentials")
        print("2. Run: python main.py --mode once")
        print("3. For continuous monitoring: python main.py --mode continuous")
    else:
        print("\n⚠️  Some tests failed. Please check the errors above.")
