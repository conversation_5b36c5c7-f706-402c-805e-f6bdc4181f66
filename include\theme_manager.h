#ifndef THEME_MANAGER_H
#define THEME_MANAGER_H

#include <Arduino.h>
#include "config.h"

// ===== THEME MACROS =====
#define COLOR_BG           theme->bg
#define COLOR_HEADER       theme->header
#define COLOR_CARD_BG      theme->cardBg
#define COLOR_CARD_BORDER  theme->cardBorder
#define COLOR_TEXT_PRIMARY theme->textPrimary
#define COLOR_TEXT_SECONDARY theme->textSecondary
#define COLOR_TEXT_DIM     theme->textDim
#define COLOR_ACCENT       theme->accent
#define COLOR_SOLAR        theme->solar
#define COLOR_SOLAR_GLOW   theme->solarGlow
#define COLOR_BATTERY_GOOD theme->batteryGood
#define COLOR_BATTERY_MID  theme->batteryMid
#define COLOR_BATTERY_LOW  theme->batteryLow
#define COLOR_GRID         theme->grid
#define COLOR_GRID_GLOW    theme->gridGlow
#define COLOR_OUTPUT       theme->output
#define COLOR_OUTPUT_GLOW  theme->outputGlow
#define COLOR_SUCCESS      theme->success
#define COLOR_WARNING      theme->warning
#define COLOR_ERROR        theme->error
#define COLOR_GLOW_EFFECT  theme->glow
#define COLOR_SHADOW       theme->shadow
#define COLOR_HIGHLIGHT    theme->highlight

// ===== GLOBAL THEME VARIABLES =====
extern const ColorTheme* theme;
extern int currentTheme;

// ===== THEME FUNCTIONS =====
void initializeThemes();
void changeTheme(int themeIndex);
const ColorTheme* getTheme(int index);
String getThemeName(int index);

// ===== COLOR UTILITY FUNCTIONS =====
uint16_t getBatteryColor(float voltage);
uint16_t getEnhancedBatteryColor(float level);

#endif // THEME_MANAGER_H
