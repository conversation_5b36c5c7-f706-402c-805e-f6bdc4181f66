/*******************************************************************************
 * Size: 90 px
 * Bpp: 1
 * Opts: 
 ******************************************************************************/

#include "lvgl.h"

#ifndef OPENSANS_90
#define OPENSANS_90 1
#endif

#if OPENSANS_90

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */
    0x0,

    /* U+0030 "0" */
    0x0, 0x7, 0xfc, 0x0, 0x0, 0x3, 0xff, 0xf0,
    0x0, 0x1, 0xff, 0xff, 0x0, 0x0, 0x7f, 0xff,
    0xf8, 0x0, 0x1f, 0xff, 0xff, 0x80, 0x7, 0xff,
    0xff, 0xf0, 0x1, 0xff, 0xff, 0xff, 0x0, 0x7f,
    0xff, 0xff, 0xf0, 0xf, 0xff, 0xff, 0xfe, 0x3,
    0xff, 0xff, 0xff, 0xe0, 0x7f, 0xfc, 0x7f, 0xfc,
    0xf, 0xff, 0x7, 0xff, 0x83, 0xff, 0xc0, 0x7f,
    0xf8, 0x7f, 0xf8, 0xf, 0xff, 0xf, 0xfe, 0x0,
    0xff, 0xe1, 0xff, 0xc0, 0x1f, 0xfe, 0x7f, 0xf8,
    0x3, 0xff, 0xcf, 0xff, 0x0, 0x7f, 0xf9, 0xff,
    0xe0, 0xf, 0xff, 0x3f, 0xf8, 0x1, 0xff, 0xe7,
    0xff, 0x0, 0x1f, 0xfc, 0xff, 0xe0, 0x3, 0xff,
    0xbf, 0xfc, 0x0, 0x7f, 0xff, 0xff, 0x80, 0xf,
    0xff, 0xff, 0xf0, 0x1, 0xff, 0xff, 0xfe, 0x0,
    0x3f, 0xff, 0xff, 0xc0, 0x7, 0xff, 0xff, 0xf8,
    0x0, 0xff, 0xff, 0xff, 0x0, 0x1f, 0xff, 0xff,
    0xe0, 0x3, 0xff, 0xff, 0xfc, 0x0, 0x7f, 0xff,
    0xff, 0x80, 0xf, 0xff, 0xff, 0xf0, 0x1, 0xff,
    0xff, 0xfe, 0x0, 0x3f, 0xff, 0xff, 0xc0, 0x7,
    0xff, 0xff, 0xf8, 0x0, 0xff, 0xff, 0xff, 0x0,
    0x1f, 0xff, 0xff, 0xe0, 0x3, 0xff, 0xff, 0xfc,
    0x0, 0x7f, 0xff, 0xff, 0x80, 0xf, 0xff, 0xff,
    0xf0, 0x1, 0xff, 0xff, 0xfe, 0x0, 0x3f, 0xff,
    0xff, 0xc0, 0x7, 0xff, 0xff, 0xf8, 0x0, 0xff,
    0xf7, 0xff, 0x0, 0x1f, 0xfc, 0xff, 0xe0, 0x3,
    0xff, 0x9f, 0xfc, 0x0, 0x7f, 0xf3, 0xff, 0xc0,
    0x1f, 0xfe, 0x7f, 0xf8, 0x3, 0xff, 0xcf, 0xff,
    0x0, 0x7f, 0xf9, 0xff, 0xe0, 0xf, 0xfe, 0x1f,
    0xfc, 0x1, 0xff, 0xc3, 0xff, 0x80, 0x7f, 0xf8,
    0x7f, 0xf8, 0xf, 0xff, 0xf, 0xff, 0x83, 0xff,
    0xc0, 0xff, 0xf8, 0xff, 0xf8, 0x1f, 0xff, 0xff,
    0xff, 0x1, 0xff, 0xff, 0xff, 0xc0, 0x3f, 0xff,
    0xff, 0xf8, 0x3, 0xff, 0xff, 0xfe, 0x0, 0x7f,
    0xff, 0xff, 0x80, 0x7, 0xff, 0xff, 0xf0, 0x0,
    0x7f, 0xff, 0xf8, 0x0, 0x7, 0xff, 0xfe, 0x0,
    0x0, 0x3f, 0xff, 0x0, 0x0, 0x0, 0xff, 0x80,
    0x0,

    /* U+0031 "1" */
    0x0, 0x0, 0x7f, 0xf0, 0x0, 0xf, 0xff, 0x0,
    0x3, 0xff, 0xf0, 0x0, 0x7f, 0xff, 0x0, 0xf,
    0xff, 0xf0, 0x1, 0xff, 0xff, 0x0, 0x3f, 0xff,
    0xf0, 0x7, 0xff, 0xff, 0x0, 0xff, 0xff, 0xf0,
    0x1f, 0xff, 0xff, 0x3, 0xff, 0xff, 0xf0, 0x7f,
    0xff, 0xff, 0xf, 0xff, 0xff, 0xf3, 0xff, 0xf7,
    0xff, 0x7f, 0xfe, 0xff, 0xff, 0xff, 0xcf, 0xff,
    0x7f, 0xf8, 0xff, 0xf3, 0xff, 0xf, 0xff, 0x1f,
    0xe0, 0xff, 0xf1, 0xfc, 0xf, 0xff, 0xf, 0x80,
    0xff, 0xf0, 0x70, 0xf, 0xff, 0x2, 0x0, 0xff,
    0xf0, 0x0, 0xf, 0xff, 0x0, 0x0, 0xff, 0xf0,
    0x0, 0xf, 0xff, 0x0, 0x0, 0xff, 0xf0, 0x0,
    0xf, 0xff, 0x0, 0x0, 0xff, 0xf0, 0x0, 0xf,
    0xff, 0x0, 0x0, 0xff, 0xf0, 0x0, 0xf, 0xff,
    0x0, 0x0, 0xff, 0xf0, 0x0, 0xf, 0xff, 0x0,
    0x0, 0xff, 0xf0, 0x0, 0xf, 0xff, 0x0, 0x0,
    0xff, 0xf0, 0x0, 0xf, 0xff, 0x0, 0x0, 0xff,
    0xf0, 0x0, 0xf, 0xff, 0x0, 0x0, 0xff, 0xf0,
    0x0, 0xf, 0xff, 0x0, 0x0, 0xff, 0xf0, 0x0,
    0xf, 0xff, 0x0, 0x0, 0xff, 0xf0, 0x0, 0xf,
    0xff, 0x0, 0x0, 0xff, 0xf0, 0x0, 0xf, 0xff,
    0x0, 0x0, 0xff, 0xf0, 0x0, 0xf, 0xff, 0x0,
    0x0, 0xff, 0xf0, 0x0, 0xf, 0xff, 0x0, 0x0,
    0xff, 0xf0, 0x0, 0xf, 0xff, 0x0, 0x0, 0xff,
    0xf0, 0x0, 0xf, 0xff, 0x0, 0x0, 0xff, 0xf0,
    0x0, 0xf, 0xff, 0x0, 0x0, 0xff, 0xf0, 0x0,
    0xf, 0xff, 0x0, 0x0, 0xff, 0xf0, 0x0, 0xf,
    0xff, 0x0, 0x0, 0xff, 0xf0, 0x0, 0xf, 0xff,

    /* U+0032 "2" */
    0x0, 0x7, 0xfc, 0x0, 0x0, 0x7, 0xff, 0xf0,
    0x0, 0x3, 0xff, 0xff, 0x80, 0x1, 0xff, 0xff,
    0xfc, 0x0, 0x7f, 0xff, 0xff, 0xc0, 0x1f, 0xff,
    0xff, 0xf8, 0x7, 0xff, 0xff, 0xff, 0x81, 0xff,
    0xff, 0xff, 0xf8, 0x7f, 0xff, 0xff, 0xff, 0xf,
    0xff, 0xff, 0xff, 0xf0, 0xff, 0xe0, 0x7f, 0xfe,
    0xf, 0xf0, 0x7, 0xff, 0xc0, 0xfc, 0x0, 0x7f,
    0xfc, 0xf, 0x0, 0xf, 0xff, 0x80, 0xc0, 0x0,
    0xff, 0xf0, 0x0, 0x0, 0x1f, 0xfe, 0x0, 0x0,
    0x3, 0xff, 0xc0, 0x0, 0x0, 0x7f, 0xf8, 0x0,
    0x0, 0xf, 0xff, 0x0, 0x0, 0x1, 0xff, 0xe0,
    0x0, 0x0, 0x3f, 0xfc, 0x0, 0x0, 0x7, 0xff,
    0x80, 0x0, 0x1, 0xff, 0xf0, 0x0, 0x0, 0x3f,
    0xfc, 0x0, 0x0, 0x7, 0xff, 0x80, 0x0, 0x0,
    0xff, 0xf0, 0x0, 0x0, 0x3f, 0xfe, 0x0, 0x0,
    0x7, 0xff, 0x80, 0x0, 0x1, 0xff, 0xf0, 0x0,
    0x0, 0x3f, 0xfc, 0x0, 0x0, 0xf, 0xff, 0x80,
    0x0, 0x1, 0xff, 0xe0, 0x0, 0x0, 0x7f, 0xfc,
    0x0, 0x0, 0x1f, 0xff, 0x0, 0x0, 0x3, 0xff,
    0xe0, 0x0, 0x0, 0xff, 0xf8, 0x0, 0x0, 0x3f,
    0xfe, 0x0, 0x0, 0x7, 0xff, 0xc0, 0x0, 0x1,
    0xff, 0xf0, 0x0, 0x0, 0x7f, 0xfc, 0x0, 0x0,
    0xf, 0xff, 0x80, 0x0, 0x3, 0xff, 0xe0, 0x0,
    0x0, 0xff, 0xf8, 0x0, 0x0, 0x1f, 0xfe, 0x0,
    0x0, 0x7, 0xff, 0xc0, 0x0, 0x1, 0xff, 0xf0,
    0x0, 0x0, 0x3f, 0xfc, 0x0, 0x0, 0xf, 0xff,
    0x0, 0x0, 0x3, 0xff, 0xe0, 0x0, 0x0, 0xff,
    0xf8, 0x0, 0x0, 0x1f, 0xfe, 0x0, 0x0, 0x7,
    0xff, 0x80, 0x0, 0x1, 0xff, 0xe0, 0x0, 0x0,
    0x3f, 0xfc, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xfb, 0xff, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff,
    0xff, 0xef, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff,
    0xff, 0xff, 0xbf, 0xff, 0xff, 0xff, 0xf7, 0xff,
    0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xdf,
    0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xff,
    0x7f, 0xff, 0xff, 0xff, 0xe0,

    /* U+0033 "3" */
    0x0, 0x1f, 0xfc, 0x0, 0x0, 0x1f, 0xff, 0xf0,
    0x0, 0x1f, 0xff, 0xff, 0x80, 0x7, 0xff, 0xff,
    0xfc, 0x3, 0xff, 0xff, 0xff, 0xc0, 0xff, 0xff,
    0xff, 0xfc, 0x3f, 0xff, 0xff, 0xff, 0x83, 0xff,
    0xff, 0xff, 0xf8, 0x7f, 0xff, 0xff, 0xff, 0x87,
    0xff, 0xff, 0xff, 0xf0, 0x7f, 0xc0, 0xff, 0xfe,
    0xf, 0xc0, 0x7, 0xff, 0xe0, 0xf0, 0x0, 0x7f,
    0xfc, 0x8, 0x0, 0xf, 0xff, 0x80, 0x0, 0x0,
    0xff, 0xf0, 0x0, 0x0, 0x1f, 0xfe, 0x0, 0x0,
    0x3, 0xff, 0xc0, 0x0, 0x0, 0x7f, 0xf8, 0x0,
    0x0, 0xf, 0xff, 0x0, 0x0, 0x1, 0xff, 0xe0,
    0x0, 0x0, 0x3f, 0xf8, 0x0, 0x0, 0x7, 0xff,
    0x0, 0x0, 0x1, 0xff, 0xe0, 0x0, 0x0, 0x3f,
    0xf8, 0x0, 0x0, 0xf, 0xff, 0x0, 0x0, 0x3,
    0xff, 0xc0, 0x0, 0x0, 0xff, 0xf0, 0x0, 0x0,
    0xff, 0xfe, 0x0, 0xf, 0xff, 0xff, 0x0, 0x1,
    0xff, 0xff, 0xc0, 0x0, 0x3f, 0xff, 0xe0, 0x0,
    0x7, 0xff, 0xe0, 0x0, 0x0, 0xff, 0xff, 0x80,
    0x0, 0x1f, 0xff, 0xfe, 0x0, 0x3, 0xff, 0xff,
    0xe0, 0x0, 0x7f, 0xff, 0xff, 0x0, 0xf, 0xff,
    0xff, 0xf0, 0x0, 0x1, 0xff, 0xfe, 0x0, 0x0,
    0x7, 0xff, 0xe0, 0x0, 0x0, 0x7f, 0xfe, 0x0,
    0x0, 0x7, 0xff, 0xc0, 0x0, 0x0, 0x7f, 0xf8,
    0x0, 0x0, 0xf, 0xff, 0x80, 0x0, 0x0, 0xff,
    0xf0, 0x0, 0x0, 0x1f, 0xfe, 0x0, 0x0, 0x3,
    0xff, 0xc0, 0x0, 0x0, 0x7f, 0xf8, 0x0, 0x0,
    0xf, 0xff, 0x0, 0x0, 0x1, 0xff, 0xe0, 0x0,
    0x0, 0x3f, 0xfc, 0x0, 0x0, 0xf, 0xff, 0x80,
    0x0, 0x1, 0xff, 0xf0, 0x0, 0x0, 0x7f, 0xfd,
    0x80, 0x0, 0xf, 0xff, 0xbe, 0x0, 0x7, 0xff,
    0xf7, 0xf8, 0x3, 0xff, 0xfc, 0xff, 0xff, 0xff,
    0xff, 0x9f, 0xff, 0xff, 0xff, 0xe3, 0xff, 0xff,
    0xff, 0xf8, 0x7f, 0xff, 0xff, 0xff, 0xf, 0xff,
    0xff, 0xff, 0xc1, 0xff, 0xff, 0xff, 0xf0, 0x3f,
    0xff, 0xff, 0xf8, 0x3, 0xff, 0xff, 0xfe, 0x0,
    0xf, 0xff, 0xff, 0x0, 0x0, 0x1f, 0xfe, 0x0,
    0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x1f, 0xff, 0x0, 0x0, 0x0, 0x3f,
    0xfe, 0x0, 0x0, 0x0, 0xff, 0xfc, 0x0, 0x0,
    0x1, 0xff, 0xf8, 0x0, 0x0, 0x7, 0xff, 0xf0,
    0x0, 0x0, 0xf, 0xff, 0xe0, 0x0, 0x0, 0x3f,
    0xff, 0xc0, 0x0, 0x0, 0x7f, 0xff, 0x80, 0x0,
    0x1, 0xff, 0xff, 0x0, 0x0, 0x3, 0xff, 0xfe,
    0x0, 0x0, 0xf, 0xff, 0xfc, 0x0, 0x0, 0x1f,
    0xff, 0xf8, 0x0, 0x0, 0x3f, 0xff, 0xf0, 0x0,
    0x0, 0xff, 0xff, 0xe0, 0x0, 0x1, 0xff, 0xff,
    0xc0, 0x0, 0x7, 0xff, 0xff, 0x80, 0x0, 0xf,
    0xf7, 0xff, 0x0, 0x0, 0x3f, 0xef, 0xfe, 0x0,
    0x0, 0x7f, 0xdf, 0xfc, 0x0, 0x1, 0xff, 0x3f,
    0xf8, 0x0, 0x3, 0xfe, 0x7f, 0xf0, 0x0, 0xf,
    0xfc, 0xff, 0xe0, 0x0, 0x1f, 0xf1, 0xff, 0xc0,
    0x0, 0x7f, 0xe3, 0xff, 0x80, 0x0, 0xff, 0x87,
    0xff, 0x0, 0x3, 0xff, 0xf, 0xfe, 0x0, 0x7,
    0xfe, 0x1f, 0xfc, 0x0, 0x1f, 0xf8, 0x3f, 0xf8,
    0x0, 0x3f, 0xf0, 0x7f, 0xf0, 0x0, 0xff, 0xc0,
    0xff, 0xe0, 0x1, 0xff, 0x81, 0xff, 0xc0, 0x3,
    0xfe, 0x3, 0xff, 0x80, 0xf, 0xfc, 0x7, 0xff,
    0x0, 0x1f, 0xf0, 0xf, 0xfe, 0x0, 0x7f, 0xe0,
    0x1f, 0xfc, 0x0, 0xff, 0xc0, 0x3f, 0xf8, 0x3,
    0xff, 0x0, 0x7f, 0xf0, 0x7, 0xfe, 0x0, 0xff,
    0xe0, 0x1f, 0xf8, 0x1, 0xff, 0xc0, 0x3f, 0xf0,
    0x3, 0xff, 0x80, 0xff, 0xc0, 0x7, 0xff, 0x1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x3f, 0xf8, 0x0, 0x0, 0x0,
    0x7f, 0xf0, 0x0, 0x0, 0x0, 0xff, 0xe0, 0x0,
    0x0, 0x1, 0xff, 0xc0, 0x0, 0x0, 0x3, 0xff,
    0x80, 0x0, 0x0, 0x7, 0xff, 0x0, 0x0, 0x0,
    0xf, 0xfe, 0x0, 0x0, 0x0, 0x1f, 0xfc, 0x0,
    0x0, 0x0, 0x3f, 0xf8, 0x0, 0x0, 0x0, 0x7f,
    0xf0, 0x0, 0x0, 0x0, 0xff, 0xe0, 0x0, 0x0,
    0x1, 0xff, 0xc0, 0x0, 0x0, 0x3, 0xff, 0x80,
    0x0, 0x0, 0x7, 0xff, 0x0,

    /* U+0035 "5" */
    0x1f, 0xff, 0xff, 0xfe, 0x7, 0xff, 0xff, 0xff,
    0x81, 0xff, 0xff, 0xff, 0xe0, 0x7f, 0xff, 0xff,
    0xf8, 0x1f, 0xff, 0xff, 0xfe, 0x7, 0xff, 0xff,
    0xff, 0x81, 0xff, 0xff, 0xff, 0xe0, 0x7f, 0xff,
    0xff, 0xf8, 0x3f, 0xff, 0xff, 0xfe, 0xf, 0xff,
    0xff, 0xff, 0x83, 0xff, 0x80, 0x0, 0x0, 0xff,
    0xe0, 0x0, 0x0, 0x3f, 0xf8, 0x0, 0x0, 0xf,
    0xfe, 0x0, 0x0, 0x3, 0xff, 0x80, 0x0, 0x0,
    0xff, 0xe0, 0x0, 0x0, 0x3f, 0xf8, 0x0, 0x0,
    0xf, 0xfe, 0x0, 0x0, 0x3, 0xff, 0x80, 0x0,
    0x0, 0xff, 0xe0, 0x0, 0x0, 0x3f, 0xf8, 0x0,
    0x0, 0xf, 0xfe, 0x0, 0x0, 0x3, 0xff, 0x80,
    0x0, 0x0, 0xff, 0xe0, 0x0, 0x0, 0x7f, 0xff,
    0xfe, 0x0, 0x1f, 0xff, 0xff, 0xe0, 0x7, 0xff,
    0xff, 0xfe, 0x1, 0xff, 0xff, 0xff, 0xc0, 0x7f,
    0xff, 0xff, 0xf8, 0x1f, 0xff, 0xff, 0xff, 0x7,
    0xff, 0xff, 0xff, 0xe1, 0xff, 0xff, 0xff, 0xf8,
    0x7f, 0xff, 0xff, 0xff, 0xf, 0xff, 0xff, 0xff,
    0xc0, 0xf0, 0xf, 0xff, 0xf8, 0x0, 0x0, 0xff,
    0xfe, 0x0, 0x0, 0xf, 0xff, 0x80, 0x0, 0x3,
    0xff, 0xf0, 0x0, 0x0, 0x7f, 0xfc, 0x0, 0x0,
    0x1f, 0xff, 0x0, 0x0, 0x3, 0xff, 0xc0, 0x0,
    0x0, 0xff, 0xf0, 0x0, 0x0, 0x3f, 0xfc, 0x0,
    0x0, 0xf, 0xff, 0x0, 0x0, 0x3, 0xff, 0xc0,
    0x0, 0x0, 0xff, 0xf0, 0x0, 0x0, 0x3f, 0xfc,
    0x0, 0x0, 0xf, 0xff, 0x0, 0x0, 0x7, 0xff,
    0xc0, 0x0, 0x1, 0xff, 0xe0, 0x0, 0x0, 0x7f,
    0xfa, 0x0, 0x0, 0x3f, 0xfe, 0xe0, 0x0, 0x1f,
    0xff, 0xbe, 0x0, 0xf, 0xff, 0xcf, 0xf0, 0xf,
    0xff, 0xf3, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xfe, 0x3f, 0xff, 0xff, 0xff, 0xf, 0xff,
    0xff, 0xff, 0x83, 0xff, 0xff, 0xff, 0xc0, 0xff,
    0xff, 0xff, 0xe0, 0x3f, 0xff, 0xff, 0xf0, 0x7,
    0xff, 0xff, 0xf0, 0x0, 0x7f, 0xff, 0xf0, 0x0,
    0x0, 0xff, 0xe0, 0x0, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x1f, 0xfc, 0x0, 0x0, 0x1f, 0xff,
    0xc0, 0x0, 0x1f, 0xff, 0xf8, 0x0, 0x7, 0xff,
    0xff, 0x0, 0x3, 0xff, 0xff, 0xe0, 0x0, 0xff,
    0xff, 0xfc, 0x0, 0x3f, 0xff, 0xff, 0x80, 0xf,
    0xff, 0xff, 0xf0, 0x3, 0xff, 0xff, 0xfe, 0x0,
    0x7f, 0xff, 0xff, 0xc0, 0x1f, 0xff, 0xe0, 0x8,
    0x3, 0xff, 0xf0, 0x0, 0x0, 0xff, 0xf8, 0x0,
    0x0, 0x1f, 0xfe, 0x0, 0x0, 0x7, 0xff, 0x80,
    0x0, 0x0, 0xff, 0xe0, 0x0, 0x0, 0x1f, 0xfc,
    0x0, 0x0, 0x7, 0xff, 0x0, 0x0, 0x0, 0xff,
    0xe0, 0x0, 0x0, 0x1f, 0xf8, 0x0, 0x0, 0x3,
    0xff, 0x0, 0x0, 0x0, 0xff, 0xe0, 0x0, 0x0,
    0x1f, 0xf8, 0x0, 0x0, 0x3, 0xff, 0x1, 0xfc,
    0x0, 0x7f, 0xe0, 0xff, 0xf0, 0xf, 0xfc, 0x3f,
    0xff, 0x1, 0xff, 0x8f, 0xff, 0xf0, 0x7f, 0xf3,
    0xff, 0xff, 0xf, 0xfe, 0xff, 0xff, 0xf1, 0xff,
    0xdf, 0xff, 0xfe, 0x3f, 0xff, 0xff, 0xff, 0xe7,
    0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff,
    0x9f, 0xff, 0xe1, 0xff, 0xfb, 0xff, 0xf0, 0x1f,
    0xff, 0x7f, 0xfe, 0x3, 0xff, 0xef, 0xff, 0x80,
    0x3f, 0xfd, 0xff, 0xf0, 0x7, 0xff, 0xff, 0xfc,
    0x0, 0xff, 0xff, 0xff, 0x80, 0xf, 0xff, 0xff,
    0xf0, 0x1, 0xff, 0xff, 0xfe, 0x0, 0x3f, 0xff,
    0xff, 0xc0, 0x7, 0xff, 0xff, 0xf8, 0x0, 0xff,
    0xff, 0xff, 0x0, 0x1f, 0xff, 0xff, 0xe0, 0x3,
    0xff, 0xff, 0xfc, 0x0, 0x7f, 0xfb, 0xff, 0x80,
    0xf, 0xff, 0x7f, 0xf0, 0x1, 0xff, 0xef, 0xff,
    0x0, 0x3f, 0xfd, 0xff, 0xe0, 0xf, 0xff, 0x3f,
    0xfc, 0x1, 0xff, 0xe3, 0xff, 0x80, 0x3f, 0xfc,
    0x7f, 0xf8, 0xf, 0xff, 0x8f, 0xff, 0x81, 0xff,
    0xe0, 0xff, 0xf8, 0xff, 0xfc, 0x1f, 0xff, 0xff,
    0xff, 0x1, 0xff, 0xff, 0xff, 0xe0, 0x3f, 0xff,
    0xff, 0xf8, 0x3, 0xff, 0xff, 0xff, 0x0, 0x3f,
    0xff, 0xff, 0xc0, 0x7, 0xff, 0xff, 0xf0, 0x0,
    0x7f, 0xff, 0xfc, 0x0, 0x3, 0xff, 0xfe, 0x0,
    0x0, 0x3f, 0xff, 0x80, 0x0, 0x0, 0xff, 0x80,
    0x0,

    /* U+0037 "7" */
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x0, 0x0, 0x3f, 0xf8, 0x0,
    0x0, 0xf, 0xfe, 0x0, 0x0, 0x7, 0xff, 0x0,
    0x0, 0x1, 0xff, 0xc0, 0x0, 0x0, 0x7f, 0xf0,
    0x0, 0x0, 0x3f, 0xfc, 0x0, 0x0, 0xf, 0xfe,
    0x0, 0x0, 0x3, 0xff, 0x80, 0x0, 0x1, 0xff,
    0xe0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0, 0x1f,
    0xfc, 0x0, 0x0, 0xf, 0xff, 0x0, 0x0, 0x3,
    0xff, 0x80, 0x0, 0x0, 0xff, 0xe0, 0x0, 0x0,
    0x7f, 0xf8, 0x0, 0x0, 0x1f, 0xfc, 0x0, 0x0,
    0x7, 0xff, 0x0, 0x0, 0x3, 0xff, 0xc0, 0x0,
    0x0, 0xff, 0xe0, 0x0, 0x0, 0x3f, 0xf8, 0x0,
    0x0, 0x1f, 0xfe, 0x0, 0x0, 0x7, 0xff, 0x80,
    0x0, 0x1, 0xff, 0xc0, 0x0, 0x0, 0xff, 0xf0,
    0x0, 0x0, 0x3f, 0xfc, 0x0, 0x0, 0xf, 0xfe,
    0x0, 0x0, 0x7, 0xff, 0x80, 0x0, 0x1, 0xff,
    0xe0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0, 0x3f,
    0xfc, 0x0, 0x0, 0xf, 0xff, 0x0, 0x0, 0x3,
    0xff, 0x80, 0x0, 0x1, 0xff, 0xe0, 0x0, 0x0,
    0x7f, 0xf8, 0x0, 0x0, 0x1f, 0xfc, 0x0, 0x0,
    0xf, 0xff, 0x0, 0x0, 0x3, 0xff, 0xc0, 0x0,
    0x0, 0xff, 0xf0, 0x0, 0x0, 0x7f, 0xf8, 0x0,
    0x0, 0x1f, 0xfe, 0x0, 0x0, 0x7, 0xff, 0x80,
    0x0, 0x3, 0xff, 0xc0, 0x0, 0x0, 0xff, 0xf0,
    0x0, 0x0, 0x3f, 0xfc, 0x0, 0x0, 0x1f, 0xfe,
    0x0, 0x0, 0x7, 0xff, 0x80, 0x0, 0x1, 0xff,
    0xe0, 0x0, 0x0, 0xff, 0xf0, 0x0, 0x0, 0x3f,
    0xfc, 0x0, 0x0, 0xf, 0xff, 0x0, 0x0, 0x7,
    0xff, 0x80, 0x0, 0x1, 0xff, 0xe0, 0x0, 0x0,
    0x7f, 0xf8, 0x0, 0x0, 0x3f, 0xfe, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x7, 0xfc, 0x0, 0x0, 0x3, 0xff, 0xf8,
    0x0, 0x0, 0xff, 0xff, 0xe0, 0x0, 0x1f, 0xff,
    0xff, 0x80, 0x7, 0xff, 0xff, 0xfc, 0x0, 0x7f,
    0xff, 0xff, 0xe0, 0xf, 0xff, 0xff, 0xfe, 0x1,
    0xff, 0xff, 0xff, 0xf0, 0x1f, 0xff, 0xff, 0xff,
    0x83, 0xff, 0xff, 0xff, 0xf8, 0x3f, 0xfe, 0xf,
    0xff, 0x87, 0xff, 0xc0, 0x7f, 0xfc, 0x7f, 0xfc,
    0x7, 0xff, 0xc7, 0xff, 0x80, 0x3f, 0xfc, 0x7f,
    0xf8, 0x3, 0xff, 0xc7, 0xff, 0x80, 0x3f, 0xfc,
    0x7f, 0xf8, 0x3, 0xff, 0xc7, 0xff, 0x80, 0x3f,
    0xfc, 0x7f, 0xf8, 0x3, 0xff, 0xc7, 0xff, 0x80,
    0x3f, 0xfc, 0x3f, 0xf8, 0x7, 0xff, 0x83, 0xff,
    0xc0, 0x7f, 0xf8, 0x3f, 0xfc, 0x7, 0xff, 0x81,
    0xff, 0xe0, 0xff, 0xf0, 0x1f, 0xff, 0x1f, 0xff,
    0x0, 0xff, 0xfb, 0xff, 0xe0, 0x7, 0xff, 0xff,
    0xfc, 0x0, 0x7f, 0xff, 0xff, 0x80, 0x3, 0xff,
    0xff, 0xf0, 0x0, 0x1f, 0xff, 0xfe, 0x0, 0x0,
    0x7f, 0xff, 0xc0, 0x0, 0x3, 0xff, 0xf8, 0x0,
    0x0, 0x7f, 0xff, 0xe0, 0x0, 0xf, 0xff, 0xff,
    0x0, 0x3, 0xff, 0xff, 0xf8, 0x0, 0x7f, 0xff,
    0xff, 0xc0, 0xf, 0xff, 0xff, 0xfe, 0x0, 0xff,
    0xf3, 0xff, 0xf0, 0x1f, 0xfe, 0x1f, 0xff, 0x83,
    0xff, 0xc0, 0xff, 0xfc, 0x3f, 0xfc, 0x7, 0xff,
    0xc7, 0xff, 0x80, 0x3f, 0xfe, 0x7f, 0xf8, 0x1,
    0xff, 0xe7, 0xff, 0x0, 0x1f, 0xfe, 0xff, 0xf0,
    0x1, 0xff, 0xff, 0xff, 0x0, 0xf, 0xff, 0xff,
    0xf0, 0x0, 0xff, 0xff, 0xff, 0x0, 0xf, 0xff,
    0xff, 0xf0, 0x0, 0xff, 0xff, 0xff, 0x0, 0xf,
    0xff, 0xff, 0xf0, 0x0, 0xff, 0xff, 0xff, 0x0,
    0xf, 0xff, 0xff, 0xf8, 0x1, 0xff, 0xff, 0xff,
    0x80, 0x1f, 0xfe, 0x7f, 0xfc, 0x3, 0xff, 0xe7,
    0xff, 0xf0, 0xff, 0xfe, 0x7f, 0xff, 0xff, 0xff,
    0xc3, 0xff, 0xff, 0xff, 0xfc, 0x3f, 0xff, 0xff,
    0xff, 0x81, 0xff, 0xff, 0xff, 0xf8, 0xf, 0xff,
    0xff, 0xff, 0x0, 0x7f, 0xff, 0xff, 0xe0, 0x3,
    0xff, 0xff, 0xfc, 0x0, 0x1f, 0xff, 0xff, 0x0,
    0x0, 0x7f, 0xff, 0xc0, 0x0, 0x0, 0x7f, 0xe0,
    0x0,

    /* U+0039 "9" */
    0x0, 0x7, 0xf8, 0x0, 0x0, 0x7, 0xff, 0xe0,
    0x0, 0x1, 0xff, 0xff, 0x0, 0x0, 0xff, 0xff,
    0xf0, 0x0, 0x3f, 0xff, 0xff, 0x0, 0xf, 0xff,
    0xff, 0xf0, 0x3, 0xff, 0xff, 0xff, 0x0, 0x7f,
    0xff, 0xff, 0xe0, 0x1f, 0xff, 0xff, 0xfe, 0x3,
    0xff, 0xff, 0xff, 0xe0, 0xff, 0xfc, 0x7f, 0xfc,
    0x1f, 0xfe, 0x7, 0xff, 0x87, 0xff, 0xc0, 0x7f,
    0xf8, 0xff, 0xf0, 0x7, 0xff, 0x1f, 0xfe, 0x0,
    0xff, 0xe3, 0xff, 0xc0, 0x1f, 0xfe, 0xff, 0xf0,
    0x3, 0xff, 0xdf, 0xfe, 0x0, 0x3f, 0xfb, 0xff,
    0xc0, 0x7, 0xff, 0x7f, 0xf8, 0x0, 0xff, 0xef,
    0xff, 0x0, 0x1f, 0xff, 0xff, 0xe0, 0x3, 0xff,
    0xff, 0xfc, 0x0, 0x7f, 0xff, 0xff, 0x80, 0xf,
    0xff, 0xff, 0xf0, 0x1, 0xff, 0xff, 0xfe, 0x0,
    0x3f, 0xff, 0xff, 0xc0, 0x7, 0xff, 0xff, 0xf8,
    0x0, 0xff, 0xff, 0xff, 0x80, 0x3f, 0xfe, 0xff,
    0xf0, 0x7, 0xff, 0xdf, 0xfe, 0x1, 0xff, 0xfb,
    0xff, 0xe0, 0x7f, 0xff, 0x7f, 0xfe, 0x1f, 0xff,
    0xe7, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff,
    0xff, 0x9f, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff,
    0xef, 0xfe, 0x3f, 0xff, 0xfd, 0xff, 0xc3, 0xff,
    0xff, 0x3f, 0xf8, 0x3f, 0xff, 0xc7, 0xfe, 0x3,
    0xff, 0xf0, 0xff, 0xc0, 0x1f, 0xfc, 0x1f, 0xf8,
    0x0, 0xfe, 0x7, 0xff, 0x0, 0x0, 0x0, 0xff,
    0xe0, 0x0, 0x0, 0x1f, 0xfc, 0x0, 0x0, 0x3,
    0xff, 0x0, 0x0, 0x0, 0xff, 0xe0, 0x0, 0x0,
    0x1f, 0xfc, 0x0, 0x0, 0x3, 0xff, 0x80, 0x0,
    0x0, 0xff, 0xe0, 0x0, 0x0, 0x1f, 0xfc, 0x0,
    0x0, 0x7, 0xff, 0x80, 0x0, 0x1, 0xff, 0xe0,
    0x0, 0x0, 0x7f, 0xfc, 0x0, 0x0, 0x3f, 0xff,
    0x0, 0x40, 0x3f, 0xff, 0xe0, 0xf, 0xff, 0xff,
    0xf8, 0x1, 0xff, 0xff, 0xfe, 0x0, 0x3f, 0xff,
    0xff, 0xc0, 0x7, 0xff, 0xff, 0xf0, 0x0, 0xff,
    0xff, 0xfc, 0x0, 0x1f, 0xff, 0xff, 0x0, 0x3,
    0xff, 0xff, 0x80, 0x0, 0x7f, 0xff, 0xc0, 0x0,
    0xf, 0xff, 0xe0, 0x0, 0x0, 0xff, 0xe0, 0x0,
    0x0,

    /* U+003A ":" */
    0xf, 0xe0, 0x7f, 0xf1, 0xff, 0xf3, 0xff, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0xff, 0xf3, 0xff, 0xe3, 0xff,
    0x81, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfc, 0xf,
    0xfe, 0x3f, 0xfe, 0x7f, 0xfd, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xbf, 0xfe, 0x7f, 0xfc, 0x7f, 0xf0, 0x3f, 0x80,

    /* U+0041 "A" */
    0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x1, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x1, 0xff, 0xff, 0x80, 0x0, 0x0, 0x1,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xc0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x3,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xe0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x7, 0xff, 0x7f, 0xe0, 0x0, 0x0, 0x7,
    0xfe, 0x7f, 0xe0, 0x0, 0x0, 0xf, 0xfe, 0x7f,
    0xf0, 0x0, 0x0, 0xf, 0xfe, 0x7f, 0xf0, 0x0,
    0x0, 0xf, 0xfe, 0x7f, 0xf0, 0x0, 0x0, 0xf,
    0xfe, 0x7f, 0xf0, 0x0, 0x0, 0x1f, 0xfc, 0x3f,
    0xf8, 0x0, 0x0, 0x1f, 0xfc, 0x3f, 0xf8, 0x0,
    0x0, 0x1f, 0xfc, 0x3f, 0xf8, 0x0, 0x0, 0x1f,
    0xfc, 0x3f, 0xf8, 0x0, 0x0, 0x3f, 0xfc, 0x3f,
    0xfc, 0x0, 0x0, 0x3f, 0xf8, 0x1f, 0xfc, 0x0,
    0x0, 0x3f, 0xf8, 0x1f, 0xfc, 0x0, 0x0, 0x3f,
    0xf8, 0x1f, 0xfc, 0x0, 0x0, 0x7f, 0xf8, 0x1f,
    0xfe, 0x0, 0x0, 0x7f, 0xf8, 0xf, 0xfe, 0x0,
    0x0, 0x7f, 0xf0, 0xf, 0xfe, 0x0, 0x0, 0x7f,
    0xf0, 0xf, 0xfe, 0x0, 0x0, 0xff, 0xf0, 0xf,
    0xff, 0x0, 0x0, 0xff, 0xf0, 0xf, 0xff, 0x0,
    0x0, 0xff, 0xe0, 0x7, 0xff, 0x0, 0x0, 0xff,
    0xe0, 0x7, 0xff, 0x0, 0x0, 0xff, 0xe0, 0x7,
    0xff, 0x80, 0x1, 0xff, 0xe0, 0x7, 0xff, 0x80,
    0x1, 0xff, 0xe0, 0x3, 0xff, 0x80, 0x1, 0xff,
    0xc0, 0x3, 0xff, 0x80, 0x1, 0xff, 0xc0, 0x3,
    0xff, 0xc0, 0x3, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x3, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x3, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xe0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x7, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x7, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xf, 0xff,
    0x0, 0x0, 0x7f, 0xf0, 0xf, 0xfe, 0x0, 0x0,
    0x7f, 0xf0, 0x1f, 0xfe, 0x0, 0x0, 0x7f, 0xf8,
    0x1f, 0xfe, 0x0, 0x0, 0x7f, 0xf8, 0x1f, 0xfe,
    0x0, 0x0, 0x7f, 0xf8, 0x1f, 0xfc, 0x0, 0x0,
    0x3f, 0xf8, 0x3f, 0xfc, 0x0, 0x0, 0x3f, 0xfc,
    0x3f, 0xfc, 0x0, 0x0, 0x3f, 0xfc, 0x3f, 0xfc,
    0x0, 0x0, 0x3f, 0xfc, 0x3f, 0xf8, 0x0, 0x0,
    0x1f, 0xfc, 0x7f, 0xf8, 0x0, 0x0, 0x1f, 0xfe,
    0x7f, 0xf8, 0x0, 0x0, 0x1f, 0xfe, 0x7f, 0xf8,
    0x0, 0x0, 0x1f, 0xfe, 0x7f, 0xf8, 0x0, 0x0,
    0xf, 0xfe, 0xff, 0xf0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0xf, 0xff,

    /* U+004D "M" */
    0xff, 0xff, 0xe0, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x3,
    0xff, 0x7f, 0xff, 0xff, 0xff, 0x80, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xef, 0xf8, 0x0, 0x3, 0xff,
    0xff, 0xff, 0xfe, 0xff, 0xc0, 0x0, 0x3f, 0xef,
    0xff, 0xff, 0xef, 0xfc, 0x0, 0x3, 0xfe, 0xff,
    0xff, 0xfe, 0xff, 0xc0, 0x0, 0x7f, 0xef, 0xff,
    0xff, 0xe7, 0xfc, 0x0, 0x7, 0xfe, 0xff, 0xff,
    0xfe, 0x7f, 0xc0, 0x0, 0x7f, 0xef, 0xff, 0xff,
    0xe7, 0xfe, 0x0, 0x7, 0xfc, 0xff, 0xff, 0xfe,
    0x7f, 0xe0, 0x0, 0x7f, 0xcf, 0xff, 0xff, 0xe7,
    0xfe, 0x0, 0xf, 0xfc, 0xff, 0xff, 0xfe, 0x3f,
    0xe0, 0x0, 0xff, 0xcf, 0xff, 0xff, 0xe3, 0xff,
    0x0, 0xf, 0xf8, 0xff, 0xff, 0xfe, 0x3f, 0xf0,
    0x0, 0xff, 0x8f, 0xff, 0xff, 0xe3, 0xff, 0x0,
    0x1f, 0xf8, 0xff, 0xff, 0xfe, 0x1f, 0xf0, 0x1,
    0xff, 0x8f, 0xff, 0xff, 0xe1, 0xff, 0x0, 0x1f,
    0xf8, 0xff, 0xff, 0xfe, 0x1f, 0xf8, 0x1, 0xff,
    0xf, 0xff, 0xff, 0xe1, 0xff, 0x80, 0x1f, 0xf0,
    0xff, 0xff, 0xfe, 0x1f, 0xf8, 0x3, 0xff, 0xf,
    0xff, 0xff, 0xe0, 0xff, 0x80, 0x3f, 0xf0, 0xff,
    0xff, 0xfe, 0xf, 0xfc, 0x3, 0xfe, 0xf, 0xff,
    0xff, 0xe0, 0xff, 0xc0, 0x3f, 0xe0, 0xff, 0xff,
    0xfe, 0xf, 0xfc, 0x3, 0xfe, 0xf, 0xff, 0xff,
    0xe0, 0x7f, 0xc0, 0x7f, 0xe0, 0xff, 0xff, 0xfe,
    0x7, 0xfc, 0x7, 0xfe, 0xf, 0xff, 0xff, 0xe0,
    0x7f, 0xe0, 0x7f, 0xc0, 0xff, 0xff, 0xfe, 0x7,
    0xfe, 0x7, 0xfc, 0xf, 0xff, 0xff, 0xe0, 0x7f,
    0xe0, 0xff, 0xc0, 0xff, 0xff, 0xfe, 0x3, 0xfe,
    0xf, 0xfc, 0xf, 0xff, 0xff, 0xe0, 0x3f, 0xe0,
    0xff, 0x80, 0xff, 0xff, 0xfe, 0x3, 0xff, 0xf,
    0xf8, 0xf, 0xff, 0xff, 0xe0, 0x3f, 0xf0, 0xff,
    0x80, 0xff, 0xff, 0xfe, 0x1, 0xff, 0x1f, 0xf8,
    0xf, 0xff, 0xff, 0xe0, 0x1f, 0xf1, 0xff, 0x80,
    0xff, 0xff, 0xfe, 0x1, 0xff, 0x9f, 0xf0, 0xf,
    0xff, 0xff, 0xe0, 0x1f, 0xf9, 0xff, 0x0, 0xff,
    0xff, 0xfe, 0x0, 0xff, 0x9f, 0xf0, 0xf, 0xff,
    0xff, 0xe0, 0xf, 0xfb, 0xff, 0x0, 0xff, 0xff,
    0xfe, 0x0, 0xff, 0xbf, 0xe0, 0xf, 0xff, 0xff,
    0xe0, 0xf, 0xff, 0xfe, 0x0, 0xff, 0xff, 0xfe,
    0x0, 0xff, 0xff, 0xe0, 0xf, 0xff, 0xff, 0xe0,
    0x7, 0xff, 0xfe, 0x0, 0xff, 0xff, 0xfe, 0x0,
    0x7f, 0xff, 0xe0, 0xf, 0xff, 0xff, 0xe0, 0x7,
    0xff, 0xfc, 0x0, 0xff, 0xff, 0xfe, 0x0, 0x7f,
    0xff, 0xc0, 0xf, 0xff, 0xff, 0xe0, 0x3, 0xff,
    0xfc, 0x0, 0xff, 0xff, 0xfe, 0x0, 0x3f, 0xff,
    0xc0, 0xf, 0xff, 0xff, 0xe0, 0x3, 0xff, 0xf8,
    0x0, 0xff, 0xff, 0xfe, 0x0, 0x3f, 0xff, 0x80,
    0xf, 0xff, 0xff, 0xe0, 0x3, 0xff, 0xf8, 0x0,
    0xff, 0xff, 0xfe, 0x0, 0x1f, 0xff, 0x80, 0xf,
    0xff, 0xff, 0xe0, 0x1, 0xff, 0xf8, 0x0, 0xff,
    0xff, 0xfe, 0x0, 0x1f, 0xff, 0x0, 0xf, 0xff,

    /* U+0050 "P" */
    0xff, 0xff, 0xfc, 0x0, 0x1f, 0xff, 0xff, 0xf0,
    0x3, 0xff, 0xff, 0xff, 0x80, 0x7f, 0xff, 0xff,
    0xfc, 0xf, 0xff, 0xff, 0xff, 0xc1, 0xff, 0xff,
    0xff, 0xfc, 0x3f, 0xff, 0xff, 0xff, 0xc7, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0x9f,
    0xff, 0xff, 0xff, 0xf3, 0xff, 0xc0, 0xff, 0xff,
    0x7f, 0xf8, 0x7, 0xff, 0xef, 0xff, 0x0, 0x7f,
    0xfd, 0xff, 0xe0, 0x7, 0xff, 0xbf, 0xfc, 0x0,
    0xff, 0xff, 0xff, 0x80, 0x1f, 0xff, 0xff, 0xf0,
    0x1, 0xff, 0xff, 0xfe, 0x0, 0x3f, 0xff, 0xff,
    0xc0, 0x7, 0xff, 0xff, 0xf8, 0x0, 0xff, 0xff,
    0xff, 0x0, 0x1f, 0xff, 0xff, 0xe0, 0x3, 0xff,
    0xff, 0xfc, 0x0, 0x7f, 0xff, 0xff, 0x80, 0xf,
    0xff, 0xff, 0xf0, 0x1, 0xff, 0xff, 0xfe, 0x0,
    0x7f, 0xff, 0xff, 0xc0, 0xf, 0xff, 0x7f, 0xf8,
    0x1, 0xff, 0xef, 0xff, 0x0, 0x7f, 0xfd, 0xff,
    0xe0, 0x1f, 0xff, 0xbf, 0xfc, 0xf, 0xff, 0xe7,
    0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff,
    0x1f, 0xff, 0xff, 0xff, 0xc3, 0xff, 0xff, 0xff,
    0xf8, 0x7f, 0xff, 0xff, 0xfe, 0xf, 0xff, 0xff,
    0xff, 0x81, 0xff, 0xff, 0xff, 0xe0, 0x3f, 0xff,
    0xff, 0xf0, 0x7, 0xff, 0xff, 0xf8, 0x0, 0xff,
    0xff, 0xf0, 0x0, 0x1f, 0xfe, 0x0, 0x0, 0x3,
    0xff, 0xc0, 0x0, 0x0, 0x7f, 0xf8, 0x0, 0x0,
    0xf, 0xff, 0x0, 0x0, 0x1, 0xff, 0xe0, 0x0,
    0x0, 0x3f, 0xfc, 0x0, 0x0, 0x7, 0xff, 0x80,
    0x0, 0x0, 0xff, 0xf0, 0x0, 0x0, 0x1f, 0xfe,
    0x0, 0x0, 0x3, 0xff, 0xc0, 0x0, 0x0, 0x7f,
    0xf8, 0x0, 0x0, 0xf, 0xff, 0x0, 0x0, 0x1,
    0xff, 0xe0, 0x0, 0x0, 0x3f, 0xfc, 0x0, 0x0,
    0x7, 0xff, 0x80, 0x0, 0x0, 0xff, 0xf0, 0x0,
    0x0, 0x1f, 0xfe, 0x0, 0x0, 0x3, 0xff, 0xc0,
    0x0, 0x0, 0x7f, 0xf8, 0x0, 0x0, 0xf, 0xff,
    0x0, 0x0, 0x1, 0xff, 0xe0, 0x0, 0x0, 0x3f,
    0xfc, 0x0, 0x0, 0x7, 0xff, 0x80, 0x0, 0x0,
    0xff, 0xf0, 0x0, 0x0, 0x0,

    /* U+0061 "a" */
    0x0, 0x3, 0xff, 0x0, 0x0, 0x7, 0xff, 0xfc,
    0x0, 0x3, 0xff, 0xff, 0xe0, 0x1, 0xff, 0xff,
    0xfe, 0x0, 0xff, 0xff, 0xff, 0xe0, 0x1f, 0xff,
    0xff, 0xfe, 0x3, 0xff, 0xff, 0xff, 0xc0, 0x3f,
    0xff, 0xff, 0xfc, 0x7, 0xff, 0xff, 0xff, 0x80,
    0x7f, 0xff, 0xff, 0xf8, 0xf, 0xf0, 0x3f, 0xff,
    0x1, 0xf0, 0x3, 0xff, 0xe0, 0x18, 0x0, 0x3f,
    0xfe, 0x0, 0x0, 0x3, 0xff, 0xc0, 0x0, 0x0,
    0x7f, 0xf8, 0x0, 0x0, 0xf, 0xff, 0x0, 0x0,
    0x1, 0xff, 0xe0, 0x0, 0x0, 0x3f, 0xfc, 0x0,
    0x0, 0x7, 0xff, 0x80, 0x0, 0x0, 0xff, 0xf0,
    0x1, 0xff, 0xff, 0xfe, 0x1, 0xff, 0xff, 0xff,
    0xc0, 0xff, 0xff, 0xff, 0xf8, 0x3f, 0xff, 0xff,
    0xff, 0x1f, 0xff, 0xff, 0xff, 0xe3, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xbf, 0xff,
    0x80, 0xff, 0xf7, 0xff, 0xe0, 0x1f, 0xfe, 0xff,
    0xf8, 0x3, 0xff, 0xff, 0xfe, 0x0, 0x7f, 0xff,
    0xff, 0xc0, 0xf, 0xff, 0xff, 0xf0, 0x1, 0xff,
    0xff, 0xfe, 0x0, 0x3f, 0xff, 0xff, 0xc0, 0x7,
    0xff, 0xff, 0xf8, 0x0, 0xff, 0xff, 0xff, 0x0,
    0x3f, 0xff, 0xff, 0xe0, 0x7, 0xff, 0xff, 0xfc,
    0x1, 0xff, 0xff, 0xff, 0xc0, 0x3f, 0xff, 0xff,
    0xf8, 0xf, 0xff, 0xef, 0xff, 0x87, 0xff, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xbf, 0xff, 0xff, 0xff,
    0xf3, 0xff, 0xff, 0xef, 0xfe, 0x7f, 0xff, 0xf8,
    0xff, 0xc7, 0xff, 0xff, 0x1f, 0xf8, 0x7f, 0xff,
    0xc3, 0xff, 0xf, 0xff, 0xf0, 0x3f, 0xe0, 0x7f,
    0xf8, 0x7, 0xfc, 0x3, 0xfc, 0x0, 0x0, 0x0,

    /* U+006D "m" */
    0x0, 0x0, 0x1f, 0xe0, 0x0, 0x1f, 0xe0, 0xf,
    0xfc, 0x7, 0xff, 0x80, 0x7, 0xff, 0x80, 0xff,
    0xc1, 0xff, 0xfe, 0x1, 0xff, 0xfe, 0xf, 0xfc,
    0x3f, 0xff, 0xf0, 0x3f, 0xff, 0xf0, 0xff, 0xc7,
    0xff, 0xff, 0x87, 0xff, 0xff, 0x8f, 0xfe, 0x7f,
    0xff, 0xf8, 0x7f, 0xff, 0xf8, 0xff, 0xef, 0xff,
    0xff, 0xcf, 0xff, 0xff, 0xcf, 0xff, 0xff, 0xff,
    0xfc, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xff, 0xff, 0x87, 0xff, 0xff,
    0x87, 0xff, 0xef, 0xff, 0xe0, 0x3f, 0xff, 0xe0,
    0x3f, 0xfe, 0xff, 0xfc, 0x1, 0xff, 0xfc, 0x1,
    0xff, 0xff, 0xff, 0xc0, 0x1f, 0xff, 0xc0, 0x1f,
    0xff, 0xff, 0xf8, 0x1, 0xff, 0xf8, 0x1, 0xff,
    0xff, 0xff, 0x80, 0xf, 0xff, 0x80, 0xf, 0xff,
    0xff, 0xf8, 0x0, 0xff, 0xf8, 0x0, 0xff, 0xff,
    0xff, 0x80, 0xf, 0xff, 0x80, 0xf, 0xff, 0xff,
    0xf0, 0x0, 0xff, 0xf0, 0x0, 0xff, 0xff, 0xff,
    0x0, 0xf, 0xff, 0x0, 0xf, 0xff, 0xff, 0xf0,
    0x0, 0xff, 0xf0, 0x0, 0xff, 0xff, 0xff, 0x0,
    0xf, 0xff, 0x0, 0xf, 0xff, 0xff, 0xf0, 0x0,
    0xff, 0xf0, 0x0, 0xff, 0xff, 0xff, 0x0, 0xf,
    0xff, 0x0, 0xf, 0xff, 0xff, 0xf0, 0x0, 0xff,
    0xf0, 0x0, 0xff, 0xff, 0xff, 0x0, 0xf, 0xff,
    0x0, 0xf, 0xff, 0xff, 0xf0, 0x0, 0xff, 0xf0,
    0x0, 0xff, 0xff, 0xff, 0x0, 0xf, 0xff, 0x0,
    0xf, 0xff, 0xff, 0xf0, 0x0, 0xff, 0xf0, 0x0,
    0xff, 0xff, 0xff, 0x0, 0xf, 0xff, 0x0, 0xf,
    0xff, 0xff, 0xf0, 0x0, 0xff, 0xf0, 0x0, 0xff,
    0xff, 0xff, 0x0, 0xf, 0xff, 0x0, 0xf, 0xff,
    0xff, 0xf0, 0x0, 0xff, 0xf0, 0x0, 0xff, 0xff,
    0xff, 0x0, 0xf, 0xff, 0x0, 0xf, 0xff, 0xff,
    0xf0, 0x0, 0xff, 0xf0, 0x0, 0xff, 0xff, 0xff,
    0x0, 0xf, 0xff, 0x0, 0xf, 0xff, 0xff, 0xf0,
    0x0, 0xff, 0xf0, 0x0, 0xff, 0xff, 0xff, 0x0,
    0xf, 0xff, 0x0, 0xf, 0xff, 0xff, 0xf0, 0x0,
    0xff, 0xf0, 0x0, 0xff, 0xff, 0xff, 0x0, 0xf,
    0xff, 0x0, 0xf, 0xff, 0xff, 0xf0, 0x0, 0xff,
    0xf0, 0x0, 0xff, 0xff, 0xff, 0x0, 0xf, 0xff,
    0x0, 0xf, 0xff, 0xff, 0xf0, 0x0, 0xff, 0xf0,
    0x0, 0xff, 0xff, 0xff, 0x0, 0xf, 0xff, 0x0,
    0xf, 0xff, 0xff, 0xf0, 0x0, 0xff, 0xf0, 0x0,
    0xff, 0xff, 0xff, 0x0, 0xf, 0xff, 0x0, 0xf,
    0xff, 0xff, 0xf0, 0x0, 0xff, 0xf0, 0x0, 0xff,
    0xff, 0xff, 0x0, 0xf, 0xff, 0x0, 0xf, 0xff,
    0xff, 0xf0, 0x0, 0xff, 0xf0, 0x0, 0xff, 0xff,
    0xff, 0x0, 0xf, 0xff, 0x0, 0xf, 0xff,

    /* U+0070 "p" */
    0x0, 0x0, 0x1f, 0xc0, 0xf, 0xfc, 0x7, 0xff,
    0x0, 0xff, 0xc0, 0xff, 0xf8, 0xf, 0xfc, 0x1f,
    0xff, 0xc0, 0xff, 0xe3, 0xff, 0xfe, 0xf, 0xfe,
    0x7f, 0xff, 0xf0, 0xff, 0xe7, 0xff, 0xff, 0xf,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xf,
    0xff, 0xcf, 0xff, 0xe0, 0x7f, 0xfc, 0xff, 0xfc,
    0x3, 0xff, 0xef, 0xff, 0xc0, 0x3f, 0xfe, 0xff,
    0xf8, 0x1, 0xff, 0xef, 0xff, 0x80, 0x1f, 0xfe,
    0xff, 0xf8, 0x1, 0xff, 0xef, 0xff, 0x80, 0x1f,
    0xff, 0xff, 0xf8, 0x0, 0xff, 0xff, 0xff, 0x0,
    0xf, 0xff, 0xff, 0xf0, 0x0, 0xff, 0xff, 0xff,
    0x0, 0xf, 0xff, 0xff, 0xf0, 0x0, 0xff, 0xff,
    0xff, 0x0, 0xf, 0xff, 0xff, 0xf0, 0x0, 0xff,
    0xff, 0xff, 0x0, 0xf, 0xff, 0xff, 0xf0, 0x0,
    0xff, 0xff, 0xff, 0x0, 0xf, 0xff, 0xff, 0xf0,
    0x0, 0xff, 0xff, 0xff, 0x0, 0xf, 0xff, 0xff,
    0xf0, 0x0, 0xff, 0xff, 0xff, 0x80, 0xf, 0xff,
    0xff, 0xf8, 0x0, 0xff, 0xff, 0xff, 0x80, 0x1f,
    0xfe, 0xff, 0xf8, 0x1, 0xff, 0xef, 0xff, 0x80,
    0x1f, 0xfe, 0xff, 0xf8, 0x1, 0xff, 0xef, 0xff,
    0xc0, 0x3f, 0xfe, 0xff, 0xfc, 0x3, 0xff, 0xcf,
    0xff, 0xe0, 0x7f, 0xfc, 0xff, 0xff, 0xf, 0xff,
    0xcf, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xff, 0x8f, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xf, 0xff, 0x7f, 0xff, 0xe0, 0xff,
    0xf7, 0xff, 0xfe, 0xf, 0xff, 0x3f, 0xff, 0xc0,
    0xff, 0xf1, 0xff, 0xf8, 0xf, 0xff, 0xf, 0xfe,
    0x0, 0xff, 0xf0, 0x3f, 0x80, 0xf, 0xff, 0x0,
    0x0, 0x0, 0xff, 0xf0, 0x0, 0x0, 0xf, 0xff,
    0x0, 0x0, 0x0, 0xff, 0xf0, 0x0, 0x0, 0xf,
    0xff, 0x0, 0x0, 0x0, 0xff, 0xf0, 0x0, 0x0,
    0xf, 0xff, 0x0, 0x0, 0x0, 0xff, 0xf0, 0x0,
    0x0, 0xf, 0xff, 0x0, 0x0, 0x0, 0xff, 0xf0,
    0x0, 0x0, 0xf, 0xff, 0x0, 0x0, 0x0, 0xff,
    0xf0, 0x0, 0x0, 0xf, 0xff, 0x0, 0x0, 0x0,
    0xff, 0xf0, 0x0, 0x0, 0xf, 0xff, 0x0, 0x0,
    0x0, 0xff, 0xf0, 0x0, 0x0, 0xf, 0xff, 0x0,
    0x0, 0x0, 0xff, 0xf0, 0x0, 0x0, 0xf, 0xff,
    0x0, 0x0, 0x0, 0xff, 0xf0, 0x0, 0x0, 0xf,
    0xff, 0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 356, .box_w = 1, .box_h = 1, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1, .adv_w = 667, .box_w = 35, .box_h = 66, .ofs_x = 3, .ofs_y = -1},
    {.bitmap_index = 290, .adv_w = 667, .box_w = 28, .box_h = 64, .ofs_x = 4, .ofs_y = 0},
    {.bitmap_index = 514, .adv_w = 667, .box_w = 35, .box_h = 65, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 799, .adv_w = 667, .box_w = 35, .box_h = 66, .ofs_x = 3, .ofs_y = -1},
    {.bitmap_index = 1088, .adv_w = 667, .box_w = 39, .box_h = 65, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1405, .adv_w = 667, .box_w = 34, .box_h = 65, .ofs_x = 4, .ofs_y = -1},
    {.bitmap_index = 1682, .adv_w = 667, .box_w = 35, .box_h = 66, .ofs_x = 3, .ofs_y = -1},
    {.bitmap_index = 1971, .adv_w = 667, .box_w = 34, .box_h = 64, .ofs_x = 4, .ofs_y = 0},
    {.bitmap_index = 2243, .adv_w = 667, .box_w = 36, .box_h = 66, .ofs_x = 3, .ofs_y = -1},
    {.bitmap_index = 2540, .adv_w = 667, .box_w = 35, .box_h = 66, .ofs_x = 3, .ofs_y = -1},
    {.bitmap_index = 2829, .adv_w = 389, .box_w = 15, .box_h = 51, .ofs_x = 5, .ofs_y = -1},
    {.bitmap_index = 2925, .adv_w = 769, .box_w = 48, .box_h = 65, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3315, .adv_w = 1129, .box_w = 60, .box_h = 64, .ofs_x = 6, .ofs_y = 0},
    {.bitmap_index = 3795, .adv_w = 690, .box_w = 35, .box_h = 65, .ofs_x = 6, .ofs_y = -1},
    {.bitmap_index = 4080, .adv_w = 672, .box_w = 35, .box_h = 51, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 4304, .adv_w = 1117, .box_w = 60, .box_h = 50, .ofs_x = 5, .ofs_y = 0},
    {.bitmap_index = 4679, .adv_w = 714, .box_w = 36, .box_h = 72, .ofs_x = 5, .ofs_y = -22}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_0[] = {
    0x0, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16,
    0x17, 0x18, 0x19, 0x1a, 0x21, 0x2d, 0x30, 0x41,
    0x4d, 0x50
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 81, .glyph_id_start = 1,
        .unicode_list = unicode_list_0, .glyph_id_ofs_list = NULL, .list_length = 18, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Pair left and right glyphs for kerning*/
static const uint8_t kern_pair_glyph_ids[] =
{
    15, 13
};

/* Kerning between the respective left and right glyphs
 * 4.4 format which needs to scaled with `kern_scale`*/
static const int8_t kern_pair_values[] =
{
    -29
};

/*Collect the kern pair's data in one place*/
static const lv_font_fmt_txt_kern_pair_t kern_pairs =
{
    .glyph_ids = kern_pair_glyph_ids,
    .values = kern_pair_values,
    .pair_cnt = 1,
    .glyph_ids_size = 0
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR >= 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_pairs,
    .kern_scale = 16,
    .cmap_num = 1,
    .bpp = 1,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR >= 8
    .cache = &cache
#endif
};


/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t opensans_90 = {
#else
lv_font_t opensans_90 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 87,          /*The maximum line height required by the font*/
    .base_line = 22,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -7,
    .underline_thickness = 4,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
    .fallback = NULL,
    .user_data = NULL
};



#endif /*#if OPENSANS_90*/

