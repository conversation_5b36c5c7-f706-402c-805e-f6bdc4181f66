#ifndef DISPLAY_H
#define DISPLAY_H

#include <Arduino.h>
#include <TFT_eSPI.h>
#include <WiFi.h>
#include "data_types.h"

class Display {
private:
  TFT_eSPI tft;

  void drawHeader(const InverterData& data);
  void showNoData();
  void drawSolarSection(const InverterData& data);
  void drawBatterySection(const InverterData& data);
  void drawGridSection(const InverterData& data);
  void drawOutputSection(const InverterData& data);
  void drawSystemSection(const InverterData& data);
  void drawWiFiStatus();

public:
  Display();

  void init();
  void showStartupScreen();
  void showWiFiSetup();
  void showWiFiConnected(IPAddress ip);
  void showError(const String& message);
  void updateData(const InverterData& data);
  void setBrightness(uint8_t brightness);
};

#endif // DISPLAY_H
