#include <Arduino.h>
#include <TFT_eSPI.h>

// Display
TFT_eSPI tft = TFT_eSPI(240, 320); // กำหนดขนาดโดยตรง

// Pin definitions
#define TFT_BL 21

void setup() {
    Serial.begin(115200);
    Serial.println("Starting TFT Test...");

    // Initialize backlight pin
    pinMode(TFT_BL, OUTPUT);
    digitalWrite(TFT_BL, HIGH); // Turn on backlight
    Serial.println("Backlight turned ON");

    // Initialize display
    Serial.println("Initializing TFT...");
    tft.init();
    Serial.println("TFT initialized");

    tft.setRotation(1); // Landscape 320x240
    Serial.println("Rotation set to 1 (Landscape)");

    // Fill screen with white first to test
    Serial.println("Filling screen with WHITE...");
    tft.fillScreen(TFT_WHITE);
    delay(2000);

    // Then fill with red
    Serial.println("Filling screen with RED...");
    tft.fillScreen(TFT_RED);
    delay(2000);

    // Then black
    Serial.println("Filling screen with BLACK...");
    tft.fillScreen(TFT_BLACK);
    delay(1000);

    // Test display
    Serial.println("Drawing text and shapes...");
    tft.setTextColor(TFT_WHITE, TFT_BLACK);
    tft.setTextSize(2);
    tft.drawString("TFT Test", 10, 10);
    tft.drawString("320x240", 10, 40);

    // Draw some shapes
    tft.drawRect(10, 80, 100, 50, TFT_RED);
    tft.fillRect(120, 80, 100, 50, TFT_GREEN);
    tft.drawCircle(160, 180, 30, TFT_BLUE);

    Serial.println("TFT Test completed!");
}

void loop() {
    // Blink backlight to test
    digitalWrite(TFT_BL, LOW);
    delay(500);
    digitalWrite(TFT_BL, HIGH);
    delay(500);
}
