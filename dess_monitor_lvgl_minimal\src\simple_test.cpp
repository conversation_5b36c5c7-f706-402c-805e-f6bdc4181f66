#include <Arduino.h>
#include <TFT_eSPI.h>

// Display
TFT_eSPI tft = TFT_eSPI(240, 320); // กำหนดขนาดโดยตรง

// Pin definitions
#define TFT_BL 21

void setup() {
    Serial.begin(115200);
    Serial.println("Starting TFT Test...");

    // Initialize backlight pin
    pinMode(TFT_BL, OUTPUT);
    digitalWrite(TFT_BL, HIGH); // Turn on backlight
    Serial.println("Backlight turned ON");

    // Initialize display
    Serial.println("Initializing TFT...");
    tft.init();
    Serial.println("TFT initialized");

    // Test different rotations
    for(int rotation = 0; rotation < 4; rotation++) {
        Serial.printf("Testing rotation %d...\n", rotation);
        tft.setRotation(rotation);

        // Fill with different colors for each rotation
        if(rotation == 0) tft.fillScreen(TFT_RED);
        else if(rotation == 1) tft.fillScreen(TFT_GREEN);
        else if(rotation == 2) tft.fillScreen(TFT_BLUE);
        else tft.fillScreen(TFT_YELLOW);

        delay(2000);

        // Draw text
        tft.setTextColor(TFT_WHITE, TFT_BLACK);
        tft.setTextSize(3);
        tft.drawString("ROT " + String(rotation), 10, 10);
        tft.drawString(String(tft.width()) + "x" + String(tft.height()), 10, 50);

        delay(3000);
    }

    Serial.println("TFT Test completed!");
}

void loop() {
    delay(1000);
}
