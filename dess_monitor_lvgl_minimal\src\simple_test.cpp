#include <Arduino.h>
#include <TFT_eSPI.h>

// Display
TFT_eSPI tft = TFT_eSPI(240, 320); // กำหนดขนาดโดยตรง

void setup() {
    Serial.begin(115200);
    Serial.println("Starting TFT Test...");

    // Initialize display
    tft.init();
    tft.setRotation(1); // Landscape 320x240
    tft.fillScreen(TFT_BLACK);
    
    // Test display
    tft.setTextColor(TFT_WHITE, TFT_BLACK);
    tft.setTextSize(2);
    tft.drawString("TFT Test", 10, 10);
    tft.drawString("320x240", 10, 40);
    
    // Draw some shapes
    tft.drawRect(10, 80, 100, 50, TFT_RED);
    tft.fillRect(120, 80, 100, 50, TFT_GREEN);
    tft.drawCircle(160, 180, 30, TFT_BLUE);
    
    Serial.println("TFT Test completed!");
}

void loop() {
    delay(1000);
}
