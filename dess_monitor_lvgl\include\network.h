#ifndef NETWORK_H
#define NETWORK_H

#include <Arduino.h>
#include <WiFi.h>
#include <HTTPClient.h>
#include <ArduinoJson.h>
#include <WiFiManager.h>
#include "data_types.h"

// Network Configuration
#define WIFI_TIMEOUT 30000
#define HTTP_TIMEOUT 10000
#define MAX_RETRY_COUNT 3
#define RECONNECT_INTERVAL 5000

// API Configuration
extern const char* API_BASE_URL;
extern const char* DEVICE_CODE;
extern const char* DEVICE_SN;

// Network status enumeration
enum class NetworkStatus {
  DISCONNECTED,
  CONNECTING,
  CONNECTED,
  ERROR,
  TIMEOUT
};

// Network manager class
class NetworkManager {
private:
  WiFiManager wifiManager;
  HTTPClient httpClient;
  NetworkStatus status;
  uint32_t lastConnectionAttempt;
  uint32_t lastDataFetch;
  int retryCount;
  String lastError;
  
public:
  NetworkManager();
  ~NetworkManager();
  
  // WiFi management
  bool initializeWiFi();
  bool connectWiFi();
  void disconnectWiFi();
  bool isConnected();
  void resetWiFiSettings();
  void startConfigPortal();
  
  // Connection monitoring
  void checkConnection();
  void handleReconnection();
  NetworkStatus getStatus();
  String getStatusString();
  
  // Signal strength
  int getSignalStrength();
  String getSignalQuality();
  
  // Network info
  String getSSID();
  String getLocalIP();
  String getMACAddress();
  
  // Data fetching
  bool fetchInverterData(InverterData& data);
  bool sendCommand(const String& command, const String& value);
  
  // Error handling
  String getLastError();
  void clearError();
  
  // Callbacks
  void onWiFiConnected();
  void onWiFiDisconnected();
  void onDataReceived(const InverterData& data);
  void onError(const String& error);
};

// HTTP response structure
struct HTTPResponse {
  int statusCode = 0;
  String body = "";
  String error = "";
  bool success = false;
  uint32_t responseTime = 0;
};

// Network functions
bool initializeNetwork();
void updateNetworkStatus();
HTTPResponse makeHTTPRequest(const String& url, const String& method = "GET", const String& payload = "");
bool parseInverterResponse(const String& response, InverterData& data);

// Network status
struct NetworkInfo {
  NetworkStatus status = NetworkStatus::DISCONNECTED;
  String ssid = "";
  String ip = "";
  String mac = "";
  int signalStrength = 0;
  uint32_t connectedTime = 0;
  uint32_t lastDataUpdate = 0;
  uint32_t dataFetchInterval = 10000;
  bool autoReconnect = true;
  int errorCount = 0;
  String lastError = "";
};

extern NetworkManager networkManager;
extern NetworkInfo networkInfo;

// Network event callbacks
typedef void (*WiFiConnectedCallback)();
typedef void (*WiFiDisconnectedCallback)();
typedef void (*DataReceivedCallback)(const InverterData& data);
typedef void (*NetworkErrorCallback)(const String& error);

void setWiFiConnectedCallback(WiFiConnectedCallback callback);
void setWiFiDisconnectedCallback(WiFiDisconnectedCallback callback);
void setDataReceivedCallback(DataReceivedCallback callback);
void setNetworkErrorCallback(NetworkErrorCallback callback);

#endif // NETWORK_H
