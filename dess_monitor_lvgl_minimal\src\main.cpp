#include <Arduino.h>
#include <TFT_eSPI.h>

// Hardware pins
#define TFT_BL 21

// Display
TFT_eSPI tft = TFT_eSPI(240, 320);

// Test variables
unsigned long lastUpdate = 0;
int testCounter = 0;

void setup() {
    Serial.begin(115200);
    Serial.println("🚀 Starting EXTREME TFT Test...");

    // Initialize backlight
    pinMode(TFT_BL, OUTPUT);
    digitalWrite(TFT_BL, HIGH);
    Serial.println("✅ Backlight ON");

    // Initialize display
    tft.init();
    tft.setRotation(1); // Landscape 320x240
    Serial.println("✅ TFT initialized");

    // Fill screen with bright color
    tft.fillScreen(TFT_RED);
    delay(1000);
    tft.fillScreen(TFT_GREEN);
    delay(1000);
    tft.fillScreen(TFT_BLUE);
    delay(1000);
    tft.fillScreen(TFT_BLACK);
    
    Serial.println("🎉 TFT Test ready!");
}

void loop() {
    // Update every 2 seconds
    if (millis() - lastUpdate > 2000) {
        testCounter++;
        
        // Clear screen
        tft.fillScreen(TFT_BLACK);
        
        // Draw LARGE WHITE TEXT
        tft.setTextColor(TFT_WHITE);
        tft.setTextSize(4);
        tft.drawString("TEST", 50, 30);
        
        // Draw counter in different colors
        tft.setTextColor(TFT_RED);
        tft.setTextSize(3);
        tft.drawString("COUNT: " + String(testCounter), 20, 80);
        
        // Draw time
        tft.setTextColor(TFT_GREEN);
        tft.setTextSize(2);
        tft.drawString("TIME: " + String(millis()/1000) + "s", 20, 130);
        
        // Draw some shapes
        tft.fillRect(20, 160, 100, 30, TFT_BLUE);
        tft.fillCircle(200, 175, 20, TFT_YELLOW);
        
        // Draw border
        tft.drawRect(0, 0, 320, 240, TFT_WHITE);
        
        Serial.printf("📊 Test update #%d - Time: %lu ms\n", testCounter, millis());
        
        lastUpdate = millis();
    }
    
    delay(100);
}
