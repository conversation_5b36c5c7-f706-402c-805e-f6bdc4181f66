#include <Arduino.h>
#include <lvgl.h>
#include <TFT_eSPI.h>
#include <XPT2046_Touchscreen.h>
#include <WiFi.h>
#include <HTTPClient.h>
#include <ArduinoJson.h>

// Hardware Configuration (ใช้ขนาดจริงของหน้าจอ)
#define SCREEN_WIDTH  320
#define SCREEN_HEIGHT 240

// Touch screen pins
#define XPT2046_IRQ  36
#define XPT2046_MOSI 32
#define XPT2046_MISO 39
#define XPT2046_CLK  25
#define XPT2046_CS   33

// Other pins
#define BOOT_BUTTON  0
#define TFT_BL       21

// WiFi credentials - แก้ไขตรงนี้
const char* ssid = "TMOGNOT 2G";
const char* password = "tmognot1121";

// Hardware instances
TFT_eSPI tft = TFT_eSPI();
SPIClass touchscreenSPI = SPIClass(VSPI);
XPT2046_Touchscreen ts(XPT2046_CS, XPT2046_IRQ);

// LVGL display buffer - optimized for 320x240
static lv_disp_draw_buf_t draw_buf;
static lv_color_t buf[SCREEN_WIDTH * 20]; // Larger buffer for better performance

// Data structure
struct InverterData {
  float pvPower = 0;
  float batteryCapacity = 0;
  float gridPower = 0;
  float outputPower = 0;
  float loadPercent = 0;
  bool dataValid = false;
  unsigned long lastUpdate = 0;
};

// Global variables
InverterData data;
lv_obj_t* mainScreen;
lv_obj_t* solarLabel;
lv_obj_t* batteryLabel;
lv_obj_t* gridLabel;
lv_obj_t* loadLabel;
lv_obj_t* statusLabel;
lv_obj_t* timeLabel;

// API Configuration
const char* API_BASE_URL = "https://web.dessmonitor.com/public/";
const char* DEVICE_CODE = "2376";
const char* DEVICE_SN = "Q0046526082082094801";

// Function declarations
void initializeHardware();
void initializeLVGL();
void createUI();
void updateUI();
bool fetchData();
void parseData(const String& response);
void displayFlushCallback(lv_disp_drv_t* disp, const lv_area_t* area, lv_color_t* color_p);
void touchpadReadCallback(lv_indev_drv_t* indev, lv_indev_data_t* data);

void setup() {
  Serial.begin(115200);
  delay(1000);

  Serial.println("\n=== DESS Monitor Pro LVGL Minimal v1.0 ===");
  Serial.println("🚀 Starting system initialization...");

  // Initialize hardware
  initializeHardware();

  // Initialize LVGL
  initializeLVGL();

  // Create UI
  createUI();

  // Initialize WiFi
  WiFi.begin(ssid, password);
  Serial.print("📡 Connecting to WiFi");

  int attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < 20) {
    delay(500);
    Serial.print(".");
    attempts++;
  }

  if (WiFi.status() == WL_CONNECTED) {
    Serial.println("\n✅ WiFi connected!");
    Serial.printf("🌐 IP address: %s\n", WiFi.localIP().toString().c_str());
  } else {
    Serial.println("\n❌ WiFi connection failed!");
  }

  Serial.println("✅ System initialization complete!");
}

void loop() {
  lv_timer_handler();

  static unsigned long lastDataUpdate = 0;
  static unsigned long lastUIUpdate = 0;

  // Fetch data every 10 seconds
  if (millis() - lastDataUpdate > 10000) {
    if (WiFi.status() == WL_CONNECTED) {
      if (fetchData()) {
        Serial.println("✅ Data updated successfully");
      } else {
        Serial.println("❌ Failed to fetch data");
      }
    }
    lastDataUpdate = millis();
  }

  // Update UI every 500ms
  if (millis() - lastUIUpdate > 500) {
    updateUI();
    lastUIUpdate = millis();
  }

  delay(5);
}

void initializeHardware() {
  Serial.println("🔧 Initializing hardware...");

  // Initialize backlight first
  pinMode(TFT_BL, OUTPUT);
  digitalWrite(TFT_BL, HIGH); // Full brightness

  // Initialize TFT with ESP32-2432S028R optimized settings
  tft.init();
  tft.setRotation(1); // Landscape mode

  // Configure for ESP32-2432S028R display
  tft.setSwapBytes(false); // Disable byte swapping for correct colors

  // Set CPU frequency to maximum for best performance
  setCpuFrequencyMhz(240); // 240MHz - maximum ESP32 frequency

  tft.fillScreen(TFT_BLACK);
  Serial.printf("✅ TFT display initialized (%dx%d) with max performance\n", tft.width(), tft.height());
  Serial.printf("✅ CPU frequency set to %dMHz for maximum FPS\n", getCpuFrequencyMhz());

  // Initialize touch screen with proper SPI setup
  touchscreenSPI.begin(XPT2046_CLK, XPT2046_MISO, XPT2046_MOSI, XPT2046_CS);
  ts.begin(touchscreenSPI);
  ts.setRotation(1); // Match TFT rotation
  Serial.println("✅ XPT2046 Touch screen initialized with proper SPI");

  // Test touch screen immediately
  Serial.println("🔍 Testing XPT2046 touch screen...");
  delay(100);
  if (ts.tirqTouched() && ts.touched()) {
    TS_Point p = ts.getPoint();
    Serial.printf("🔍 Touch test: ACTIVE at x=%d, y=%d, pressure=%d\n", p.x, p.y, p.z);
  } else {
    Serial.println("🔍 Touch test: No touch detected (normal)");
  }

  // Touch screen calibration info
  Serial.println("🔍 XPT2046 Touch screen calibration data:");
  Serial.printf("🔍 Touch CS pin: %d, IRQ pin: %d\n", XPT2046_CS, XPT2046_IRQ);
  Serial.printf("🔍 SPI pins: CLK=%d, MISO=%d, MOSI=%d\n", XPT2046_CLK, XPT2046_MISO, XPT2046_MOSI);
  Serial.println("🔍 XPT2046 Touch ready for input");

  // Initialize boot button
  pinMode(BOOT_BUTTON, INPUT_PULLUP);

  Serial.println("✅ Hardware initialized");
}

void initializeLVGL() {
  Serial.println("🎨 Initializing LVGL...");

  lv_init();

  // Initialize display buffer with larger size
  lv_disp_draw_buf_init(&draw_buf, buf, NULL, SCREEN_WIDTH * 20);

  // Initialize display driver
  static lv_disp_drv_t disp_drv;
  lv_disp_drv_init(&disp_drv);
  disp_drv.hor_res = SCREEN_WIDTH;
  disp_drv.ver_res = SCREEN_HEIGHT;
  disp_drv.flush_cb = displayFlushCallback;
  disp_drv.draw_buf = &draw_buf;
  lv_disp_drv_register(&disp_drv);

  // Initialize input driver
  static lv_indev_drv_t indev_drv;
  lv_indev_drv_init(&indev_drv);
  indev_drv.type = LV_INDEV_TYPE_POINTER;
  indev_drv.read_cb = touchpadReadCallback;
  lv_indev_drv_register(&indev_drv);

  Serial.println("✅ LVGL initialized");
}

void createUI() {
  Serial.println("🖼️ Creating minimal UI...");

  // Create main screen
  mainScreen = lv_obj_create(NULL);
  lv_obj_set_style_bg_color(mainScreen, lv_color_black(), 0);
  lv_scr_load(mainScreen);

  // Title
  lv_obj_t* titleLabel = lv_label_create(mainScreen);
  lv_label_set_text(titleLabel, "DESS Monitor Pro");
  lv_obj_set_style_text_color(titleLabel, lv_color_white(), 0);
  lv_obj_set_style_text_font(titleLabel, &lv_font_montserrat_16, 0);
  lv_obj_align(titleLabel, LV_ALIGN_TOP_MID, 0, 10);

  // Time
  timeLabel = lv_label_create(mainScreen);
  lv_label_set_text(timeLabel, "00:00");
  lv_obj_set_style_text_color(timeLabel, lv_color_white(), 0);
  lv_obj_align(timeLabel, LV_ALIGN_TOP_RIGHT, -10, 10);

  // Solar data
  solarLabel = lv_label_create(mainScreen);
  lv_label_set_text(solarLabel, "Solar: 0.0W");
  lv_obj_set_style_text_color(solarLabel, lv_color_hex(0xFFC107), 0);
  lv_obj_set_style_text_font(solarLabel, &lv_font_montserrat_14, 0);
  lv_obj_align(solarLabel, LV_ALIGN_TOP_LEFT, 10, 50);

  // Battery data
  batteryLabel = lv_label_create(mainScreen);
  lv_label_set_text(batteryLabel, "Battery: 0%");
  lv_obj_set_style_text_color(batteryLabel, lv_color_hex(0x4CAF50), 0);
  lv_obj_set_style_text_font(batteryLabel, &lv_font_montserrat_14, 0);
  lv_obj_align(batteryLabel, LV_ALIGN_TOP_RIGHT, -10, 50);

  // Grid data
  gridLabel = lv_label_create(mainScreen);
  lv_label_set_text(gridLabel, "Grid: 0.0W");
  lv_obj_set_style_text_color(gridLabel, lv_color_hex(0x2196F3), 0);
  lv_obj_set_style_text_font(gridLabel, &lv_font_montserrat_14, 0);
  lv_obj_align(gridLabel, LV_ALIGN_CENTER, -50, -20);

  // Load data
  loadLabel = lv_label_create(mainScreen);
  lv_label_set_text(loadLabel, "Load: 0.0W");
  lv_obj_set_style_text_color(loadLabel, lv_color_hex(0xFF5722), 0);
  lv_obj_set_style_text_font(loadLabel, &lv_font_montserrat_14, 0);
  lv_obj_align(loadLabel, LV_ALIGN_CENTER, 50, -20);

  // Status
  statusLabel = lv_label_create(mainScreen);
  lv_label_set_text(statusLabel, "Status: Initializing...");
  lv_obj_set_style_text_color(statusLabel, lv_color_white(), 0);
  lv_obj_align(statusLabel, LV_ALIGN_BOTTOM_MID, 0, -10);

  Serial.println("✅ Minimal UI created");
}

void updateUI() {
  // Update time
  struct tm timeinfo;
  if (getLocalTime(&timeinfo)) {
    char timeStr[16];
    snprintf(timeStr, sizeof(timeStr), "%02d:%02d", timeinfo.tm_hour, timeinfo.tm_min);
    lv_label_set_text(timeLabel, timeStr);
  }

  // Update data labels
  if (data.dataValid) {
    char solarStr[32];
    snprintf(solarStr, sizeof(solarStr), "Solar: %.1fW", data.pvPower);
    lv_label_set_text(solarLabel, solarStr);

    char batteryStr[32];
    snprintf(batteryStr, sizeof(batteryStr), "Battery: %.0f%%", data.batteryCapacity);
    lv_label_set_text(batteryLabel, batteryStr);

    char gridStr[32];
    snprintf(gridStr, sizeof(gridStr), "Grid: %.1fW", data.gridPower);
    lv_label_set_text(gridLabel, gridStr);

    char loadStr[32];
    snprintf(loadStr, sizeof(loadStr), "Load: %.1fW", data.outputPower);
    lv_label_set_text(loadLabel, loadStr);

    lv_label_set_text(statusLabel, "Status: Data OK");
  } else {
    lv_label_set_text(statusLabel, "Status: No Data");
  }
}

bool fetchData() {
  if (WiFi.status() != WL_CONNECTED) return false;

  HTTPClient http;
  String url = String(API_BASE_URL) +
               "?sign=8743221c28ad40664baa48193bbf4b03caa726f1" +
               "&salt=" + String(millis()) +
               "&token=c8075906465cca180724b25d151680e31e2267ff1319ccf2b43c88ba979a7576" +
               "&action=querySPDeviceLastData" +
               "&source=1" +
               "&devcode=" + String(DEVICE_CODE) +
               "&pn=" + String(DEVICE_SN) +
               "&devaddr=1" +
               "&sn=" + String(DEVICE_SN) +
               "&i18n=en_US";

  http.begin(url);
  http.setTimeout(10000);

  int httpCode = http.GET();

  if (httpCode == 200) {
    String response = http.getString();
    parseData(response);
    http.end();
    return true;
  }

  http.end();
  return false;
}

void parseData(const String& response) {
  DynamicJsonDocument doc(4096);
  DeserializationError error = deserializeJson(doc, response);

  if (error) {
    Serial.printf("❌ JSON parsing failed: %s\n", error.c_str());
    return;
  }

  if (doc["err"] != 0) {
    Serial.printf("❌ API error: %s\n", doc["desc"].as<String>().c_str());
    return;
  }

  // Parse different parameter groups
  JsonObject pars = doc["dat"]["pars"];

  // Parse PV data
  if (pars["pv_"].is<JsonArray>()) {
    for (JsonObject param : pars["pv_"].as<JsonArray>()) {
      String parName = param["par"].as<String>();
      if (parName == "PV Power") {
        data.pvPower = param["val"].as<float>();
      }
    }
  }

  // Parse Battery data
  if (pars["bt_"].is<JsonArray>()) {
    for (JsonObject param : pars["bt_"].as<JsonArray>()) {
      String parName = param["par"].as<String>();
      if (parName == "bt_battery_capacity") {
        data.batteryCapacity = param["val"].as<float>();
      }
    }
  }

  // Parse Grid data
  if (pars["gd_"].is<JsonArray>()) {
    for (JsonObject param : pars["gd_"].as<JsonArray>()) {
      String parName = param["par"].as<String>();
      if (parName == "Grid Power") {
        data.gridPower = param["val"].as<float>();
      }
    }
  }

  // Parse Output data
  if (pars["bc_"].is<JsonArray>()) {
    for (JsonObject param : pars["bc_"].as<JsonArray>()) {
      String parName = param["par"].as<String>();
      if (parName == "Active Power") {
        data.outputPower = param["val"].as<float>();
      } else if (parName == "Load Percent") {
        data.loadPercent = param["val"].as<float>();
      }
    }
  }

  data.dataValid = true;
  data.lastUpdate = millis();

  Serial.println("✅ Data parsed successfully");
}

void displayFlushCallback(lv_disp_drv_t* disp, const lv_area_t* area, lv_color_t* color_p) {
  uint32_t w = (area->x2 - area->x1 + 1);
  uint32_t h = (area->y2 - area->y1 + 1);

  tft.startWrite();
  tft.setAddrWindow(area->x1, area->y1, w, h);

  // Use optimized color pushing for ESP32-2432S028R
  uint16_t* pixels = (uint16_t*)color_p;
  tft.pushColors(pixels, w * h, false); // No byte swapping needed

  tft.endWrite();
  lv_disp_flush_ready(disp);
}

void touchpadReadCallback(lv_indev_drv_t* indev, lv_indev_data_t* data) {
  bool touched = ts.tirqTouched() && ts.touched();

  if (touched) {
    TS_Point p = ts.getPoint();
    data->state = LV_INDEV_STATE_PR;
    // Proper calibration for ESP32-2432S028R
    data->point.x = map(p.x, 200, 3700, 1, SCREEN_WIDTH);
    data->point.y = map(p.y, 240, 3800, 1, SCREEN_HEIGHT);

    // Debug touch coordinates
    Serial.printf("🔍 Touch: raw(%d,%d,%d) -> screen(%d,%d)\n",
                  p.x, p.y, p.z, data->point.x, data->point.y);
  } else {
    data->state = LV_INDEV_STATE_REL;
  }
}
