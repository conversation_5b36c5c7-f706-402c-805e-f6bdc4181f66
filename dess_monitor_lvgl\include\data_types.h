#ifndef DATA_TYPES_H
#define DATA_TYPES_H

#include <Arduino.h>
#include <lvgl.h>

// Forward declarations
struct InverterData;
struct UIComponents;
struct AnimationState;

// Data structure for inverter information
struct InverterData {
  // Grid data
  float gridVoltage = 0.0f;
  float gridFrequency = 0.0f;
  float gridPower = 0.0f;
  
  // PV/Solar data
  float pvVoltage = 0.0f;
  float pvCurrent = 0.0f;
  float pvPower = 0.0f;
  float pvChargingCurrent = 0.0f;
  
  // Battery data
  float batteryVoltage = 0.0f;
  float batteryCurrent = 0.0f;
  float batteryCapacity = 0.0f;  // Percentage from API
  
  // Output/Load data
  float outputVoltage = 0.0f;
  float outputCurrent = 0.0f;
  float outputPower = 0.0f;
  float outputApparentPower = 0.0f;
  float outputFrequency = 0.0f;
  float loadPercent = 0.0f;
  
  // System data
  int dcTemp = 0;
  int invTemp = 0;
  String operatingMode = "";
  String outputPriority = "";
  String chargerSourcePriority = "";
  
  // Charging data
  float acChargingCurrent = 0.0f;
  
  // Metadata
  String timestamp = "";
  unsigned long lastUpdate = 0;
  bool dataValid = false;
  int errorCount = 0;
  int signalStrength = 0;
  
  // Helper methods
  bool isCharging() const {
    return batteryCurrent > 0.1f; // Positive current = charging
  }
  
  bool isDischarging() const {
    return batteryCurrent < -0.1f; // Negative current = discharging
  }
  
  float getBatteryLevel() const {
    // Use API battery capacity if available, otherwise estimate from voltage
    if (batteryCapacity > 0) {
      return batteryCapacity;
    }
    
    // Estimate battery level based on voltage (48V system)
    float minVoltage = 48.0f; // 0%
    float maxVoltage = 58.4f; // 100%
    
    float level = ((batteryVoltage - minVoltage) / (maxVoltage - minVoltage)) * 100.0f;
    return constrain(level, 0.0f, 100.0f);
  }
  
  String getBatteryStatus() const {
    float level = getBatteryLevel();
    if (level > 80) return "Excellent";
    if (level > 60) return "Good";
    if (level > 40) return "Fair";
    if (level > 20) return "Low";
    return "Critical";
  }
  
  lv_color_t getBatteryColor() const {
    float level = getBatteryLevel();
    if (level > 60) return lv_color_hex(0x4CAF50); // Green
    if (level > 30) return lv_color_hex(0xFFC107); // Yellow
    return lv_color_hex(0xF44336); // Red
  }
  
  void reset() {
    gridVoltage = 0.0f;
    gridFrequency = 0.0f;
    gridPower = 0.0f;
    pvVoltage = 0.0f;
    pvCurrent = 0.0f;
    pvPower = 0.0f;
    pvChargingCurrent = 0.0f;
    batteryVoltage = 0.0f;
    batteryCurrent = 0.0f;
    batteryCapacity = 0.0f;
    outputVoltage = 0.0f;
    outputCurrent = 0.0f;
    outputPower = 0.0f;
    outputApparentPower = 0.0f;
    outputFrequency = 0.0f;
    loadPercent = 0.0f;
    dcTemp = 0;
    invTemp = 0;
    operatingMode = "";
    outputPriority = "";
    chargerSourcePriority = "";
    acChargingCurrent = 0.0f;
    timestamp = "";
    dataValid = false;
    errorCount = 0;
    signalStrength = 0;
  }
};

// Connection status enumeration
enum class ConnectionStatus {
  DISCONNECTED,
  CONNECTING,
  CONNECTED,
  ERROR
};

// Data status enumeration
enum class DataStatus {
  NO_DATA,
  FETCHING,
  VALID,
  ERROR,
  TIMEOUT
};

// Theme enumeration
enum class ThemeType {
  DARK = 0,
  LIGHT = 1,
  SOLAR = 2,
  BLUE = 3,
  GREEN = 4
};

// Animation state structure
struct AnimationState {
  bool isAnimating = false;
  uint32_t startTime = 0;
  uint32_t duration = 400;
  lv_anim_path_cb_t pathCb = lv_anim_path_ease_out;
  void* targetObj = nullptr;
  lv_anim_exec_xcb_t execCb = nullptr;
  lv_anim_ready_cb_t readyCb = nullptr;
};

// UI Components structure
struct UIComponents {
  // Main screen
  lv_obj_t* mainScreen = nullptr;
  lv_obj_t* headerPanel = nullptr;
  lv_obj_t* contentPanel = nullptr;
  
  // Header components
  lv_obj_t* titleLabel = nullptr;
  lv_obj_t* timeLabel = nullptr;
  lv_obj_t* wifiIcon = nullptr;
  lv_obj_t* signalBars = nullptr;
  
  // Data cards
  lv_obj_t* solarCard = nullptr;
  lv_obj_t* batteryCard = nullptr;
  lv_obj_t* gridCard = nullptr;
  lv_obj_t* loadCard = nullptr;
  
  // Card components
  lv_obj_t* solarIcon = nullptr;
  lv_obj_t* solarValue = nullptr;
  lv_obj_t* solarStatus = nullptr;
  
  lv_obj_t* batteryIcon = nullptr;
  lv_obj_t* batteryValue = nullptr;
  lv_obj_t* batteryBar = nullptr;
  lv_obj_t* batteryPercent = nullptr;
  
  lv_obj_t* gridIcon = nullptr;
  lv_obj_t* gridValue = nullptr;
  lv_obj_t* gridStatus = nullptr;
  
  lv_obj_t* loadIcon = nullptr;
  lv_obj_t* loadValue = nullptr;
  lv_obj_t* loadBar = nullptr;
  
  // Settings screen
  lv_obj_t* settingsScreen = nullptr;
  lv_obj_t* settingsPanel = nullptr;
  lv_obj_t* settingsTitle = nullptr;
  lv_obj_t* settingsClose = nullptr;
  
  // Animation objects
  AnimationState cardAnimations[4];
  AnimationState headerAnimation;
  AnimationState screenTransition;
};

// Touch gesture structure
struct TouchGesture {
  enum Type {
    NONE,
    TAP,
    LONG_PRESS,
    SWIPE_LEFT,
    SWIPE_RIGHT,
    SWIPE_UP,
    SWIPE_DOWN,
    PINCH,
    ZOOM
  };
  
  Type type = NONE;
  lv_point_t startPoint = {0, 0};
  lv_point_t endPoint = {0, 0};
  uint32_t duration = 0;
  bool isValid = false;
};

// Settings structure
struct AppSettings {
  ThemeType currentTheme = ThemeType::DARK;
  bool autoTheme = false;
  uint8_t brightness = 80;
  bool autoBrightness = true;
  bool soundEnabled = true;
  bool animationsEnabled = true;
  uint16_t refreshInterval = 10000; // ms
  String wifiSSID = "";
  String deviceName = "DESS Monitor Pro";
  bool debugMode = false;
};

#endif // DATA_TYPES_H
