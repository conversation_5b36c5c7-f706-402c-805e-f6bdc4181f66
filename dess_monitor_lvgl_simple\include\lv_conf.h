#ifndef LV_CONF_H
#define LV_CONF_H

/*====================
   COLOR SETTINGS
 *====================*/
#define LV_COLOR_DEPTH 16
#define LV_COLOR_16_SWAP 1

/*=========================
   MEMORY SETTINGS
 *=========================*/
#define LV_MEM_SIZE (32U * 1024U)

/*====================
   HAL SETTINGS
 *====================*/
#define LV_DISP_DEF_REFR_PERIOD 16
#define LV_INDEV_DEF_READ_PERIOD 30

#define LV_TICK_CUSTOM 1
#if LV_TICK_CUSTOM
    #define LV_TICK_CUSTOM_INCLUDE "Arduino.h"
    #define LV_TICK_CUSTOM_SYS_TIME_EXPR (millis())
#endif

/*================
 * FEATURE USAGE
 *================*/
#define LV_USE_ANIMATION 1
#define LV_USE_SHADOW 0
#define LV_USE_OUTLINE 0
#define LV_USE_PATTERN 0
#define LV_USE_VALUE_STR 1
#define LV_USE_USER_DATA 1

/*==================
 *  LV OBJ X USAGE
 *==================*/
#define LV_USE_LABEL 1
#define LV_USE_IMG 1
#define LV_USE_LINE 0
#define LV_USE_ARC 0
#define LV_USE_SPINNER 0
#define LV_USE_SLIDER 0
#define LV_USE_SWITCH 0
#define LV_USE_TEXTAREA 1
#define LV_USE_TABLE 0
#define LV_USE_BTN 1
#define LV_USE_BTNMATRIX 1
#define LV_USE_DROPDOWN 0
#define LV_USE_ROLLER 0
#define LV_USE_CHECKBOX 0
#define LV_USE_BAR 0

/*==================
 * FONT USAGE
 *==================*/
#define LV_FONT_MONTSERRAT_14 1
#define LV_FONT_MONTSERRAT_16 1
#define LV_FONT_MONTSERRAT_18 1
#define LV_FONT_MONTSERRAT_20 0

/*==================
 * THEME USAGE
 *==================*/
#define LV_USE_THEME_DEFAULT 1
#define LV_USE_THEME_BASIC 1

/*==================
 * LAYOUTS
 *==================*/
#define LV_USE_FLEX 1
#define LV_USE_GRID 1

#endif /*LV_CONF_H*/
