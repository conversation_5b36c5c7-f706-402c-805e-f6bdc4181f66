#!/usr/bin/env python3
"""
DESS Monitor Data Collector
Automated data collection from DESS Monitor using ChromeDriver
"""

import argparse
import sys
from dess_monitor import DESSMonitor
from scheduler import DataScheduler
from config import Config

def main():
    parser = argparse.ArgumentParser(description='DESS Monitor Data Collector')
    parser.add_argument('--mode', choices=['once', 'continuous'], default='once',
                       help='Collection mode: once or continuous')
    parser.add_argument('--headless', action='store_true', default=True,
                       help='Run browser in headless mode')
    parser.add_argument('--interval', type=int, default=300,
                       help='Collection interval in seconds (for continuous mode)')
    
    args = parser.parse_args()
    
    # Update config based on arguments
    Config.HEADLESS_MODE = args.headless
    Config.COLLECTION_INTERVAL = args.interval
    
    try:
        if args.mode == 'once':
            print("Running single data collection...")
            monitor = DESSMonitor()
            success = monitor.run_once()
            if success:
                print("Data collection completed successfully!")
                return 0
            else:
                print("Data collection failed!")
                return 1
                
        elif args.mode == 'continuous':
            print(f"Starting continuous data collection (interval: {args.interval}s)...")
            scheduler = DataScheduler()
            scheduler.run_continuous()
            return 0
            
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        return 0
    except Exception as e:
        print(f"Error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
