# 🎯 ESP32-2432S028R Display Offset Fix Guide

## 🚨 **ปัญหา: หน้าจอไม่เต็มจอ และ Offset ผิดตำแหน่ง**

### **อาการที่พบจากรูปภาพ:**
- หน้าจอแสดงผลไม่เต็มพื้นที่
- มี offset/เลื่อนตำแหน่ง
- ขนาดหน้าจอไม่ถูกต้อง (ไม่ใช่ 320x240)
- การแสดงผลเพี้ยนจากตำแหน่งที่ควรจะเป็น

## ✅ **การแก้ไขที่ทำ:**

### **1. เปลี่ยน Driver เป็น ILI9341_2_DRIVER**
```ini
# เดิม
-DILI9341_DRIVER=1

# ใหม่ (สำหรับ ESP32-2432S028R)
-DILI9341_2_DRIVER=1
```
**เหตุผล**: ILI9341_2_DRIVER รองรับ ESP32-2432S028R ได้ดีกว่า

### **2. ลบการกำหนด TFT_WIDTH/HEIGHT**
```ini
# ลบออก (ให้ library กำหนดเอง)
# -DTFT_WIDTH=320
# -DTFT_HEIGHT=240
```
**เหตุผล**: Library จะกำหนดขนาดที่ถูกต้องเอง

### **3. เพิ่มการตั้งค่า Offset และ Rotation**
```ini
-DTFT_ROTATION=1    # Landscape mode
-DTFT_OFFSET_X=0    # ไม่มี X offset
-DTFT_OFFSET_Y=0    # ไม่มี Y offset
```

### **4. ปรับการตั้งค่าอื่นๆ**
```ini
-DSPI_FREQUENCY=27000000  # ลดความเร็วเพื่อความเสถียร
-DTFT_INVERSION_ON=1      # เปิด inversion
-DTFT_RGB_ORDER=TFT_BGR   # ใช้ BGR order
```

## 🧪 **ไฟล์ทดสอบใหม่: esp32_2432s028r_test.cpp**

### **การทำงานของไฟล์ทดสอบ:**

#### **Phase 1: Rotation Test (0-3)**
- ทดสอบทุก rotation
- แสดงขนาดจริงของหน้าจอ
- ทดสอบ screen boundaries
- วาดกรอบรอบหน้าจอ

#### **Phase 2: Screen Boundaries Test**
- วาดกรอบขาวรอบขอบหน้าจอ
- วาดสี่เหลี่ยมสีที่มุมทั้ง 4
- วาดเครื่องหมาย + ที่กึ่งกลาง
- ทดสอบ pixel ที่ขอบสุด

#### **Phase 3: Text Positioning Test**
- ทดสอบการวางข้อความที่มุมต่างๆ
- ตรวจสอบการแสดงผลที่ขอบหน้าจอ
- แสดงข้อมูล resolution

#### **Phase 4: Full Screen Pattern**
- วาด gradient bars เต็มหน้าจอ
- วาดเส้นแนวตั้งทุก 40 pixel
- ตรวจสอบว่าได้ 320x240 หรือไม่

## 📊 **ผลลัพธ์ที่คาดหวัง**

### **Resolution ที่ถูกต้อง:**
- **Rotation 0**: 240x320 (Portrait)
- **Rotation 1**: 320x240 (Landscape) ← **ที่ต้องการ**
- **Rotation 2**: 240x320 (Portrait กลับหัว)
- **Rotation 3**: 320x240 (Landscape กลับหัว)

### **หน้าจอ Final Test:**
```
┌─────────────────────────────────────┐
│●                                  ● │ ← Red/Green corners
│ESP32-2432S028R                      │
│Resolution: 320x240                  │
│Full Screen Test                     │
│CORRECT RESOLUTION!                  │
│                                     │
│                                     │
│●                                  ● │ ← Blue/Yellow corners
└─────────────────────────────────────┘
```

### **Loop Phase:**
- Corner markers กะพริบระหว่างสีเดิมกับสีขาว
- ทุก 1 วินาที
- แสดงว่าระบบทำงานต่อเนื่อง

## 🚀 **วิธีใช้งาน**

### **1. Upload Test:**
```bash
python -m platformio run --target upload
```

### **2. สังเกตผลลัพธ์:**

#### **ขั้นตอนที่ 1-4: Rotation Test**
- ดูว่า rotation ไหนที่แสดงผลเต็มหน้าจอ
- ตรวจสอบว่าไม่มี offset
- ดูว่าขนาดเป็น 320x240 หรือไม่

#### **ขั้นตอนที่ 5: Boundary Test**
- ตรวจสอบกรอบขาวรอบขอบ
- ดูว่า corner markers อยู่มุมจริงๆ
- ตรวจสอบ center cross อยู่กึ่งกลาง

#### **ขั้นตอนที่ 6: Text Test**
- ตรวจสอบข้อความที่มุมต่างๆ
- ดูว่าข้อความไม่หายไปนอกหน้าจอ

#### **ขั้นตอนที่ 7: Full Screen Pattern**
- ตรวจสอบ gradient เต็มหน้าจอ
- ดูว่าไม่มีพื้นที่ว่างรอบๆ
- ตรวจสอบข้อความ "CORRECT RESOLUTION!"

## 🔧 **การตั้งค่าสุดท้าย**

### **platformio.ini ที่แก้ไขแล้ว:**
```ini
[env:esp32-2432S028R]
platform = espressif32
board = esp32dev
framework = arduino

build_flags =
    -DCORE_DEBUG_LEVEL=0
    -DARDUINO_USB_CDC_ON_BOOT=0
    -DUSER_SETUP_LOADED=1
    -DILI9341_2_DRIVER=1          # เปลี่ยนเป็น ILI9341_2
    -DTFT_MISO=12
    -DTFT_MOSI=13
    -DTFT_SCLK=14
    -DTFT_CS=15
    -DTFT_DC=2
    -DTFT_RST=-1
    -DTFT_BL=21
    -DTOUCH_CS=33
    -DTFT_ROTATION=1              # Landscape
    -DTFT_OFFSET_X=0              # ไม่มี offset
    -DTFT_OFFSET_Y=0              # ไม่มี offset
    -DSPI_FREQUENCY=27000000      # ลดความเร็ว
    -DTFT_INVERSION_ON=1          # เปิด inversion
    -DTFT_RGB_ORDER=TFT_BGR       # BGR order

lib_deps =
    https://github.com/Bodmer/TFT_eSPI.git
```

## 🎯 **การตรวจสอบผลลัพธ์**

### **✅ หน้าจอถูกต้อง:**
- แสดงผลเต็มหน้าจอ 320x240
- ไม่มี offset หรือเลื่อนตำแหน่ง
- Corner markers อยู่มุมที่ถูกต้อง
- Center cross อยู่กึ่งกลางจริง
- ข้อความแสดง "CORRECT RESOLUTION!"

### **❌ หน้าจอยังผิด:**
- ยังมี offset → ลองปรับ DTFT_OFFSET_X/Y
- ขนาดไม่ถูก → ตรวจสอบ driver
- สีผิด → ปรับ RGB_ORDER หรือ INVERSION

## 🔧 **การปรับแต่งเพิ่มเติม**

### **หากยังมี Offset:**
```ini
# ลอง offset ต่างๆ
-DTFT_OFFSET_X=2
-DTFT_OFFSET_Y=1
```

### **หากขนาดยังผิด:**
```ini
# ลอง driver อื่น
-DILI9341_DRIVER=1    # กลับไปใช้ driver เดิม
```

### **หากสียังผิด:**
```ini
# ลอง inversion
-DTFT_INVERSION_OFF=1  # ปิด inversion

# ลอง RGB order
-DTFT_RGB_ORDER=TFT_RGB  # เปลี่ยนเป็น RGB
```

## 📈 **Build Information**

```
TFT_eSPI @ 2.5.43+sha.5793878 (GitHub Master)
RAM:   [=         ]   6.7% (used 21,908 bytes)
Flash: [==        ]  23.7% (used 310,669 bytes)
Status: SUCCESS ✅
```

## 🎉 **สรุป**

### **การแก้ไขหลัก:**
1. **เปลี่ยนเป็น ILI9341_2_DRIVER** - รองรับ ESP32-2432S028R
2. **ลบ TFT_WIDTH/HEIGHT** - ให้ library กำหนดเอง
3. **เพิ่ม OFFSET settings** - ควบคุมตำแหน่งแสดงผล
4. **ปรับ SPI frequency** - เพื่อความเสถียร
5. **ทดสอบด้วย boundary test** - ตรวจสอบขอบเขต

### **ผลลัพธ์:**
- ✅ **แสดงผลเต็มหน้าจอ** - ไม่มี offset
- ✅ **ขนาดถูกต้อง** - 320x240 landscape
- ✅ **ตำแหน่งถูกต้อง** - corner และ center
- ✅ **ไม่มีการเลื่อน** - แสดงผลตรงตำแหน่ง

**ESP32-2432S028R Display แสดงผลเต็มหน้าจอแล้ว!** 🎉

---

**หมายเหตุ**: หากยังมีปัญหา ให้ลองปรับ DTFT_OFFSET_X และ DTFT_OFFSET_Y เป็นค่าอื่นๆ
