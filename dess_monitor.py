import json
import time
import logging
import requests
import pandas as pd
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from config import Config
import os

class DESSMonitor:
    def __init__(self):
        self.config = Config()
        self.driver = None
        self.session_data = {}
        self.setup_logging()
        self.setup_data_directory()

    def setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=getattr(logging, self.config.LOG_LEVEL),
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.config.LOG_FILE),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def setup_data_directory(self):
        """Create data directory if it doesn't exist"""
        if not os.path.exists(self.config.DATA_SAVE_PATH):
            os.makedirs(self.config.DATA_SAVE_PATH)

    def setup_driver(self):
        """Setup Chrome WebDriver"""
        try:
            chrome_options = Options()
            if self.config.HEADLESS_MODE:
                chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')

            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.logger.info("Chrome WebDriver initialized successfully")
            return True
        except Exception as e:
            self.logger.error(f"Failed to setup Chrome WebDriver: {e}")
            return False

    def login_to_dess(self):
        """Login to DESS Monitor"""
        try:
            self.logger.info("Attempting to login to DESS Monitor...")
            self.driver.get(self.config.DESS_LOGIN_URL)

            # Wait for login form to load
            wait = WebDriverWait(self.driver, 10)

            # Find and fill username
            username_field = wait.until(EC.presence_of_element_located((By.NAME, "username")))
            username_field.clear()
            username_field.send_keys(self.config.DESS_USERNAME)

            # Find and fill password
            password_field = self.driver.find_element(By.NAME, "password")
            password_field.clear()
            password_field.send_keys(self.config.DESS_PASSWORD)

            # Click login button
            login_button = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            login_button.click()

            # Wait for successful login (check for dashboard or redirect)
            time.sleep(5)

            # Check if login was successful
            current_url = self.driver.current_url
            if "login" not in current_url.lower():
                self.logger.info("Login successful")
                return True
            else:
                self.logger.error("Login failed - still on login page")
                return False

        except Exception as e:
            self.logger.error(f"Login failed: {e}")
            return False

    def extract_session_data(self):
        """Extract session data from browser"""
        try:
            # Get cookies
            cookies = self.driver.get_cookies()

            # Extract session information from current page or localStorage
            session_data = self.driver.execute_script("""
                return {
                    token: localStorage.getItem('token') || '',
                    userId: localStorage.getItem('userId') || '',
                    sessionId: localStorage.getItem('sessionId') || ''
                };
            """)

            self.session_data = {
                'cookies': cookies,
                'token': session_data.get('token', ''),
                'userId': session_data.get('userId', ''),
                'sessionId': session_data.get('sessionId', '')
            }

            self.logger.info("Session data extracted successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to extract session data: {e}")
            return False

    def get_device_data_url(self):
        """Generate device data URL with current timestamp and signature"""
        import hashlib
        import time

        # Generate salt (timestamp)
        salt = str(int(time.time() * 1000))

        # Create signature (this might need to be adjusted based on actual API)
        sign_string = f"action=querySPDeviceLastData&devcode={self.config.DEVICE_CODE}&pn={self.config.DEVICE_PN}&salt={salt}"
        sign = hashlib.md5(sign_string.encode()).hexdigest()

        # Build URL
        url = f"{self.config.DESS_WEB_URL}public/?sign={sign}&salt={salt}&token={self.session_data.get('token', '')}&action=querySPDeviceLastData&source=1&devcode={self.config.DEVICE_CODE}&pn={self.config.DEVICE_PN}&devaddr={self.config.DEVICE_ADDR}&sn={self.config.DEVICE_SN}&i18n=en_US"

        return url

    def fetch_device_data(self):
        """Fetch device data using requests with session cookies"""
        try:
            # Prepare cookies for requests
            cookies_dict = {}
            for cookie in self.session_data.get('cookies', []):
                cookies_dict[cookie['name']] = cookie['value']

            # Use the provided URL format (you can modify this to use dynamic generation)
            url = "https://web.dessmonitor.com/public/?sign=8743221c28ad40664baa48193bbf4b03caa726f1&salt=1748162984217&token=c8075906465cca180724b25d151680e31e2267ff1319ccf2b43c88ba979a7576&action=querySPDeviceLastData&source=1&devcode=2376&pn=Q0046526082082&devaddr=1&sn=Q0046526082082094801&i18n=en_US"

            response = requests.get(url, cookies=cookies_dict, timeout=30)

            if response.status_code == 200:
                data = response.json()
                if data.get('err') == 0:
                    self.logger.info("Device data fetched successfully")
                    return data
                else:
                    self.logger.error(f"API error: {data.get('desc', 'Unknown error')}")
                    return None
            else:
                self.logger.error(f"HTTP error: {response.status_code}")
                return None

        except Exception as e:
            self.logger.error(f"Failed to fetch device data: {e}")
            return None

    def parse_device_data(self, raw_data):
        """Parse raw device data into structured format"""
        try:
            if not raw_data or 'dat' not in raw_data:
                return None

            parsed_data = {
                'timestamp': raw_data['dat']['gts'],
                'collected_at': datetime.now().isoformat()
            }

            # Parse different parameter groups
            for group_key, group_data in raw_data['dat']['pars'].items():
                for param in group_data:
                    param_name = param['par'].replace(' ', '_').lower()
                    parsed_data[param_name] = {
                        'value': param['val'],
                        'unit': param.get('unit', ''),
                        'id': param['id']
                    }

            return parsed_data

        except Exception as e:
            self.logger.error(f"Failed to parse device data: {e}")
            return None

    def save_data_to_csv(self, data):
        """Save data to CSV file"""
        try:
            if not data:
                return False

            # Flatten data for CSV
            flat_data = {'timestamp': data['timestamp'], 'collected_at': data['collected_at']}

            for key, value in data.items():
                if isinstance(value, dict) and 'value' in value:
                    flat_data[key] = value['value']
                    flat_data[f"{key}_unit"] = value.get('unit', '')

            # Create DataFrame
            df = pd.DataFrame([flat_data])

            # Save to CSV
            filename = f"{self.config.DATA_SAVE_PATH}/inverter_data_{datetime.now().strftime('%Y%m%d')}.csv"

            # Append to existing file or create new one
            if os.path.exists(filename):
                df.to_csv(filename, mode='a', header=False, index=False, lineterminator='\n')
            else:
                df.to_csv(filename, index=False, lineterminator='\n')

            self.logger.info(f"Data saved to {filename}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to save data to CSV: {e}")
            return False

    def save_data_to_json(self, data):
        """Save data to JSON file"""
        try:
            if not data:
                return False

            filename = f"{self.config.DATA_SAVE_PATH}/inverter_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

            with open(filename, 'w') as f:
                json.dump(data, f, indent=2)

            self.logger.info(f"Data saved to {filename}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to save data to JSON: {e}")
            return False

    def collect_data_once(self):
        """Collect data once"""
        try:
            self.logger.info("Starting data collection...")

            # Fetch raw data
            raw_data = self.fetch_device_data()
            if not raw_data:
                return False

            # Parse data
            parsed_data = self.parse_device_data(raw_data)
            if not parsed_data:
                return False

            # Save data
            self.save_data_to_csv(parsed_data)
            self.save_data_to_json(parsed_data)

            self.logger.info("Data collection completed successfully")
            return True

        except Exception as e:
            self.logger.error(f"Data collection failed: {e}")
            return False

    def run_once(self):
        """Run data collection once"""
        try:
            if not self.setup_driver():
                return False

            if not self.login_to_dess():
                return False

            if not self.extract_session_data():
                return False

            success = self.collect_data_once()

            return success

        finally:
            if self.driver:
                self.driver.quit()

    def cleanup(self):
        """Cleanup resources"""
        if self.driver:
            self.driver.quit()
            self.logger.info("WebDriver closed")
