#include "animations.h"

// Global animation manager instance
AnimationManager animationManager;

AnimationManager::AnimationManager() {
  animationCount = 0;
  globalAnimationsEnabled = true;
  
  // Initialize animation array
  for (int i = 0; i < MAX_ANIMATIONS; i++) {
    animations[i].isActive = false;
    animations[i].target = nullptr;
    animations[i].userData = nullptr;
  }
}

AnimationManager::~AnimationManager() {
  stopAll();
}

void AnimationManager::setEnabled(bool enabled) {
  globalAnimationsEnabled = enabled;
  
  if (!enabled) {
    stopAll();
  }
  
  Serial.printf("🎬 Global animations %s\n", enabled ? "enabled" : "disabled");
}

bool AnimationManager::isEnabled() {
  return globalAnimationsEnabled;
}

void AnimationManager::pauseAll() {
  for (int i = 0; i < MAX_ANIMATIONS; i++) {
    if (animations[i].isActive) {
      lv_anim_del(animations[i].target, nullptr);
    }
  }
  Serial.println("⏸️ All animations paused");
}

void AnimationManager::resumeAll() {
  // TODO: Implement animation resume functionality
  Serial.println("▶️ All animations resumed");
}

void AnimationManager::stopAll() {
  lv_anim_del(nullptr, nullptr); // Stop all animations
  
  for (int i = 0; i < MAX_ANIMATIONS; i++) {
    animations[i].isActive = false;
  }
  
  animationCount = 0;
  Serial.println("⏹️ All animations stopped");
}

void AnimationManager::fadeIn(lv_obj_t* obj, uint32_t duration, uint32_t delay) {
  if (!globalAnimationsEnabled || !obj) return;
  
  lv_anim_t anim;
  lv_anim_init(&anim);
  lv_anim_set_var(&anim, obj);
  lv_anim_set_values(&anim, LV_OPA_TRANSP, LV_OPA_COVER);
  lv_anim_set_time(&anim, duration);
  lv_anim_set_delay(&anim, delay);
  lv_anim_set_exec_cb(&anim, opacityExecCallback);
  lv_anim_set_path_cb(&anim, lv_anim_path_ease_out);
  lv_anim_set_ready_cb(&anim, animationReadyCallback);
  lv_anim_start(&anim);
  
  registerAnimation(anim, obj, AnimationType::FADE_IN);
}

void AnimationManager::fadeOut(lv_obj_t* obj, uint32_t duration, uint32_t delay) {
  if (!globalAnimationsEnabled || !obj) return;
  
  lv_anim_t anim;
  lv_anim_init(&anim);
  lv_anim_set_var(&anim, obj);
  lv_anim_set_values(&anim, LV_OPA_COVER, LV_OPA_TRANSP);
  lv_anim_set_time(&anim, duration);
  lv_anim_set_delay(&anim, delay);
  lv_anim_set_exec_cb(&anim, opacityExecCallback);
  lv_anim_set_path_cb(&anim, lv_anim_path_ease_in);
  lv_anim_set_ready_cb(&anim, animationReadyCallback);
  lv_anim_start(&anim);
  
  registerAnimation(anim, obj, AnimationType::FADE_OUT);
}

void AnimationManager::slideIn(lv_obj_t* obj, lv_dir_t direction, uint32_t duration, uint32_t delay) {
  if (!globalAnimationsEnabled || !obj) return;
  
  lv_coord_t startX = lv_obj_get_x(obj);
  lv_coord_t startY = lv_obj_get_y(obj);
  lv_coord_t endX = startX;
  lv_coord_t endY = startY;
  
  // Calculate start position based on direction
  switch (direction) {
    case LV_DIR_LEFT:
      startX = -lv_obj_get_width(obj);
      break;
    case LV_DIR_RIGHT:
      startX = lv_obj_get_parent(obj) ? lv_obj_get_width(lv_obj_get_parent(obj)) : 320;
      break;
    case LV_DIR_TOP:
      startY = -lv_obj_get_height(obj);
      break;
    case LV_DIR_BOTTOM:
      startY = lv_obj_get_parent(obj) ? lv_obj_get_height(lv_obj_get_parent(obj)) : 240;
      break;
    default:
      return;
  }
  
  // Set initial position
  lv_obj_set_pos(obj, startX, startY);
  
  // Animate to final position
  lv_anim_t anim;
  lv_anim_init(&anim);
  lv_anim_set_var(&anim, obj);
  lv_anim_set_time(&anim, duration);
  lv_anim_set_delay(&anim, delay);
  lv_anim_set_path_cb(&anim, lv_anim_path_ease_out);
  lv_anim_set_ready_cb(&anim, animationReadyCallback);
  
  if (direction == LV_DIR_LEFT || direction == LV_DIR_RIGHT) {
    lv_anim_set_values(&anim, startX, endX);
    lv_anim_set_exec_cb(&anim, xExecCallback);
  } else {
    lv_anim_set_values(&anim, startY, endY);
    lv_anim_set_exec_cb(&anim, yExecCallback);
  }
  
  lv_anim_start(&anim);
  registerAnimation(anim, obj, AnimationType::SLIDE_IN);
}

void AnimationManager::slideOut(lv_obj_t* obj, lv_dir_t direction, uint32_t duration, uint32_t delay) {
  if (!globalAnimationsEnabled || !obj) return;
  
  lv_coord_t startX = lv_obj_get_x(obj);
  lv_coord_t startY = lv_obj_get_y(obj);
  lv_coord_t endX = startX;
  lv_coord_t endY = startY;
  
  // Calculate end position based on direction
  switch (direction) {
    case LV_DIR_LEFT:
      endX = -lv_obj_get_width(obj);
      break;
    case LV_DIR_RIGHT:
      endX = lv_obj_get_parent(obj) ? lv_obj_get_width(lv_obj_get_parent(obj)) : 320;
      break;
    case LV_DIR_TOP:
      endY = -lv_obj_get_height(obj);
      break;
    case LV_DIR_BOTTOM:
      endY = lv_obj_get_parent(obj) ? lv_obj_get_height(lv_obj_get_parent(obj)) : 240;
      break;
    default:
      return;
  }
  
  lv_anim_t anim;
  lv_anim_init(&anim);
  lv_anim_set_var(&anim, obj);
  lv_anim_set_time(&anim, duration);
  lv_anim_set_delay(&anim, delay);
  lv_anim_set_path_cb(&anim, lv_anim_path_ease_in);
  lv_anim_set_ready_cb(&anim, animationReadyCallback);
  
  if (direction == LV_DIR_LEFT || direction == LV_DIR_RIGHT) {
    lv_anim_set_values(&anim, startX, endX);
    lv_anim_set_exec_cb(&anim, xExecCallback);
  } else {
    lv_anim_set_values(&anim, startY, endY);
    lv_anim_set_exec_cb(&anim, yExecCallback);
  }
  
  lv_anim_start(&anim);
  registerAnimation(anim, obj, AnimationType::SLIDE_OUT);
}

void AnimationManager::scaleIn(lv_obj_t* obj, uint32_t duration, uint32_t delay) {
  if (!globalAnimationsEnabled || !obj) return;
  
  lv_anim_t anim;
  lv_anim_init(&anim);
  lv_anim_set_var(&anim, obj);
  lv_anim_set_values(&anim, 0, 256); // 0% to 100% scale
  lv_anim_set_time(&anim, duration);
  lv_anim_set_delay(&anim, delay);
  lv_anim_set_exec_cb(&anim, scaleExecCallback);
  lv_anim_set_path_cb(&anim, lv_anim_path_bounce);
  lv_anim_set_ready_cb(&anim, animationReadyCallback);
  lv_anim_start(&anim);
  
  registerAnimation(anim, obj, AnimationType::SCALE_IN);
}

void AnimationManager::scaleOut(lv_obj_t* obj, uint32_t duration, uint32_t delay) {
  if (!globalAnimationsEnabled || !obj) return;
  
  lv_anim_t anim;
  lv_anim_init(&anim);
  lv_anim_set_var(&anim, obj);
  lv_anim_set_values(&anim, 256, 0); // 100% to 0% scale
  lv_anim_set_time(&anim, duration);
  lv_anim_set_delay(&anim, delay);
  lv_anim_set_exec_cb(&anim, scaleExecCallback);
  lv_anim_set_path_cb(&anim, lv_anim_path_ease_in);
  lv_anim_set_ready_cb(&anim, animationReadyCallback);
  lv_anim_start(&anim);
  
  registerAnimation(anim, obj, AnimationType::SCALE_OUT);
}

void AnimationManager::bounce(lv_obj_t* obj, uint32_t duration) {
  if (!globalAnimationsEnabled || !obj) return;
  
  lv_coord_t originalY = lv_obj_get_y(obj);
  
  lv_anim_t anim;
  lv_anim_init(&anim);
  lv_anim_set_var(&anim, obj);
  lv_anim_set_values(&anim, originalY, originalY - 20);
  lv_anim_set_time(&anim, duration / 2);
  lv_anim_set_exec_cb(&anim, yExecCallback);
  lv_anim_set_path_cb(&anim, lv_anim_path_bounce);
  lv_anim_set_playback_time(&anim, duration / 2);
  lv_anim_set_ready_cb(&anim, animationReadyCallback);
  lv_anim_start(&anim);
  
  registerAnimation(anim, obj, AnimationType::BOUNCE);
}

void AnimationManager::shake(lv_obj_t* obj, uint32_t duration, int16_t amplitude) {
  if (!globalAnimationsEnabled || !obj) return;
  
  lv_coord_t originalX = lv_obj_get_x(obj);
  
  lv_anim_t anim;
  lv_anim_init(&anim);
  lv_anim_set_var(&anim, obj);
  lv_anim_set_values(&anim, originalX - amplitude, originalX + amplitude);
  lv_anim_set_time(&anim, duration / 8);
  lv_anim_set_exec_cb(&anim, xExecCallback);
  lv_anim_set_path_cb(&anim, lv_anim_path_linear);
  lv_anim_set_repeat_count(&anim, 4);
  lv_anim_set_playback_time(&anim, duration / 8);
  lv_anim_set_ready_cb(&anim, animationReadyCallback);
  lv_anim_start(&anim);
  
  registerAnimation(anim, obj, AnimationType::SHAKE);
}

void AnimationManager::pulse(lv_obj_t* obj, uint32_t duration, uint8_t scale) {
  if (!globalAnimationsEnabled || !obj) return;
  
  lv_anim_t anim;
  lv_anim_init(&anim);
  lv_anim_set_var(&anim, obj);
  lv_anim_set_values(&anim, 256, (256 * scale) / 100);
  lv_anim_set_time(&anim, duration / 2);
  lv_anim_set_exec_cb(&anim, scaleExecCallback);
  lv_anim_set_path_cb(&anim, lv_anim_path_ease_in_out);
  lv_anim_set_playback_time(&anim, duration / 2);
  lv_anim_set_repeat_count(&anim, LV_ANIM_REPEAT_INFINITE);
  lv_anim_start(&anim);
  
  registerAnimation(anim, obj, AnimationType::PULSE);
}

bool AnimationManager::isAnimating(lv_obj_t* obj) {
  for (int i = 0; i < MAX_ANIMATIONS; i++) {
    if (animations[i].isActive && animations[i].target == obj) {
      return true;
    }
  }
  return false;
}

void AnimationManager::stopAnimation(lv_obj_t* obj) {
  lv_anim_del(obj, nullptr);
  unregisterAnimation(obj, AnimationType::NONE);
}

uint8_t AnimationManager::getActiveAnimationCount() {
  return animationCount;
}

// Private methods
void AnimationManager::registerAnimation(const lv_anim_t& anim, lv_obj_t* target, AnimationType type) {
  for (int i = 0; i < MAX_ANIMATIONS; i++) {
    if (!animations[i].isActive) {
      animations[i].anim = anim;
      animations[i].target = target;
      animations[i].type = type;
      animations[i].startTime = millis();
      animations[i].duration = lv_anim_get_time(&anim);
      animations[i].isActive = true;
      animationCount++;
      break;
    }
  }
}

void AnimationManager::unregisterAnimation(lv_obj_t* target, AnimationType type) {
  for (int i = 0; i < MAX_ANIMATIONS; i++) {
    if (animations[i].isActive && 
        (target == nullptr || animations[i].target == target) &&
        (type == AnimationType::NONE || animations[i].type == type)) {
      animations[i].isActive = false;
      animationCount--;
    }
  }
}

// Animation callbacks
void AnimationManager::animationReadyCallback(lv_anim_t* anim) {
  animationManager.unregisterAnimation((lv_obj_t*)lv_anim_get_var(anim), AnimationType::NONE);
}

void AnimationManager::animationDeletedCallback(lv_anim_t* anim) {
  animationManager.unregisterAnimation((lv_obj_t*)lv_anim_get_var(anim), AnimationType::NONE);
}

void AnimationManager::animationStartCallback(lv_anim_t* anim) {
  // Animation started
}

// Execution callbacks
void AnimationManager::opacityExecCallback(void* var, int32_t val) {
  lv_obj_set_style_opa((lv_obj_t*)var, val, 0);
}

void AnimationManager::xExecCallback(void* var, int32_t val) {
  lv_obj_set_x((lv_obj_t*)var, val);
}

void AnimationManager::yExecCallback(void* var, int32_t val) {
  lv_obj_set_y((lv_obj_t*)var, val);
}

void AnimationManager::widthExecCallback(void* var, int32_t val) {
  lv_obj_set_width((lv_obj_t*)var, val);
}

void AnimationManager::heightExecCallback(void* var, int32_t val) {
  lv_obj_set_height((lv_obj_t*)var, val);
}

void AnimationManager::scaleExecCallback(void* var, int32_t val) {
  lv_obj_set_style_transform_zoom((lv_obj_t*)var, val, 0);
}

void AnimationManager::rotateExecCallback(void* var, int32_t val) {
  lv_obj_set_style_transform_angle((lv_obj_t*)var, val, 0);
}

void AnimationManager::colorExecCallback(void* var, int32_t val) {
  lv_obj_set_style_bg_color((lv_obj_t*)var, lv_color_hex(val), 0);
}

// Global functions
void initializeAnimations() {
  Serial.println("🎬 Initializing Animation Manager...");
  animationManager.setEnabled(true);
  Serial.println("✅ Animation Manager initialized");
}

lv_anim_t createBaseAnimation(lv_obj_t* obj, uint32_t duration, uint32_t delay) {
  lv_anim_t anim;
  lv_anim_init(&anim);
  lv_anim_set_var(&anim, obj);
  lv_anim_set_time(&anim, duration);
  lv_anim_set_delay(&anim, delay);
  lv_anim_set_path_cb(&anim, lv_anim_path_ease_out);
  return anim;
}
