# 📱 ESP32-2432S028 DESS Monitor Display Project

## 🎯 **โปรเจคสำเร็จแล้ว!**

ได้สร้างระบบแสดงข้อมูลอินเวอร์เตอร์บนหน้าจอ ESP32-2432S028 เรียบร้อยแล้ว

## 📁 **ไฟล์ที่สร้าง:**

```
esp32_display/
├── platformio.ini              # การตั้งค่า PlatformIO
├── src/
│   ├── main.cpp               # โค้ดหลัก ESP32
│   └── config.h               # ไฟล์ configuration
├── examples/
│   └── advanced_display.cpp   # ตัวอย่างหน้าจอขั้นสูง
├── build.bat                  # Script สำหรับ build
├── upload.bat                 # Script สำหรับ upload
├── wifi_config.example        # ตัวอย่างการตั้งค่า WiFi
└── README.md                  # คู่มือการใช้งาน
```

## 🔧 **Hardware: ESP32-2432S028**

- **MCU**: ESP32-WROOM-32
- **Display**: 2.8" TFT LCD 320x240 pixels
- **Touch**: Resistive touch screen
- **WiFi**: Built-in 802.11 b/g/n
- **Power**: 5V via USB or external

## 📊 **Features ที่ได้:**

### 🎨 **หน้าจอหลัก:**
- **Header**: ชื่อระบบ (สีฟ้า)
- **Timestamp**: เวลาข้อมูลล่าสุด (สีเหลือง)
- **Operating Mode**: โหมดการทำงาน (สีเขียว)

### ⚡ **ข้อมูลพลังงาน:**
- **PV Power**: กำลังไฟจากโซลาร์ (สีส้ม)
- **Output Power**: กำลังไฟที่ใช้งาน
- **Load Percent**: เปอร์เซ็นต์โหลด

### 🔌 **ข้อมูลแรงดัน:**
- **Grid Voltage**: แรงดันไฟฟ้าหลัก (สีฟ้า)
- **PV Voltage**: แรงดันโซลาร์เซลล์
- **Battery Voltage**: แรงดันแบตเตอรี่

### 🔋 **ข้อมูลแบตเตอรี่:**
- **Battery Current**: กระแสแบตเตอรี่
  - สีเขียว = กำลังชาร์จ
  - สีแดง = กำลังดิสชาร์จ

### 🌡️ **ข้อมูลอุณหภูมิ:**
- **DC Module Temperature**: อุณหภูมิโมดูล DC (สีม่วง)
- **INV Module Temperature**: อุณหภูมิโมดูล Inverter

### 📡 **Status Bar:**
- **WiFi Status**: สถานะการเชื่อมต่อ (จุดเขียว/แดง)
- **Last Update**: เวลาที่อัปเดตล่าสุด
- **Power Flow**: สถานะการไหลของพลังงาน

## 🚀 **การใช้งาน:**

### 1. **ติดตั้ง PlatformIO:**
```bash
pip install platformio
```

### 2. **แก้ไขการตั้งค่า:**
```cpp
// ใน src/main.cpp
const char* ssid = "YOUR_WIFI_SSID";
const char* password = "YOUR_WIFI_PASSWORD";
```

### 3. **Build และ Upload:**
```bash
cd esp32_display
pio run                    # Build
pio run --target upload    # Upload to ESP32
pio device monitor         # Monitor serial output
```

### 4. **หรือใช้ไฟล์ .bat:**
```bash
build.bat      # Build project
upload.bat     # Upload and monitor
```

## 📈 **ข้อมูลที่แสดงแบบ Real-time:**

จากการทดสอบล่าสุด (2025-05-25 16:05:55):
- **PV Power**: 2,029 W
- **Output Power**: 2,224 W
- **Battery Current**: -3.5 A (ดิสชาร์จ)
- **Load**: 38%
- **Battery Voltage**: 52.6 V
- **Operating Mode**: Off-Grid Mode

## 🎨 **การแสดงผลแบบสี:**

| ข้อมูล | สี | ความหมาย |
|--------|-----|-----------|
| Header | ฟ้า (Cyan) | ชื่อระบบ |
| Timestamp | เหลือง (Yellow) | เวลาข้อมูล |
| Mode | เขียว (Green) | โหมดการทำงาน |
| Power | ส้ม (Orange) | ข้อมูลพลังงาน |
| Voltage | ฟ้า (Cyan) | ข้อมูลแรงดัน |
| Charging | เขียว (Green) | กำลังชาร์จ |
| Discharging | แดง (Red) | กำลังดิสชาร์จ |
| Temperature | ม่วง (Magenta) | อุณหภูมิ |

## 🔄 **การอัปเดตข้อมูล:**

- **ความถี่**: ทุก 30 วินาที
- **การแสดงผล**: ทุก 1 วินาที
- **การเชื่อมต่อ**: Auto-reconnect WiFi
- **Error Handling**: แสดงสถานะข้อผิดพลาด

## 🛠️ **การปรับแต่งเพิ่มเติม:**

### เปลี่ยนความถี่อัปเดต:
```cpp
const unsigned long updateInterval = 30000; // 30 วินาที
```

### เพิ่มข้อมูลใหม่:
1. เพิ่มตัวแปรใน `InverterData` struct
2. เพิ่มการ parse ใน `parseInverterData()`
3. เพิ่มการแสดงผลใน `updateDisplay()`

### เปลี่ยนสี:
```cpp
#define TFT_CUSTOM_COLOR 0x07E0  // RGB565 format
```

## 📱 **ตัวอย่างหน้าจอขั้นสูง:**

ในไฟล์ `examples/advanced_display.cpp` มี:
- **กราฟแสดงประวัติพลังงาน**
- **แผนภาพการไหลของพลังงาน**
- **ตัวบ่งชี้แบตเตอรี่แบบกราฟิก**
- **ระบบแจ้งเตือน**
- **หน้าจอหลายแบบ (Auto-rotate)**

## 🔧 **Troubleshooting:**

### หน้าจอไม่แสดงผล:
- ตรวจสอบการเชื่อมต่อสาย
- ตรวจสอบ pin configuration

### WiFi เชื่อมต่อไม่ได้:
- ตรวจสอบ SSID และ Password
- ตรวจสอบสัญญาณ WiFi

### ไม่มีข้อมูลจาก API:
- ตรวจสอบ API URL
- ตรวจสอบ token หมดอายุหรือไม่

## 🎉 **สรุป:**

ระบบพร้อมใช้งานแล้ว! ESP32-2432S028 จะแสดงข้อมูลอินเวอร์เตอร์แบบ real-time บนหน้าจอสีสวยงาม พร้อมระบบแจ้งเตือนและการแสดงผลที่เข้าใจง่าย

**ขั้นตอนสุดท้าย:**
1. แก้ไข WiFi credentials ใน `main.cpp`
2. Build และ upload ไปยัง ESP32
3. เพลิดเพลินกับการมอนิเตอร์อินเวอร์เตอร์!
