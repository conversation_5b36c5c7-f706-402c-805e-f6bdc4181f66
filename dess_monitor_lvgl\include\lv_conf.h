#ifndef LV_CONF_H
#define LV_CONF_H

/*====================
   COLOR SETTINGS
 *====================*/

/* Color depth: 1 (1 byte per pixel), 8 (RGB332), 16 (RGB565), 32 (ARGB8888) */
#define LV_COLOR_DEPTH 16

/* Swap the 2 bytes of RGB565 color. Useful if the display has an 8-bit interface (e.g. SPI) */
#define LV_COLOR_16_SWAP 1

/* Enable more complex drawing routines to manage screens transparency */
#define LV_COLOR_SCREEN_TRANSP 0

/* Images pixels with this color will not be drawn if they are chroma keyed) */
#define LV_COLOR_CHROMA_KEY lv_color_hex(0x00ff00)

/*=========================
   MEMORY SETTINGS
 *=========================*/

/* Size of the memory available for `lv_malloc()` in bytes (>= 2kB) */
#define LV_MEM_SIZE (64U * 1024U)          /* [bytes] */

/* Set an address for the memory pool instead of allocating it as a normal array */
#define LV_MEM_ADR 0     /* 0: unused */

/* Instead of an address give a memory allocator that will be called to get a memory pool for LVGL */
#define LV_MEM_CUSTOM 0  /* 1: use custom malloc/free, 0: use the built-in `lv_mem_alloc()` and `lv_mem_free()` */

/*====================
   HAL SETTINGS
 *====================*/

/* Default display refresh period. LVG will redraw changed areas with this period time */
#define LV_DISP_DEF_REFR_PERIOD 16    /* [ms] for 60 FPS */

/* Input device read period in milliseconds */
#define LV_INDEV_DEF_READ_PERIOD 30   /* [ms] */

/* Use a custom tick source that tells the elapsed time in milliseconds */
#define LV_TICK_CUSTOM 1
#if LV_TICK_CUSTOM
    #define LV_TICK_CUSTOM_INCLUDE "Arduino.h"         /* Header for the system time function */
    #define LV_TICK_CUSTOM_SYS_TIME_EXPR (millis())    /* Expression evaluating to current system time in ms */
#endif   /* LV_TICK_CUSTOM */

/*================
 * FEATURE USAGE
 *================*/

/* Enable the Animations */
#define LV_USE_ANIMATION 1
#if LV_USE_ANIMATION
    /* Declare the type of the user data of animations (can be `void *`, `int`, `struct`) */
    typedef void * lv_anim_user_data_t;
    #define LV_ANIM_RESOLUTION 1024 /* [px] */
    #define LV_ANIM_PATH_EASE_IN_OUT 1
    #define LV_ANIM_PATH_OVERSHOOT 1
    #define LV_ANIM_PATH_BOUNCE 1
#endif

/* Enable shadow drawing */
#define LV_USE_SHADOW 1
#if LV_USE_SHADOW
    /* Allow buffering some shadow calculation. LV_SHADOW_CACHE_SIZE is the max shadow size to buffer */
    #define LV_SHADOW_CACHE_SIZE 0
#endif

/* Enable outline drawing */
#define LV_USE_OUTLINE 1

/* Enable pattern drawing */
#define LV_USE_PATTERN 1

/* Enable value string drawing */
#define LV_USE_VALUE_STR 1

/* Use blend modes (with software renderer) */
#define LV_USE_BLEND_MODES 1

/* Use the GPU of the ESP32 */
#define LV_USE_GPU_ESP32_PSRAM_WORKAROUND 1

/*==================
 *  LV OBJ SETTINGS
 *==================*/

#define LV_USE_USER_DATA 1

/* Enable `lv_obj_realign()` based on `lv_obj_align()` parameters */
#define LV_USE_OBJ_REALIGN 1

/* Enable to make the object clickable on a larger area */
#define LV_USE_EXT_CLICK_AREA 1

/*==================
 *  LV OBJ X USAGE
 *==================*/

/* Documentation of the object types: https://docs.lvgl.io/latest/en/html/widgets/index.html */

#define LV_USE_LABEL 1
#if LV_USE_LABEL
    #define LV_LABEL_DEF_SCROLL_SPEED 25     /* Hor, or ver. scroll speed [px/sec] in 'LV_LABEL_LONG_ROLL/ROLL_CIRC' mode */
    #define LV_LABEL_WAIT_CHAR_COUNT 3       /* Waiting period at beginning/end of animation cycle */
    #define LV_LABEL_TEXT_SELECTION 1        /* Enable selecting text of the label */
    #define LV_LABEL_LONG_TXT_HINT 1         /* Store some extra info in labels to speed up drawing of very long texts */
#endif

#define LV_USE_IMG 1   /* Requires: lv_label */

#define LV_USE_LINE 1

#define LV_USE_ARC 1

#define LV_USE_SPINNER 1
#if LV_USE_SPINNER
    #define LV_SPINNER_DEF_ARC_LENGTH 60      /* [deg] */
    #define LV_SPINNER_DEF_SPIN_TIME 1000     /* [ms] */
    #define LV_SPINNER_DEF_ANIM LV_ANIM_TYPE_SPIN_ARC
#endif

#define LV_USE_SLIDER 1   /* Requires: lv_bar */

#define LV_USE_SWITCH 1

#define LV_USE_TEXTAREA 1
#if LV_USE_TEXTAREA != 0
    #define LV_TEXTAREA_DEF_PWD_SHOW_TIME 1500    /* [ms] */
#endif

#define LV_USE_TABLE 1

/*==================
 * Non-user section
 *==================*/

#if defined(_MSC_VER) && !defined(_CRT_SECURE_NO_WARNINGS)    /* Disable warnings for Visual Studio*/
    #define _CRT_SECURE_NO_WARNINGS
#endif

/*--END OF LV_CONF_H--*/

#endif /*LV_CONF_H*/
