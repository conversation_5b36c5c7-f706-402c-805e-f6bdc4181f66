# ESP32-2432S028R DESS Monitor

โปรเจค ESP32 สำหรับแสดงข้อมูลอินเวอร์เตอร์จาก DESS Monitor บนหน้าจอ TFT 2.8"

## 🔧 Hardware Requirements

- **ESP32-2432S028R** (ESP32 with 2.8" TFT Display)
- **WiFi Connection**
- **Power Supply** (USB-C หรือ 5V)

## 📋 Features

- ✅ **Real-time Data Display** - แสดงข้อมูลอินเวอร์เตอร์แบบ real-time
- ✅ **WiFi Manager** - ตั้งค่า WiFi ผ่าน Web Portal
- ✅ **Auto Reconnect** - เชื่อมต่อ WiFi อัตโนมัติเมื่อขาดการเชื่อมต่อ
- ✅ **Color-coded Display** - แสดงข้อมูลด้วยสีที่แตกต่างกัน
- ✅ **Error Handling** - จัดการข้อผิดพลาดและแสดงสถานะ
- ✅ **Low Power** - ใช้พลังงานต่ำ

## 📊 Data Displayed

### Solar Panel
- Voltage (V)
- Current (A)  
- Power (W)

### Battery
- Voltage (V)
- Current (A) - แสดงสีเขียวเมื่อชาร์จ, สีส้มเมื่อดิสชาร์จ
- Battery Level (%)

### Grid
- Voltage (V)
- Frequency (Hz)

### Output
- Voltage (V)
- Power (W)
- Load (%)

### System
- Operating Mode
- DC Temperature (°C)
- Inverter Temperature (°C)
- WiFi Signal Strength

## 🚀 Installation

### 1. PlatformIO Setup

```bash
# Clone or create project
mkdir esp32-dess-monitor
cd esp32-dess-monitor

# Initialize PlatformIO project
pio init --board esp32dev

# Copy files to project directory
```

### 2. Configuration

แก้ไขไฟล์ `include/config.h`:

```cpp
// แก้ไข API URL ของคุณ
#define DEFAULT_API_URL "https://web.dessmonitor.com/public/?sign=YOUR_SIGN&salt=YOUR_SALT&token=YOUR_TOKEN&action=querySPDeviceLastData&source=1&devcode=YOUR_DEVCODE&pn=YOUR_PN&devaddr=YOUR_DEVADDR&sn=YOUR_SN&i18n=en_US"

// แก้ไขข้อมูล Device
#define DEVICE_CODE "YOUR_DEVICE_CODE"
#define DEVICE_PN "YOUR_DEVICE_PN"
#define DEVICE_ADDR "YOUR_DEVICE_ADDR"
#define DEVICE_SN "YOUR_DEVICE_SN"
```

### 3. Build and Upload

```bash
# Build project
pio run

# Upload to ESP32
pio run --target upload

# Monitor serial output
pio device monitor
```

## 📱 WiFi Setup

1. **First Boot**: ESP32 จะสร้าง Access Point ชื่อ "ESP32-DESS"
2. **Connect**: เชื่อมต่อกับ AP ด้วยมือถือหรือคอมพิวเตอร์
3. **Configure**: เปิดเบราว์เซอร์ไปที่ `192.168.4.1`
4. **Enter WiFi**: ใส่ชื่อและรหัสผ่าน WiFi ของคุณ
5. **Save**: บันทึกและ ESP32 จะรีสตาร์ท

## 🎨 Display Layout

```
┌─────────────────────────────────────┐
│ DESS Monitor              ●         │ ← Header + Status
│ Last Update: 16:05:55               │
├─────────────────────────────────────┤
│ SOLAR    BATTERY   GRID    OUTPUT   │
│ 281.0V   52.6V     229.5V  228.5V   │ ← Main Data
│ 7.1A     -3.5A     50.07Hz 2224W    │
│ 2029W    85%                38%     │
├─────────────────────────────────────┤
│ SYSTEM                              │
│ Off-Grid Mode                       │ ← System Info
│ DC:35°C  INV:42°C                   │
│                        WiFi:-45dBm  │ ← WiFi Status
└─────────────────────────────────────┘
```

## 🔧 Pin Configuration

ESP32-2432S028R มีการเชื่อมต่อดังนี้:

```cpp
#define TFT_MISO 12
#define TFT_MOSI 13
#define TFT_SCLK 14
#define TFT_CS   15
#define TFT_DC   2
#define TFT_RST  -1
#define TFT_BL   21
#define TOUCH_CS 33
```

## 📚 Code Structure

```
src/
├── main.cpp          # Main application
├── display.cpp       # Display management
└── api_client.cpp    # API communication

include/
├── config.h          # Configuration
├── data_types.h      # Data structures
├── display.h         # Display header
└── api_client.h      # API client header

platformio.ini        # PlatformIO configuration
```

## 🔍 Troubleshooting

### WiFi Issues
- ตรวจสอบว่า ESP32 อยู่ในระยะ WiFi
- ลองรีเซ็ต WiFi settings โดยกดปุ่ม BOOT ค้างไว้ขณะเปิดเครื่อง
- ตรวจสอบ Serial Monitor สำหรับข้อผิดพลาด

### Display Issues
- ตรวจสอบการเชื่อมต่อสาย
- ตรวจสอบ TFT_eSPI configuration
- ลองปรับ SPI frequency ใน platformio.ini

### API Issues
- ตรวจสอบ API URL ใน config.h
- ตรวจสอบ internet connection
- ดู Serial Monitor สำหรับ HTTP error codes

### Memory Issues
- ลดขนาด JSON buffer ใน api_client.cpp
- ปิด debug messages โดยตั้ง DEBUG_SERIAL = 0

## 📈 Performance

- **Boot Time**: ~10 วินาที
- **Data Refresh**: ทุก 30 วินาที
- **Display Update**: ทุก 2 วินาที
- **Memory Usage**: ~60KB RAM
- **Power Consumption**: ~150mA @ 5V

## 🔄 Updates

### การอัพเดท API URL
1. เชื่อมต่อ Serial Monitor
2. รีสตาร์ท ESP32
3. ใช้ WiFiManager เพื่อเปลี่ยน URL (ถ้ามี custom parameter)

### การอัพเดท Firmware
```bash
pio run --target upload
```

## 📝 Customization

### เปลี่ยนสี
แก้ไขใน `include/config.h`:
```cpp
#define COLOR_SOLAR      0xFFE0  // Yellow
#define COLOR_BATTERY    0x07E0  // Green
// ... etc
```

### เปลี่ยน Refresh Rate
แก้ไขใน `include/config.h`:
```cpp
#define DATA_REFRESH_INTERVAL 30000  // 30 seconds
#define DISPLAY_UPDATE_INTERVAL 2000  // 2 seconds
```

### เพิ่มข้อมูล
1. เพิ่ม field ใน `InverterData` struct
2. เพิ่ม parsing logic ใน `api_client.cpp`
3. เพิ่ม display logic ใน `display.cpp`

## 📄 License

MIT License - ใช้งานได้อย่างอิสระ

## 🤝 Contributing

1. Fork the project
2. Create feature branch
3. Commit changes
4. Push to branch
5. Open Pull Request

## 📞 Support

หากมีปัญหาหรือข้อสงสัย:
1. ตรวจสอบ Serial Monitor output
2. ดู Troubleshooting section
3. สร้าง Issue ใน GitHub repository
