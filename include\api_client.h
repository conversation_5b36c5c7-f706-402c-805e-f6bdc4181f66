#ifndef API_CLIENT_H
#define API_CLIENT_H

#include <Arduino.h>
#include <WiFi.h>
#include <HTTPClient.h>
#include <ArduinoJson.h>
#include "data_types.h"

class APIClient {
private:
  HTTPClient http;
  String apiURL;
  String lastError;

  bool parseJSON(const String& json, InverterData& data);
  void parseGridData(JsonObject& pars, InverterData& data);
  void parsePVData(JsonObject& pars, InverterData& data);
  void parseBatteryData(JsonObject& pars, InverterData& data);
  void parseOutputData(JsonObject& pars, InverterData& data);
  void parseSystemData(JsonObject& pars, InverterData& data);

public:
  APIClient();

  bool begin(const String& url);
  bool fetchData(InverterData& data);
  String getLastError() const;
  void setURL(const String& url);
};

#endif // API_CLIENT_H
