# DESS Monitor Automated Data Collector

ระบบดึงข้อมูลอัตโนมัติจาก DESS Monitor โดยใช้ ChromeDriver สำหรับเก็บข้อมูลอินเวอร์เตอร์แบบ real-time

## คุณสมบัติ

- 🔄 ดึงข้อมูลแบบอัตโนมัติจาก DESS Monitor
- 📊 บันทึกข้อมูลในรูปแบบ CSV และ JSON
- ⏰ รองรับการทำงานแบบ scheduled (ทุก 5 นาที)
- 📈 วิเคราะห์และสร้างกราฟข้อมูล
- 🔒 ล็อกอินอัตโนมัติผ่าน ChromeDriver
- 📋 สร้างรายงานสรุปแบบ Excel

## การติดตั้ง

### 1. ติดตั้ง Dependencies

```bash
pip install -r requirements.txt
```

### 2. ตั้งค่า Environment Variables

สร้างไฟล์ `.env` จาก `.env.example`:

```bash
cp .env.example .env
```

แก้ไขไฟล์ `.env` และใส่ข้อมูลการล็อกอิน:

```
DESS_USERNAME=your_username_here
DESS_PASSWORD=your_password_here
```

### 3. ตั้งค่า Device Information

แก้ไขไฟล์ `config.py` และใส่ข้อมูล device ของคุณ:

```python
DEVICE_CODE = '2376'
DEVICE_PN = 'Q0046526082082'
DEVICE_ADDR = '1'
DEVICE_SN = 'Q0046526082082094801'
```

## การใช้งาน

### 1. ดึงข้อมูลครั้งเดียว

```bash
python main.py --mode once
```

### 2. ดึงข้อมูลแบบต่อเนื่อง (ทุก 5 นาที)

```bash
python main.py --mode continuous
```

### 3. ตั้งค่า interval เอง

```bash
python main.py --mode continuous --interval 300
```

### 4. รันแบบไม่แสดง browser (headless)

```bash
python main.py --mode continuous --headless
```

### 5. วิเคราะห์ข้อมูล

```bash
python data_analyzer.py
```

## โครงสร้างไฟล์

```
├── main.py              # ไฟล์หลักสำหรับรันโปรแกรม
├── dess_monitor.py      # คลาสหลักสำหรับดึงข้อมูล
├── scheduler.py         # ระบบ scheduler สำหรับรันต่อเนื่อง
├── data_analyzer.py     # วิเคราะห์และสร้างกราฟ
├── config.py           # การตั้งค่า
├── requirements.txt    # Dependencies
├── .env.example       # ตัวอย่างไฟล์ environment
└── inverter_data/     # โฟลเดอร์เก็บข้อมูล
    ├── inverter_data_20250525.csv
    ├── inverter_data_20250525_143022.json
    └── summary_report_20250525.xlsx
```

## ข้อมูลที่เก็บ

- **Grid**: แรงดัน, ความถี่, กำลังไฟ
- **PV**: แรงดัน, กระแส, กำลังไฟ
- **Battery**: แรงดัน, กระแส
- **Output**: แรงดัน, กระแส, กำลังไฟ, โหลด
- **System**: อุณหภูมิ, โหมดการทำงาน

## การตั้งค่าเพิ่มเติม

### ปรับ Collection Interval

แก้ไขใน `config.py`:

```python
COLLECTION_INTERVAL = 300  # วินาที (5 นาที)
```

### เปลี่ยนโฟลเดอร์เก็บข้อมูล

```python
DATA_SAVE_PATH = 'my_inverter_data'
```

### ปิด Headless Mode (แสดง browser)

```python
HEADLESS_MODE = False
```

## การแก้ไขปัญหา

### 1. ChromeDriver ไม่ทำงาน

- ตรวจสอบว่าติดตั้ง Chrome browser แล้ว
- ลองรันแบบไม่ headless เพื่อดูข้อผิดพลาด

### 2. Login ไม่สำเร็จ

- ตรวจสอบ username/password ใน `.env`
- ตรวจสอบว่าเว็บไซต์ DESS Monitor เข้าได้ปกติ

### 3. ข้อมูลไม่ถูกต้อง

- ตรวจสอบ Device Code, PN, SN ใน `config.py`
- ดูใน log file เพื่อหาข้อผิดพลาด

## Log Files

- `inverter_monitor.log` - Log การดึงข้อมูล
- `scheduler.log` - Log ของ scheduler

## ตัวอย่างการใช้งานขั้นสูง

### รันเป็น Service บน Linux

สร้างไฟล์ `/etc/systemd/system/dess-monitor.service`:

```ini
[Unit]
Description=DESS Monitor Data Collector
After=network.target

[Service]
Type=simple
User=your_user
WorkingDirectory=/path/to/dess-monitor
ExecStart=/usr/bin/python3 main.py --mode continuous
Restart=always

[Install]
WantedBy=multi-user.target
```

เปิดใช้งาน:

```bash
sudo systemctl enable dess-monitor
sudo systemctl start dess-monitor
```

### Integration กับ Home Assistant

ใช้ข้อมูลจาก CSV files ใน Home Assistant:

```yaml
sensor:
  - platform: file
    name: "Inverter PV Power"
    file_path: "/path/to/inverter_data/latest.csv"
    value_template: "{{ value.split(',')[2] }}"
    unit_of_measurement: "W"
```

## License

MIT License
