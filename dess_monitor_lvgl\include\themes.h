#ifndef THEMES_H
#define THEMES_H

#include <lvgl.h>
#include "data_types.h"

// Theme color structure
struct ThemeColors {
  // Background colors
  lv_color_t background;
  lv_color_t surface;
  lv_color_t card;
  
  // Text colors
  lv_color_t textPrimary;
  lv_color_t textSecondary;
  lv_color_t textDisabled;
  
  // Accent colors
  lv_color_t primary;
  lv_color_t secondary;
  lv_color_t accent;
  
  // Status colors
  lv_color_t success;
  lv_color_t warning;
  lv_color_t error;
  lv_color_t info;
  
  // Component colors
  lv_color_t solar;
  lv_color_t battery;
  lv_color_t grid;
  lv_color_t load;
  
  // UI element colors
  lv_color_t border;
  lv_color_t shadow;
  lv_color_t highlight;
  lv_color_t disabled;
};

// Theme configuration structure
struct ThemeConfig {
  const char* name;
  const char* icon;
  ThemeColors colors;
  bool isDark;
  uint8_t cardRadius;
  uint8_t shadowSize;
  uint8_t borderWidth;
  lv_opa_t shadowOpacity;
};

// Theme manager class
class ThemeManager {
private:
  ThemeType currentTheme;
  ThemeConfig themes[5]; // Support for 5 themes
  lv_theme_t* lvglTheme;
  bool autoThemeEnabled;
  uint32_t lastThemeChange;
  
public:
  ThemeManager();
  ~ThemeManager();
  
  // Initialization
  void initialize();
  void setupThemes();
  
  // Theme management
  void setTheme(ThemeType theme);
  ThemeType getCurrentTheme();
  const ThemeConfig& getCurrentThemeConfig();
  const ThemeColors& getCurrentColors();
  
  // Theme switching
  void nextTheme();
  void previousTheme();
  void toggleDarkLight();
  
  // Auto theme
  void enableAutoTheme(bool enabled);
  bool isAutoThemeEnabled();
  void updateAutoTheme(); // Based on time or light sensor
  
  // Theme application
  void applyTheme();
  void applyToObject(lv_obj_t* obj);
  void applyCardStyle(lv_obj_t* card, lv_color_t accentColor);
  void applyButtonStyle(lv_obj_t* button, bool isPrimary = false);
  void applyLabelStyle(lv_obj_t* label, bool isSecondary = false);
  
  // Color utilities
  lv_color_t getComponentColor(const char* component);
  lv_color_t getStatusColor(const char* status);
  lv_color_t blendColors(lv_color_t color1, lv_color_t color2, uint8_t ratio);
  lv_color_t adjustBrightness(lv_color_t color, int8_t adjustment);
  
  // Animation support
  void animateThemeChange(uint32_t duration = 500);
  
private:
  // Theme definitions
  void setupDarkTheme();
  void setupLightTheme();
  void setupSolarTheme();
  void setupBlueTheme();
  void setupGreenTheme();
  
  // LVGL theme callbacks
  static void themeApplyCallback(lv_theme_t* th, lv_obj_t* obj);
  static void colorMixCallback(const lv_color_filter_dsc_t* dsc, lv_color_t color, lv_opa_t opa);
};

// Predefined theme configurations
extern const ThemeConfig DARK_THEME;
extern const ThemeConfig LIGHT_THEME;
extern const ThemeConfig SOLAR_THEME;
extern const ThemeConfig BLUE_THEME;
extern const ThemeConfig GREEN_THEME;

// Global theme manager instance
extern ThemeManager themeManager;

// Theme utility functions
lv_style_t* createCardStyle(const ThemeColors& colors, uint8_t radius = 12);
lv_style_t* createButtonStyle(const ThemeColors& colors, bool isPrimary = false);
lv_style_t* createLabelStyle(const ThemeColors& colors, bool isSecondary = false);
lv_style_t* createProgressBarStyle(const ThemeColors& colors, lv_color_t fillColor);

// Color conversion utilities
lv_color_t hexToLvColor(uint32_t hex);
uint32_t lvColorToHex(lv_color_t color);
lv_color_t rgbToLvColor(uint8_t r, uint8_t g, uint8_t b);
void lvColorToRgb(lv_color_t color, uint8_t& r, uint8_t& g, uint8_t& b);

// Theme animation callbacks
void themeTransitionCallback(void* var, int32_t val);
void themeTransitionReadyCallback(lv_anim_t* anim);

// Theme persistence
void saveThemeSettings();
void loadThemeSettings();

#endif // THEMES_H
