#ifndef CONFIG_H
#define CONFIG_H

#include <Arduino.h>

// Hardware Configuration
#define TFT_WIDTH  320
#define TFT_HEIGHT 240

// Pin definitions for ESP32-2432S028R
#define TFT_MISO   12
#define TFT_MOSI   13
#define TFT_SCLK   14
#define TFT_CS     15
#define TFT_DC     2
#define TFT_RST    4
#define TFT_BL     21

// Touch screen pins
#define XPT2046_IRQ  36
#define XPT2046_MOSI 32
#define XPT2046_MISO 39
#define XPT2046_CLK  25
#define XPT2046_CS   33

// Other pins
#define BOOT_BUTTON  0
#define LDR_PIN      34
#define SPEAKER_PIN  26

// Network Configuration
#define WIFI_TIMEOUT 30000
#define HTTP_TIMEOUT 10000
#define DATA_UPDATE_INTERVAL 10000
#define DISPLAY_UPDATE_INTERVAL 100

// API Configuration
extern const char* API_BASE_URL;
extern const char* DEVICE_CODE;
extern const char* DEVICE_SN;

// Touch calibration
#define TOUCH_CALIBRATION_X1 200
#define TOUCH_CALIBRATION_Y1 200
#define TOUCH_CALIBRATION_X2 3700
#define TOUCH_CALIBRATION_Y2 3700

// Animation settings
#define ANIM_DURATION_FAST   200
#define ANIM_DURATION_NORMAL 400
#define ANIM_DURATION_SLOW   800

// Theme colors (RGB565)
#define COLOR_PRIMARY     0x1E88E5  // Blue
#define COLOR_SECONDARY   0x43A047  // Green
#define COLOR_ACCENT      0xFF9800  // Orange
#define COLOR_ERROR       0xF44336  // Red
#define COLOR_WARNING     0xFFC107  // Amber
#define COLOR_SUCCESS     0x4CAF50  // Green
#define COLOR_INFO        0x2196F3  // Light Blue

#define COLOR_BACKGROUND  0x121212  // Dark Gray
#define COLOR_SURFACE     0x1E1E1E  // Darker Gray
#define COLOR_ON_PRIMARY  0xFFFFFF  // White
#define COLOR_ON_SURFACE  0xE0E0E0  // Light Gray

// Solar theme colors
#define COLOR_SOLAR       0xFFC107  // Solar Yellow
#define COLOR_BATTERY     0x4CAF50  // Battery Green
#define COLOR_GRID        0x2196F3  // Grid Blue
#define COLOR_LOAD        0xFF5722  // Load Orange

// Data validation
#define MAX_VOLTAGE       300.0f
#define MAX_CURRENT       100.0f
#define MAX_POWER         10000.0f
#define MAX_FREQUENCY     70.0f
#define MIN_FREQUENCY     40.0f
#define MAX_TEMPERATURE   100
#define MIN_TEMPERATURE   -20

// Display settings
#define CARD_RADIUS       12
#define CARD_SHADOW       8
#define ANIMATION_CURVE   LV_ANIM_PATH_EASE_OUT

// Emoji support
#define EMOJI_SOLAR       "☀️"
#define EMOJI_BATTERY     "🔋"
#define EMOJI_GRID        "⚡"
#define EMOJI_LOAD        "🏠"
#define EMOJI_WIFI        "📶"
#define EMOJI_SETTINGS    "⚙️"
#define EMOJI_INFO        "ℹ️"
#define EMOJI_WARNING     "⚠️"
#define EMOJI_ERROR       "❌"
#define EMOJI_SUCCESS     "✅"

#endif // CONFIG_H
